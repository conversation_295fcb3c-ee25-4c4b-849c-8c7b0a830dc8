#!/usr/bin/env python3
"""
Fix corrupted node embeddings by regenerating them properly
"""

import os
import sys
import pickle
import numpy as np
from pathlib import Path
from datetime import datetime

# Set environment and add path
os.environ["TOKENIZERS_PARALLELISM"] = "false"
sys.path.insert(0, str(Path(__file__).parent))

from setup_embedding_model import setup_embedding_model

def main():
    print("🔧 FIXING CORRUPTED NODE EMBEDDINGS")
    print("="*50)
    
    # Load data
    data_file = Path("import/pdf_dataset/complete_data.pkl")
    with open(data_file, 'rb') as f:
        complete_data = pickle.load(f)
    
    node_list = complete_data.get('node_list', [])
    node_embeddings = complete_data.get('node_embeddings', [])
    
    print(f"📊 Current state:")
    print(f"   • Node list: {len(node_list)} entries")
    print(f"   • Node embeddings: {len(node_embeddings)} embeddings")
    
    # Verify the corruption
    if len(node_embeddings) > 1:
        first_emb = node_embeddings[0]
        second_emb = node_embeddings[1]
        similarity = np.dot(first_emb, second_emb)
        print(f"   • Similarity between first 2 embeddings: {similarity:.6f}")
        
        if similarity > 0.99:
            print("   ❌ CONFIRMED: Node embeddings are corrupted (too similar)")
        else:
            print("   ✅ Node embeddings look normal")
            return
    
    # Setup embedding model
    print(f"\n🔧 Setting up embedding model...")
    embedding_model = setup_embedding_model()
    
    # Create backup
    backup_file = data_file.parent / f"complete_data_backup_node_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
    
    import shutil
    shutil.copy2(data_file, backup_file)
    print(f"📁 Backup created: {backup_file.name}")
    
    # Regenerate node embeddings
    print(f"\n🚀 Regenerating node embeddings...")
    
    if len(node_list) == 0:
        print("❌ No node list found!")
        return
    
    # Sample some nodes to see what we're working with
    print(f"📋 Sample nodes:")
    for i, node in enumerate(node_list[:5], 1):
        print(f"   #{i}: {node[:60]}...")
    
    # Generate embeddings in batches
    batch_size = 64
    all_embeddings = []
    
    print(f"🔄 Processing {len(node_list)} nodes in batches of {batch_size}...")
    
    for i in range(0, len(node_list), batch_size):
        batch_nodes = node_list[i:i+batch_size]
        batch_embeddings = embedding_model.encode(batch_nodes, show_progress_bar=True)
        all_embeddings.append(batch_embeddings)
        
        if i % (batch_size * 10) == 0:
            print(f"   Processed {i + len(batch_nodes)}/{len(node_list)} nodes...")
    
    # Combine all embeddings
    new_node_embeddings = np.vstack(all_embeddings)
    print(f"✅ Generated embeddings shape: {new_node_embeddings.shape}")
    
    # Verify the new embeddings are different
    if len(new_node_embeddings) > 1:
        first_new = new_node_embeddings[0]
        second_new = new_node_embeddings[1]
        new_similarity = np.dot(first_new, second_new)
        print(f"📊 New embeddings check:")
        print(f"   • First vs second similarity: {new_similarity:.6f}")
        
        if new_similarity > 0.99:
            print("   ⚠️  New embeddings still too similar - might be an issue with input data")
        else:
            print("   ✅ New embeddings look properly varied")
    
    # Test with a query
    print(f"\n🧪 Testing new embeddings with sample query...")
    test_query = "Risk Reversal strategy"
    query_emb = embedding_model.encode([test_query])[0]
    
    # Calculate similarities
    raw_scores = new_node_embeddings @ query_emb.T
    unique_scores = len(np.unique(np.round(raw_scores, 6)))
    
    print(f"   • Query: '{test_query}'")
    print(f"   • Raw score range: [{np.min(raw_scores):.6f}, {np.max(raw_scores):.6f}]")
    print(f"   • Unique scores: {unique_scores}")
    
    if unique_scores > 100:  # Should have many different scores
        print("   ✅ New embeddings show proper score variation!")
        
        # Update the data
        complete_data['node_embeddings'] = new_node_embeddings
        
        # Save updated data
        with open(data_file, 'wb') as f:
            pickle.dump(complete_data, f)
        
        print(f"💾 Updated data saved successfully!")
        
        # Show top results
        top_indices = np.argsort(raw_scores)[-5:][::-1]
        print(f"\n🎯 Top 5 nodes for test query:")
        for i, idx in enumerate(top_indices, 1):
            node = node_list[idx]
            score = raw_scores[idx]
            print(f"   #{i}: {node[:50]}... (score: {score:.6f})")
        
    else:
        print("   ❌ New embeddings still don't show proper variation")
        print("   This suggests the node_list might contain duplicate or similar entries")
    
    print(f"\n" + "="*50)
    print("🎯 NODE EMBEDDING FIX SUMMARY:")
    print("✅ Corrupted node embeddings identified and regenerated")
    print("✅ Backup created for safety")
    print("🚀 HippoRAG2 should now show proper score variation!")

if __name__ == "__main__":
    main()