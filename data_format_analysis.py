#!/usr/bin/env python3
"""
Data Format Analysis - Compare Our CSV Outputs with Official AutoSchemaKG Format

This script analyzes our generated CSV files and compares them with the expected format
from the official AutoSchemaKG working examples to identify data ingestion issues.
"""

import pandas as pd
import os
from pathlib import Path
from collections import defaultdict
import json

class DataFormatAnalyzer:
    """Analyzes data format compatibility with official AutoSchemaKG requirements."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.import_dir = self.project_root / "import"
        self.analysis_results = {}
        
        # Expected file patterns from official examples
        self.required_files = {
            "triples_csv": [
                "triple_nodes_*_from_json_without_emb_with_numeric_id.csv",
                "text_nodes_*_from_json_with_numeric_id.csv", 
                "triple_edges_*_from_json_without_emb_with_numeric_id.csv",
                "text_edges_*_from_json.csv"
            ],
            "concept_csv": [
                "concept_nodes_*_from_json_with_concept.csv",
                "concept_edges_*_from_json_with_concept.csv"
            ]
        }
        
        # Expected column schemas
        self.expected_schemas = {
            "triple_nodes": ["name:ID", "type", "concepts", "synsets", "numeric_id", ":LABEL"],
            "text_nodes": ["text_id:ID", "original_text", "numeric_id", ":LABEL"],
            "triple_edges": ["head:START_ID", "relation:TYPE", "tail:END_ID", "numeric_id"],
            "text_edges": ["head:START_ID", "relation:TYPE", "tail:END_ID"],
            "concept_nodes": ["name:ID", "type", "concepts", "synsets", ":LABEL"],
            "concept_edges": ["head:START_ID", "relation:TYPE", "tail:END_ID"]
        }
    
    def analyze_dataset(self, dataset_name):
        """Analyze a specific dataset for format compliance."""
        print(f"\n📊 ANALYZING DATASET: {dataset_name}")
        print("="*60)
        
        dataset_path = self.import_dir / dataset_name
        if not dataset_path.exists():
            print(f"❌ Dataset directory not found: {dataset_path}")
            return None
        
        results = {
            "dataset_name": dataset_name,
            "directory_structure": self.check_directory_structure(dataset_path),
            "file_presence": self.check_required_files(dataset_path),
            "schema_compliance": self.check_schema_compliance(dataset_path),
            "numeric_id_status": self.check_numeric_id_presence(dataset_path),
            "data_quality": self.check_data_quality(dataset_path)
        }
        
        self.analysis_results[dataset_name] = results
        return results
    
    def check_directory_structure(self, dataset_path):
        """Check if directory structure matches official requirements."""
        print(f"\n🏗️  DIRECTORY STRUCTURE ANALYSIS:")
        
        expected_dirs = ["triples_csv", "concept_csv", "kg_extraction", "vector_index"]
        structure_status = {}
        
        for expected_dir in expected_dirs:
            dir_path = dataset_path / expected_dir
            exists = dir_path.exists()
            structure_status[expected_dir] = exists
            status = "✅" if exists else "❌"
            print(f"   {status} {expected_dir}/")
        
        return structure_status
    
    def check_required_files(self, dataset_path):
        """Check if all required CSV files are present."""
        print(f"\n📁 REQUIRED FILES ANALYSIS:")
        
        file_status = {}
        
        for dir_name, file_patterns in self.required_files.items():
            dir_path = dataset_path / dir_name
            file_status[dir_name] = {}
            
            if not dir_path.exists():
                print(f"   ❌ Directory missing: {dir_name}/")
                continue
            
            print(f"\n   📂 {dir_name}/:")
            
            for pattern in file_patterns:
                # Find files matching pattern (simplified pattern matching)
                base_pattern = pattern.replace("*", "").replace("_from_json", "_*_from_json")
                matching_files = []
                
                for file_path in dir_path.glob("*.csv"):
                    if self.matches_pattern(file_path.name, pattern):
                        matching_files.append(file_path.name)
                
                file_status[dir_name][pattern] = {
                    "found": len(matching_files) > 0,
                    "files": matching_files
                }
                
                status = "✅" if matching_files else "❌"
                print(f"      {status} {pattern}")
                if matching_files:
                    for file in matching_files:
                        print(f"         - {file}")
        
        return file_status
    
    def matches_pattern(self, filename, pattern):
        """Simple pattern matching for CSV files."""
        # Remove wildcards and check key components
        pattern_parts = pattern.replace("*", "").split("_")
        return all(part in filename for part in pattern_parts if part)
    
    def check_schema_compliance(self, dataset_path):
        """Check if CSV files have correct column schemas."""
        print(f"\n📋 SCHEMA COMPLIANCE ANALYSIS:")
        
        schema_status = {}
        
        # Check each CSV file type
        for file_type, expected_columns in self.expected_schemas.items():
            csv_files = self.find_csv_files_by_type(dataset_path, file_type)
            schema_status[file_type] = {}
            
            if not csv_files:
                print(f"   ❌ No {file_type} files found")
                schema_status[file_type]["status"] = "missing"
                continue
            
            print(f"\n   📊 {file_type} files:")
            
            for csv_file in csv_files:
                try:
                    df = pd.read_csv(csv_file, nrows=1)  # Read only header
                    actual_columns = list(df.columns)
                    
                    # Check if all expected columns are present
                    missing_columns = set(expected_columns) - set(actual_columns)
                    extra_columns = set(actual_columns) - set(expected_columns)
                    
                    compliance = len(missing_columns) == 0
                    status = "✅" if compliance else "⚠️"
                    
                    print(f"      {status} {csv_file.name}")
                    if missing_columns:
                        print(f"         Missing: {missing_columns}")
                    if extra_columns:
                        print(f"         Extra: {extra_columns}")
                    
                    schema_status[file_type][csv_file.name] = {
                        "compliant": compliance,
                        "actual_columns": actual_columns,
                        "missing_columns": list(missing_columns),
                        "extra_columns": list(extra_columns)
                    }
                    
                except Exception as e:
                    print(f"      ❌ Error reading {csv_file.name}: {e}")
                    schema_status[file_type][csv_file.name] = {"error": str(e)}
        
        return schema_status
    
    def find_csv_files_by_type(self, dataset_path, file_type):
        """Find CSV files of a specific type in the dataset."""
        csv_files = []
        
        # Search in both triples_csv and concept_csv directories
        for subdir in ["triples_csv", "concept_csv"]:
            subdir_path = dataset_path / subdir
            if subdir_path.exists():
                for csv_file in subdir_path.glob("*.csv"):
                    if file_type in csv_file.name:
                        csv_files.append(csv_file)
        
        return csv_files
    
    def check_numeric_id_presence(self, dataset_path):
        """Check if numeric ID columns are present and properly formatted."""
        print(f"\n🔢 NUMERIC ID ANALYSIS:")
        
        numeric_id_status = {}
        
        # Files that should have numeric IDs
        id_required_files = [
            "triple_nodes_*_with_numeric_id.csv",
            "text_nodes_*_with_numeric_id.csv", 
            "triple_edges_*_with_numeric_id.csv"
        ]
        
        triples_dir = dataset_path / "triples_csv"
        if not triples_dir.exists():
            print("   ❌ triples_csv directory missing")
            return {"status": "missing_directory"}
        
        for pattern in id_required_files:
            matching_files = []
            for csv_file in triples_dir.glob("*.csv"):
                if "with_numeric_id" in csv_file.name and any(part in csv_file.name for part in pattern.replace("*", "").split("_") if part):
                    matching_files.append(csv_file)
            
            has_files = len(matching_files) > 0
            status = "✅" if has_files else "❌"
            print(f"   {status} {pattern}")
            
            numeric_id_status[pattern] = {
                "found": has_files,
                "files": [f.name for f in matching_files]
            }
            
            # Check numeric ID column format for found files
            for csv_file in matching_files:
                try:
                    df = pd.read_csv(csv_file, nrows=10)
                    if "numeric_id" in df.columns:
                        id_values = df["numeric_id"].dropna()
                        if len(id_values) > 0:
                            print(f"      - {csv_file.name}: numeric_id range 0-{id_values.max()}")
                        else:
                            print(f"      - {csv_file.name}: numeric_id column empty")
                    else:
                        print(f"      - {csv_file.name}: missing numeric_id column")
                except Exception as e:
                    print(f"      - {csv_file.name}: error reading file - {e}")
        
        return numeric_id_status
    
    def check_data_quality(self, dataset_path):
        """Check data quality metrics."""
        print(f"\n📈 DATA QUALITY ANALYSIS:")
        
        quality_metrics = {}
        
        # Count entities, events, and text passages
        triples_dir = dataset_path / "triples_csv"
        concept_dir = dataset_path / "concept_csv"
        
        if triples_dir.exists():
            # Count nodes
            for csv_file in triples_dir.glob("*nodes*.csv"):
                try:
                    df = pd.read_csv(csv_file)
                    if "type" in df.columns:
                        type_counts = df["type"].value_counts().to_dict()
                        quality_metrics[csv_file.name] = {
                            "total_rows": len(df),
                            "type_distribution": type_counts
                        }
                        print(f"   📊 {csv_file.name}: {len(df):,} rows")
                        for node_type, count in type_counts.items():
                            print(f"      - {node_type}: {count:,}")
                except Exception as e:
                    print(f"   ❌ Error reading {csv_file.name}: {e}")
            
            # Count edges
            for csv_file in triples_dir.glob("*edges*.csv"):
                try:
                    df = pd.read_csv(csv_file)
                    quality_metrics[csv_file.name] = {"total_rows": len(df)}
                    print(f"   📊 {csv_file.name}: {len(df):,} edges")
                except Exception as e:
                    print(f"   ❌ Error reading {csv_file.name}: {e}")
        
        return quality_metrics
    
    def generate_compatibility_report(self):
        """Generate a comprehensive compatibility report."""
        print(f"\n📑 COMPATIBILITY REPORT SUMMARY")
        print("="*80)
        
        for dataset_name, results in self.analysis_results.items():
            print(f"\n🎯 Dataset: {dataset_name}")
            print("-" * 40)
            
            # Directory structure
            structure = results["directory_structure"]
            structure_score = sum(structure.values()) / len(structure) * 100
            print(f"Directory Structure: {structure_score:.0f}% complete")
            
            # Required files
            file_presence = results["file_presence"]
            total_patterns = sum(len(patterns) for patterns in self.required_files.values())
            found_patterns = 0
            for dir_files in file_presence.values():
                for pattern_info in dir_files.values():
                    if pattern_info.get("found", False):
                        found_patterns += 1
            
            file_score = found_patterns / total_patterns * 100 if total_patterns > 0 else 0
            print(f"Required Files: {file_score:.0f}% present")
            
            # Numeric ID status
            numeric_id = results["numeric_id_status"]
            numeric_files = sum(info.get("found", False) for info in numeric_id.values())
            numeric_score = numeric_files / len(self.required_files["triples_csv"]) * 100
            print(f"Numeric ID Files: {numeric_score:.0f}% present")
            
            # Overall compatibility
            overall_score = (structure_score + file_score + numeric_score) / 3
            compatibility_status = "🟢 Good" if overall_score >= 80 else "🟡 Needs Work" if overall_score >= 60 else "🔴 Poor"
            print(f"Overall Compatibility: {overall_score:.0f}% {compatibility_status}")
    
    def save_analysis_report(self, output_file="data_format_analysis_report.json"):
        """Save detailed analysis results to JSON."""
        report_path = self.project_root / output_file
        
        with open(report_path, 'w') as f:
            json.dump(self.analysis_results, f, indent=2, default=str)
        
        print(f"\n💾 Detailed analysis report saved to: {report_path}")
        return report_path

def main():
    """Main analysis function."""
    print("🔍 DATA FORMAT ANALYSIS FOR AutoSchemaKG COMPATIBILITY")
    print("="*80)
    
    analyzer = DataFormatAnalyzer()
    
    # Analyze key datasets
    datasets_to_analyze = [
        "pdf_dataset",  # Most complete dataset
        "360t_guide_direct_api_v2",  # Current working dataset
        "CICGPC_Glazing_ver1.0a"  # Official example format
    ]
    
    for dataset_name in datasets_to_analyze:
        if (analyzer.import_dir / dataset_name).exists():
            analyzer.analyze_dataset(dataset_name)
        else:
            print(f"\n⚠️  Dataset not found: {dataset_name}")
    
    # Generate compatibility report
    analyzer.generate_compatibility_report()
    
    # Save detailed report
    analyzer.save_analysis_report()
    
    print(f"\n🎯 NEXT STEPS RECOMMENDATION:")
    print("-" * 40)
    print("1. Focus on pdf_dataset which has the most complete format")
    print("2. Generate missing numeric_id files for 360t_guide_direct_api_v2")
    print("3. Use create_embeddings_and_index() with properly formatted data")
    print("4. Test HippoRAG2 with officially compatible data structure")

if __name__ == "__main__":
    main()