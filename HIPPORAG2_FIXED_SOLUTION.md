# HippoRAG2 Fixed Solution - Using AutoSchemaKG's Proven Methods

## Problem Summary
The HippoRAG2 retrieval was returning entity names instead of actual passage content, resulting in poor quality answers. After extensive debugging, we discovered we were reinventing AutoSchemaKG functionality instead of using its proven methods.

## Root Cause
1. **Missing Passage Nodes**: GraphML files were missing nodes with `type="passage"` containing actual text
2. **Improper Data Loading**: Not using AutoSchemaKG's `create_embeddings_and_index()` function
3. **Custom Implementation**: Creating unnecessary custom retrievers instead of using standard components

## Solution Implementation

### 1. GraphML Regeneration (`regenerate_graphml_with_passages.py`)
- Uses `csvs_to_graphml()` from AutoSchemaKG to properly create GraphML
- Includes all node types: entities, events, concepts, and **passages**
- Passage nodes contain actual document text in their `id` attribute

### 2. Data Loading (`load_with_autoschemakg.py`)
- Uses `create_embeddings_and_index()` from `atlas_rag.vectorstore.create_graph_index`
- Properly loads GraphML and creates `text_dict` mapping to actual passages
- Builds correct FAISS indices for all components

### 3. Standard Pipeline (`test_standard_hipporag2.py`)
- Uses standard `HippoRAG2Retriever` without custom modifications
- Integrates with Ollama LLM (qwen3:30b-a3b-thinking-2507-q4_K_M)
- Returns actual passage content, not entity names

## Key Learnings

### What Works
```python
# Correct approach using AutoSchemaKG methods
from atlas_rag.vectorstore.create_graph_index import create_embeddings_and_index

data = create_embeddings_and_index(
    sentence_encoder=sentence_encoder,
    model_name="sentence-transformers/all-mpnet-base-v2",
    working_directory="import/360t_guide_direct_api_v2",
    keyword="",
    include_concept=True,
    include_events=True,
    normalize_embeddings=True
)

retriever = HippoRAG2Retriever(
    llm_generator=llm_generator,
    sentence_encoder=sentence_encoder,
    data=data
)
```

### What Doesn't Work
- Creating custom enhanced retrievers
- Loading data manually without `create_embeddings_and_index()`
- GraphML files without passage nodes
- Mixing embedding models (e.g., switching between MiniLM and MPNet)

## Results
✅ Retrieval now returns actual text passages (200-500 words) instead of entity names (2-5 words)
✅ Ollama integration working with proper context
✅ PageRank scores are real values, not fake 5,4,3,2,1
✅ Using proven AutoSchemaKG methods instead of custom implementations

## Running the Solution

1. **Regenerate GraphML with passages**:
```bash
python regenerate_graphml_with_passages.py
```

2. **Test the complete pipeline**:
```bash
# Ensure Ollama is running with the model
ollama run qwen3:30b-a3b-thinking-2507-q4_K_M

# Run the test
python test_standard_hipporag2.py --test
```

3. **Interactive Q&A**:
```bash
python test_standard_hipporag2.py
```

## Files Created
- `regenerate_graphml_with_passages.py` - Regenerates GraphML with passage nodes
- `load_with_autoschemakg.py` - Demonstrates proper data loading
- `test_standard_hipporag2.py` - Complete working pipeline with Ollama

## Important Notes
1. Always use AutoSchemaKG's existing methods - don't reinvent them
2. GraphML must include passage nodes with actual text content
3. Use `create_embeddings_and_index()` for data loading
4. Stick with one embedding model throughout (we use all-mpnet-base-v2)
5. The 360t_guide_direct_api_v2 dataset works correctly with this approach