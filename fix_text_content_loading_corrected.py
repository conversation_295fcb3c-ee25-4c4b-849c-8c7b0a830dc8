#!/usr/bin/env python3
"""
Fix Text Content Loading (Corrected) - Phase 3.1b

This script fixes the text content loading using the correct attribute name.
The investigation revealed text content is stored in 'text' attribute, not 'original_text'.
"""

import os
import sys
import json
import networkx as nx
from pathlib import Path
from datetime import datetime

class TextContentLoaderCorrected:
    """Fixes text content loading using correct GraphML attribute."""
    
    def __init__(self, data_directory="import/pdf_dataset"):
        self.data_directory = Path(data_directory)
        self.vector_dir = self.data_directory / "vector_index"
        
        print("🔧 TEXT CONTENT LOADING FIX (CORRECTED) - Phase 3.1b")
        print("="*60)
        print(f"Using correct 'text' attribute from GraphML nodes")
        print(f"Data Directory: {self.data_directory}")
        print()
    
    def extract_text_content_from_graphml(self):
        """Extract text content from GraphML using correct attribute."""
        print("📊 Extracting Text Content from GraphML...")
        
        # Load knowledge graph
        graphml_path = self.data_directory / "kg_graphml" / "knowledge_graph.graphml"
        self.KG = nx.read_graphml(str(graphml_path))
        
        # Extract text content using 'text' attribute (not 'original_text')
        text_content_dict = {}
        text_nodes_with_content = 0
        text_nodes_without_content = 0
        total_text_length = 0
        
        for node_id, node_data in self.KG.nodes(data=True):
            if node_data.get('type') == 'text':
                # Use 'text' attribute instead of 'original_text'
                text_content = node_data.get('text', '')
                
                if text_content and len(text_content.strip()) > 0:
                    text_content_dict[node_id] = text_content.strip()
                    text_nodes_with_content += 1
                    total_text_length += len(text_content)
                else:
                    text_nodes_without_content += 1
        
        print(f"   📊 Text Content Extraction Results:")
        print(f"      Text nodes with content: {text_nodes_with_content:,}")
        print(f"      Text nodes without content: {text_nodes_without_content:,}")
        print(f"      Total text length: {total_text_length:,} characters")
        
        if text_nodes_with_content > 0:
            avg_length = total_text_length / text_nodes_with_content
            print(f"      Average text length: {avg_length:.0f} chars per node")
            
            # Show sample content
            sample_entries = list(text_content_dict.items())[:3]
            print(f"   📝 Sample text content:")
            for node_id, content in sample_entries:
                # Clean up HTML-like content for preview
                clean_content = content.replace('<span id="page-0-0"></span>', '').replace('![](_page_0_Picture_0.jpeg)', '').strip()
                preview = clean_content[:100] + "..." if len(clean_content) > 100 else clean_content
                print(f"      {node_id[:12]}...: {preview}")
            
            self.text_content_dict = text_content_dict
            return True
        else:
            print(f"   ❌ No text content found even with 'text' attribute")
            return False
    
    def create_text_dictionary(self):
        """Create the text dictionary JSON file."""
        print("\n💾 Creating Text Dictionary...")
        
        if not hasattr(self, 'text_content_dict'):
            print("❌ No text content to create dictionary")
            return False
        
        try:
            # Save as JSON
            output_path = self.vector_dir / "text_dict.json"
            
            # Backup existing if present
            if output_path.exists():
                backup_path = self.vector_dir / "text_dict_backup.json"
                os.rename(output_path, backup_path)
                print(f"   ✅ Existing dictionary backed up: {backup_path}")
            
            # Save new dictionary
            with open(output_path, 'w') as f:
                json.dump(self.text_content_dict, f, indent=2)
            
            file_size_mb = output_path.stat().st_size / (1024*1024)
            print(f"   ✅ Text dictionary created: {output_path}")
            print(f"   📊 File size: {file_size_mb:.1f} MB")
            print(f"   📊 Entries: {len(self.text_content_dict):,}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error creating text dictionary: {e}")
            return False
    
    def test_text_access(self):
        """Test that text content can be accessed properly."""
        print("\n🧪 Testing Text Content Access...")
        
        try:
            # Load the text dictionary we just created
            text_dict_path = self.vector_dir / "text_dict.json"
            
            with open(text_dict_path, 'r') as f:
                text_dict = json.load(f)
            
            print(f"   ✅ Text dictionary loaded: {len(text_dict):,} entries")
            
            # Test access to a few entries
            if len(text_dict) > 0:
                import random
                sample_size = min(3, len(text_dict))
                sample_keys = random.sample(list(text_dict.keys()), sample_size)
                
                print(f"   🧪 Testing {sample_size} random entries:")
                
                accessible_count = 0
                for key in sample_keys:
                    content = text_dict.get(key, '')
                    if content and len(content.strip()) > 10:  # At least 10 chars
                        accessible_count += 1
                        # Clean up content for display
                        clean_content = content.replace('<span id="page-0-0"></span>', '').replace('![](_page_0_Picture_0.jpeg)', '').strip()
                        preview = clean_content[:80] + "..." if len(clean_content) > 80 else clean_content
                        print(f"      ✅ {key[:12]}...: {preview}")
                    else:
                        print(f"      ❌ {key[:12]}...: Empty or too short")
                
                success_rate = (accessible_count / sample_size) * 100
                print(f"   📊 Content accessibility: {accessible_count}/{sample_size} ({success_rate:.1f}%)")
                
                if success_rate >= 80:
                    print(f"   🎉 SUCCESS: Text content access is working!")
                    return True
                else:
                    print(f"   ⚠️  Content access issues detected")
                    return False
            else:
                print(f"   ❌ Empty text dictionary")
                return False
                
        except Exception as e:
            print(f"❌ Error testing text access: {e}")
            return False
    
    def run_corrected_fix(self):
        """Run the corrected text content loading fix."""
        print("🚀 Starting Corrected Text Content Loading Fix...")
        print("   Using 'text' attribute instead of 'original_text'")
        print()
        
        steps = [
            ("Extract Text from GraphML", self.extract_text_content_from_graphml),
            ("Create Text Dictionary", self.create_text_dictionary),
            ("Test Text Access", self.test_text_access)
        ]
        
        for step_name, step_func in steps:
            print(f"📋 {step_name}...")
            if not step_func():
                print(f"❌ {step_name} failed. Stopping fix.")
                return False
        
        print("\n✅ CORRECTED TEXT CONTENT LOADING FIX COMPLETE")
        print("="*60)
        print("🎯 RESULTS:")
        print(f"   • Text content successfully extracted from GraphML")
        print(f"   • Text dictionary created and accessible")
        print(f"   • Retrieval systems can now access text passages")
        print(f"   🎉 Combined with 100% embeddings: Complete working pipeline!")
        
        print(f"\n📋 Next: Re-test HippoRAG2 with both fixes applied")
        
        return True

def main():
    """Main corrected fix function."""
    loader = TextContentLoaderCorrected()
    return loader.run_corrected_fix()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)