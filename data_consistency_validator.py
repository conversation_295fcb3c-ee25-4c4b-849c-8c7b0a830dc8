#!/usr/bin/env python3
"""
Data Consistency Validator for AutoSchemaKG

Prevents the embedding/GraphML size mismatch issues that caused HippoRAG2 indexing errors.
Validates that all pipeline components (CSV, GraphML, embeddings, FAISS indexes) are consistent.
"""

import os
import csv
import json
import pickle
import numpy as np
import networkx as nx
import faiss
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataConsistencyValidator:
    """
    Validates consistency between different pipeline components:
    - CSV files (nodes, edges, text)
    - GraphML files 
    - Embedding arrays (.npy)
    - FAISS indexes
    """
    
    def __init__(self, data_directory: str = "import/pdf_dataset"):
        self.data_directory = Path(data_directory)
        self.validation_results = {}
        
    def validate_file_timestamps(self) -> Dict[str, any]:
        """Check if all files were created around the same time."""
        logger.info("🕒 Validating file timestamps...")
        
        # Key files to check
        files_to_check = [
            self.data_directory / "kg_graphml" / "knowledge_graph.graphml",
            self.data_directory / "triples_csv" / "triple_nodes__from_json_without_emb.csv",
            self.data_directory / "triples_csv" / "triple_edges__from_json_without_emb.csv",
            self.data_directory / "vector_index" / "triple_nodes__from_json_with_emb.npy",
            self.data_directory / "vector_index" / "triple_edges__from_json_with_concept_with_emb.npy"
        ]
        
        timestamps = {}
        for file_path in files_to_check:
            if file_path.exists():
                stat = file_path.stat()
                timestamps[file_path.name] = {
                    'modified': datetime.fromtimestamp(stat.st_mtime),
                    'created': datetime.fromtimestamp(stat.st_ctime),
                    'size': stat.st_size
                }
            else:
                timestamps[file_path.name] = None
        
        # Check for suspicious time gaps (> 24 hours)
        modification_times = [t['modified'] for t in timestamps.values() if t is not None]
        if len(modification_times) > 1:
            time_span = max(modification_times) - min(modification_times)
            suspicious_gap = time_span.total_seconds() > 24 * 3600  # 24 hours
        else:
            suspicious_gap = False
        
        result = {
            'timestamps': timestamps,
            'suspicious_gap': suspicious_gap,
            'time_span_hours': time_span.total_seconds() / 3600 if len(modification_times) > 1 else 0,
            'status': 'WARNING' if suspicious_gap else 'OK'
        }
        
        self.validation_results['timestamps'] = result
        
        if suspicious_gap:
            logger.warning(f"⚠️  Suspicious time gap detected: {result['time_span_hours']:.1f} hours")
        else:
            logger.info(f"✅ File timestamps are consistent (span: {result['time_span_hours']:.1f} hours)")
        
        return result
    
    def validate_dimensions(self) -> Dict[str, any]:
        """Validate that dimensions match between components."""
        logger.info("📏 Validating data dimensions...")
        
        dimensions = {}
        
        # Count CSV rows
        csv_files = {
            'nodes': self.data_directory / "triples_csv" / "triple_nodes__from_json_without_emb.csv",
            'edges': self.data_directory / "triples_csv" / "triple_edges__from_json_without_emb.csv",
            'text': self.data_directory / "triples_csv" / "text_nodes__from_json.csv"
        }
        
        for key, file_path in csv_files.items():
            if file_path.exists():
                with open(file_path, 'r') as f:
                    row_count = sum(1 for _ in f) - 1  # Subtract header
                dimensions[f'csv_{key}'] = row_count
            else:
                dimensions[f'csv_{key}'] = None
        
        # Count GraphML nodes/edges
        graphml_path = self.data_directory / "kg_graphml" / "knowledge_graph.graphml"
        if graphml_path.exists():
            try:
                graph = nx.read_graphml(str(graphml_path))
                dimensions['graphml_nodes'] = len(graph.nodes)
                dimensions['graphml_edges'] = len(graph.edges)
            except Exception as e:
                logger.warning(f"Could not read GraphML: {e}")
                dimensions['graphml_nodes'] = None
                dimensions['graphml_edges'] = None
        else:
            dimensions['graphml_nodes'] = None
            dimensions['graphml_edges'] = None
        
        # Count embedding arrays
        embedding_files = {
            'node_embeddings': self.data_directory / "vector_index" / "triple_nodes__from_json_with_emb.npy",
            'edge_embeddings': self.data_directory / "vector_index" / "triple_edges__from_json_with_concept_with_emb.npy",
            'text_embeddings': self.data_directory / "vector_index" / "text_nodes__from_json_with_emb.npy"
        }
        
        for key, file_path in embedding_files.items():
            if file_path.exists():
                try:
                    arr = np.load(str(file_path))
                    dimensions[key] = arr.shape[0]
                    dimensions[f'{key}_dim'] = arr.shape[1] if len(arr.shape) > 1 else None
                except Exception as e:
                    logger.warning(f"Could not load {file_path}: {e}")
                    dimensions[key] = None
            else:
                dimensions[key] = None
        
        # Check for mismatches
        mismatches = []
        
        # Node consistency checks
        if dimensions['csv_nodes'] and dimensions['node_embeddings']:
            if dimensions['csv_nodes'] != dimensions['node_embeddings']:
                mismatches.append(f"Nodes: CSV {dimensions['csv_nodes']} ≠ Embeddings {dimensions['node_embeddings']}")
        
        # Edge consistency checks (embeddings may be truncated)
        if dimensions['csv_edges'] and dimensions['edge_embeddings']:
            if dimensions['csv_edges'] < dimensions['edge_embeddings']:
                mismatches.append(f"Edges: CSV {dimensions['csv_edges']} < Embeddings {dimensions['edge_embeddings']}")
        
        # GraphML vs CSV checks
        if dimensions['graphml_nodes'] and dimensions['csv_nodes']:
            if abs(dimensions['graphml_nodes'] - dimensions['csv_nodes']) > 1000:  # Allow small variance
                mismatches.append(f"GraphML nodes {dimensions['graphml_nodes']} ≠ CSV nodes {dimensions['csv_nodes']}")
        
        result = {
            'dimensions': dimensions,
            'mismatches': mismatches,
            'status': 'ERROR' if mismatches else 'OK'
        }
        
        self.validation_results['dimensions'] = result
        
        if mismatches:
            logger.error(f"❌ Dimension mismatches detected:")
            for mismatch in mismatches:
                logger.error(f"   - {mismatch}")
        else:
            logger.info("✅ All dimensions are consistent")
        
        return result
    
    def validate_embedding_integrity(self) -> Dict[str, any]:
        """Check for corrupt or invalid embeddings."""
        logger.info("🔍 Validating embedding integrity...")
        
        embedding_files = {
            'node_embeddings': self.data_directory / "vector_index" / "triple_nodes__from_json_with_emb.npy",
            'edge_embeddings': self.data_directory / "vector_index" / "triple_edges__from_json_with_concept_with_emb.npy",
            'text_embeddings': self.data_directory / "vector_index" / "text_nodes__from_json_with_emb.npy"
        }
        
        integrity_results = {}
        issues = []
        
        for key, file_path in embedding_files.items():
            if not file_path.exists():
                integrity_results[key] = {'status': 'MISSING'}
                issues.append(f"{key}: File missing")
                continue
                
            try:
                arr = np.load(str(file_path))
                
                # Check for common issues
                has_nan = np.isnan(arr).any()
                has_inf = np.isinf(arr).any()
                all_zeros = (arr == 0).all()
                has_extreme_values = (np.abs(arr) > 1000).any()
                
                integrity_results[key] = {
                    'status': 'OK',
                    'shape': arr.shape,
                    'dtype': str(arr.dtype),
                    'has_nan': bool(has_nan),
                    'has_inf': bool(has_inf),
                    'all_zeros': bool(all_zeros),
                    'has_extreme_values': bool(has_extreme_values),
                    'mean': float(np.mean(arr)),
                    'std': float(np.std(arr))
                }
                
                if has_nan:
                    issues.append(f"{key}: Contains NaN values")
                if has_inf:
                    issues.append(f"{key}: Contains infinite values")
                if all_zeros:
                    issues.append(f"{key}: All values are zero")
                if has_extreme_values:
                    issues.append(f"{key}: Contains extreme values (>1000)")
                    
            except Exception as e:
                integrity_results[key] = {'status': 'ERROR', 'error': str(e)}
                issues.append(f"{key}: {e}")
        
        result = {
            'integrity_results': integrity_results,
            'issues': issues,
            'status': 'ERROR' if issues else 'OK'
        }
        
        self.validation_results['integrity'] = result
        
        if issues:
            logger.warning(f"⚠️  Embedding integrity issues:")
            for issue in issues:
                logger.warning(f"   - {issue}")
        else:
            logger.info("✅ All embeddings have good integrity")
        
        return result
    
    def validate_faiss_indexes(self) -> Dict[str, any]:
        """Validate FAISS indexes match embedding dimensions."""
        logger.info("🔗 Validating FAISS indexes...")
        
        index_files = [
            self.data_directory / "vector_index" / "triple_nodes__from_json_with_emb_non_norm.index",
            self.data_directory / "vector_index" / "triple_edges__from_json_with_concept_with_emb_non_norm.index",
            self.data_directory / "vector_index" / "text_nodes__from_json_with_emb_non_norm.index"
        ]
        
        index_results = {}
        issues = []
        
        for index_file in index_files:
            key = index_file.stem.replace('_non_norm', '')
            
            if not index_file.exists():
                index_results[key] = {'status': 'MISSING'}
                continue
                
            try:
                index = faiss.read_index(str(index_file))
                
                # Get corresponding embedding file
                emb_file = index_file.parent / f"{key}.npy"
                if emb_file.exists():
                    emb_arr = np.load(str(emb_file))
                    
                    # Check dimensions match
                    if index.ntotal != emb_arr.shape[0]:
                        issues.append(f"{key}: Index size {index.ntotal} ≠ Embedding size {emb_arr.shape[0]}")
                    
                    if index.d != emb_arr.shape[1]:
                        issues.append(f"{key}: Index dim {index.d} ≠ Embedding dim {emb_arr.shape[1]}")
                
                index_results[key] = {
                    'status': 'OK',
                    'ntotal': index.ntotal,
                    'dimension': index.d,
                    'metric_type': index.metric_type
                }
                
            except Exception as e:
                index_results[key] = {'status': 'ERROR', 'error': str(e)}
                issues.append(f"{key}: {e}")
        
        result = {
            'index_results': index_results,
            'issues': issues,
            'status': 'ERROR' if issues else 'OK'
        }
        
        self.validation_results['faiss'] = result
        
        if issues:
            logger.error(f"❌ FAISS index issues:")
            for issue in issues:
                logger.error(f"   - {issue}")
        else:
            logger.info("✅ All FAISS indexes are consistent")
        
        return result
    
    def generate_report(self) -> Dict[str, any]:
        """Generate comprehensive validation report."""
        logger.info("📋 Generating validation report...")
        
        # Run all validations
        timestamp_result = self.validate_file_timestamps()
        dimension_result = self.validate_dimensions()
        integrity_result = self.validate_embedding_integrity()
        faiss_result = self.validate_faiss_indexes()
        
        # Determine overall status
        all_results = [timestamp_result, dimension_result, integrity_result, faiss_result]
        has_errors = any(r['status'] == 'ERROR' for r in all_results)
        has_warnings = any(r['status'] == 'WARNING' for r in all_results)
        
        if has_errors:
            overall_status = 'ERROR'
        elif has_warnings:
            overall_status = 'WARNING'
        else:
            overall_status = 'OK'
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'data_directory': str(self.data_directory),
            'overall_status': overall_status,
            'results': {
                'timestamps': timestamp_result,
                'dimensions': dimension_result,
                'integrity': integrity_result,
                'faiss': faiss_result
            },
            'summary': {
                'total_checks': len(all_results),
                'passed': sum(1 for r in all_results if r['status'] == 'OK'),
                'warnings': sum(1 for r in all_results if r['status'] == 'WARNING'),
                'errors': sum(1 for r in all_results if r['status'] == 'ERROR')
            }
        }
        
        # Save report
        report_file = self.data_directory / "data_consistency_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"📄 Report saved to: {report_file}")
        
        # Print summary
        print(f"\\n{'='*50}")
        print(f"📊 DATA CONSISTENCY VALIDATION REPORT")
        print(f"{'='*50}")
        print(f"Overall Status: {overall_status}")
        print(f"Checks Passed: {report['summary']['passed']}/{report['summary']['total_checks']}")
        if report['summary']['warnings'] > 0:
            print(f"Warnings: {report['summary']['warnings']}")
        if report['summary']['errors'] > 0:
            print(f"Errors: {report['summary']['errors']}")
        print(f"{'='*50}")
        
        return report

def main():
    """Main function to run validation."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Data Consistency Validator for AutoSchemaKG")
    parser.add_argument(
        "--data-dir", 
        default="import/pdf_dataset",
        help="Directory containing pipeline data (default: import/pdf_dataset)"
    )
    parser.add_argument(
        "--fix", 
        action="store_true",
        help="Attempt to fix issues automatically (experimental)"
    )
    
    args = parser.parse_args()
    
    try:
        validator = DataConsistencyValidator(args.data_dir)
        report = validator.generate_report()
        
        if report['overall_status'] == 'OK':
            print("🎉 All validation checks passed!")
            return True
        elif report['overall_status'] == 'WARNING':
            print("⚠️  Validation completed with warnings.")
            return True
        else:
            print("❌ Validation failed with errors.")
            print("💡 Consider regenerating inconsistent components.")
            return False
            
    except Exception as e:
        logger.error(f"❌ Validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)