#!/usr/bin/env python3
"""
Embedding Coverage Audit - Phase 1.1

This script performs a comprehensive audit of embedding coverage to understand
why 49,008 text edges lack embeddings and why there's a node count mismatch.

Original Issues to Investigate:
- 49,008 text edges excluded without embeddings
- Node count mismatch: 63,364 graph nodes vs 62,157 embeddings
- Poor retrieval results across query types

This addresses the ROOT CAUSE instead of symptoms.
"""

import os
import sys
import json
import pickle
import numpy as np
import networkx as nx
import pandas as pd
from pathlib import Path
from datetime import datetime
from collections import defaultdict, Counter

# Set environment variable to avoid tokenizer warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

class EmbeddingCoverageAuditor:
    """Comprehensive auditor for embedding coverage issues."""
    
    def __init__(self, data_directory="import/pdf_dataset"):
        self.data_directory = Path(data_directory)
        self.report = {
            'timestamp': datetime.now().isoformat(),
            'data_directory': str(self.data_directory),
            'findings': {},
            'root_causes': [],
            'recommendations': []
        }
        
        print("🔍 EMBEDDING COVERAGE AUDIT - Phase 1.1")
        print("="*60)
        print(f"Investigating root causes of embedding discrepancies")
        print(f"Data Directory: {self.data_directory}")
        print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
    
    def load_knowledge_graph(self):
        """Load the knowledge graph structure."""
        print("📊 Loading Knowledge Graph Structure...")
        
        graphml_path = self.data_directory / "kg_graphml" / "knowledge_graph.graphml"
        if not graphml_path.exists():
            print(f"❌ GraphML file not found: {graphml_path}")
            return False
        
        try:
            self.KG = nx.read_graphml(str(graphml_path))
            
            # Analyze graph structure
            total_nodes = len(self.KG.nodes)
            total_edges = len(self.KG.edges)
            
            # Categorize nodes by type
            node_types = defaultdict(int)
            for node_id, node_data in self.KG.nodes(data=True):
                node_type = node_data.get('type', 'unknown')
                node_types[node_type] += 1
            
            # Categorize edges by type  
            edge_types = defaultdict(int)
            text_edges = 0
            entity_text_edges = 0
            
            for src, dst, edge_data in self.KG.edges(data=True):
                src_type = self.KG.nodes[src].get('type', 'unknown')
                dst_type = self.KG.nodes[dst].get('type', 'unknown')
                
                edge_key = f"{src_type} -> {dst_type}"
                edge_types[edge_key] += 1
                
                # Count text-related edges
                if src_type == 'text' or dst_type == 'text':
                    text_edges += 1
                    
                if (src_type != 'text' and dst_type == 'text') or \
                   (src_type == 'text' and dst_type != 'text'):
                    entity_text_edges += 1
            
            self.report['findings']['graph_structure'] = {
                'total_nodes': total_nodes,
                'total_edges': total_edges,
                'node_types': dict(node_types),
                'edge_types': dict(edge_types),
                'text_edges': text_edges,
                'entity_text_edges': entity_text_edges
            }
            
            print(f"   ✅ Graph loaded: {total_nodes:,} nodes, {total_edges:,} edges")
            print(f"   📊 Node types: {dict(node_types)}")
            print(f"   📊 Text-related edges: {text_edges:,}")
            print(f"   📊 Entity-text connections: {entity_text_edges:,}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading GraphML: {e}")
            return False
    
    def analyze_embedding_files(self):
        """Analyze existing embedding files and their coverage."""
        print("\n🔍 Analyzing Embedding File Coverage...")
        
        vector_dir = self.data_directory / "vector_index"
        if not vector_dir.exists():
            print(f"❌ Vector index directory not found: {vector_dir}")
            return False
        
        embedding_files = {
            'node_embeddings': 'triple_nodes__from_json_with_emb.npy',
            'edge_embeddings': 'triple_edges__from_json_with_concept_with_emb.npy', 
            'text_embeddings': 'text_nodes__from_json_with_emb.npy'
        }
        
        embedding_coverage = {}
        
        for emb_type, filename in embedding_files.items():
            emb_path = vector_dir / filename
            
            if emb_path.exists():
                try:
                    embeddings = np.load(emb_path)
                    embedding_coverage[emb_type] = {
                        'file_exists': True,
                        'shape': embeddings.shape,
                        'count': embeddings.shape[0],
                        'dimensions': embeddings.shape[1] if len(embeddings.shape) > 1 else 0,
                        'file_size_mb': emb_path.stat().st_size / (1024*1024)
                    }
                    print(f"   ✅ {emb_type}: {embeddings.shape} ({emb_path.stat().st_size/(1024*1024):.1f} MB)")
                except Exception as e:
                    embedding_coverage[emb_type] = {
                        'file_exists': True,
                        'error': str(e)
                    }
                    print(f"   ❌ {emb_type}: Error loading - {e}")
            else:
                embedding_coverage[emb_type] = {'file_exists': False}
                print(f"   ❌ {emb_type}: File not found - {filename}")
        
        self.report['findings']['embedding_coverage'] = embedding_coverage
        return True
    
    def analyze_coverage_gaps(self):
        """Identify specific gaps in embedding coverage."""
        print("\n🎯 Analyzing Coverage Gaps (THE CORE ISSUE)...")
        
        graph_data = self.report['findings']['graph_structure']
        embedding_data = self.report['findings']['embedding_coverage']
        
        # Node coverage analysis
        total_nodes = graph_data['total_nodes']
        node_embeddings_count = embedding_data.get('node_embeddings', {}).get('count', 0)  
        node_gap = total_nodes - node_embeddings_count
        
        print(f"   📊 Node Coverage Analysis:")
        print(f"      Total graph nodes: {total_nodes:,}")
        print(f"      Node embeddings: {node_embeddings_count:,}")
        print(f"      Missing node embeddings: {node_gap:,} ({node_gap/total_nodes*100:.1f}%)")
        
        # Edge coverage analysis
        total_edges = graph_data['total_edges'] 
        edge_embeddings_count = embedding_data.get('edge_embeddings', {}).get('count', 0)
        edge_gap = total_edges - edge_embeddings_count
        
        print(f"   📊 Edge Coverage Analysis:")
        print(f"      Total graph edges: {total_edges:,}")
        print(f"      Edge embeddings: {edge_embeddings_count:,}")
        print(f"      Missing edge embeddings: {edge_gap:,} ({edge_gap/total_edges*100:.1f}%)")
        
        # Text edge analysis (THE ORIGINAL 49K ISSUE)
        text_edges = graph_data['text_edges']
        entity_text_edges = graph_data['entity_text_edges']
        
        print(f"   🎯 Text Edge Analysis (ORIGINAL ISSUE):")
        print(f"      Text-related edges: {text_edges:,}")
        print(f"      Entity-text connections: {entity_text_edges:,}")
        print(f"      Potential missing text edges: ~{edge_gap:,}")
        
        # Root cause identification
        coverage_issues = {
            'node_coverage_gap': {
                'missing_count': node_gap,
                'missing_percentage': node_gap/total_nodes*100 if total_nodes > 0 else 0
            },
            'edge_coverage_gap': {
                'missing_count': edge_gap,
                'missing_percentage': edge_gap/total_edges*100 if total_edges > 0 else 0
            },
            'text_edge_analysis': {
                'total_text_edges': text_edges,
                'entity_text_edges': entity_text_edges,
                'likely_missing_text_edges': max(0, edge_gap)
            }
        }
        
        self.report['findings']['coverage_gaps'] = coverage_issues
        
        # Identify if this matches the original 49K issue
        if edge_gap > 40000:
            print(f"   🚨 CONFIRMED: This matches the original '49,008 text edges' issue!")
            self.report['root_causes'].append(
                f"Confirmed embedding gap of {edge_gap:,} edges matches original issue"
            )
        
        return True
    
    def investigate_embedding_generation_process(self):
        """Investigate the embedding generation process to find root causes."""
        print("\n🔬 Investigating Embedding Generation Process...")
        
        # Look for CSV source files
        csv_files = {
            'triple_nodes': 'triple_nodes__from_json_with_concept.csv',
            'triple_edges': 'triple_edges__from_json_with_concept.csv',
            'text_nodes': 'text_nodes__from_json.csv',
            'text_edges': 'text_edges__from_json.csv'
        }
        
        csv_analysis = {}
        
        for csv_type, filename in csv_files.items():
            csv_path = self.data_directory / filename
            
            if csv_path.exists():
                try:
                    df = pd.read_csv(csv_path)
                    csv_analysis[csv_type] = {
                        'exists': True,
                        'row_count': len(df),
                        'columns': list(df.columns),
                        'file_size_mb': csv_path.stat().st_size / (1024*1024)
                    }
                    print(f"   ✅ {csv_type}: {len(df):,} rows ({csv_path.stat().st_size/(1024*1024):.1f} MB)")
                except Exception as e:
                    csv_analysis[csv_type] = {'exists': True, 'error': str(e)}
                    print(f"   ❌ {csv_type}: Error - {e}")
            else:
                csv_analysis[csv_type] = {'exists': False}
                print(f"   ⚠️  {csv_type}: File not found")
        
        self.report['findings']['csv_source_analysis'] = csv_analysis
        
        # Calculate expected vs actual embeddings
        expected_edges = 0
        if csv_analysis.get('triple_edges', {}).get('exists'):
            expected_edges += csv_analysis['triple_edges'].get('row_count', 0)
        if csv_analysis.get('text_edges', {}).get('exists'):  
            expected_edges += csv_analysis['text_edges'].get('row_count', 0)
        
        actual_edge_embeddings = self.report['findings']['embedding_coverage'].get(
            'edge_embeddings', {}
        ).get('count', 0)
        
        embedding_generation_gap = expected_edges - actual_edge_embeddings
        
        print(f"   📊 Embedding Generation Analysis:")
        print(f"      Expected edge embeddings: {expected_edges:,}")
        print(f"      Actual edge embeddings: {actual_edge_embeddings:,}")
        print(f"      Generation gap: {embedding_generation_gap:,}")
        
        if embedding_generation_gap > 0:
            print(f"   🚨 ROOT CAUSE IDENTIFIED: Embedding generation is incomplete!")
            self.report['root_causes'].append(
                f"Embedding generation gap: {embedding_generation_gap:,} edges not processed"
            )
        
        return True
    
    def identify_root_causes_and_recommendations(self):
        """Synthesize findings into root causes and actionable recommendations."""
        print("\n💡 Root Cause Analysis & Recommendations...")
        
        # Analyze all findings to identify root causes
        findings = self.report['findings']
        
        # Root cause 1: Incomplete embedding generation
        coverage_gaps = findings.get('coverage_gaps', {})
        edge_gap = coverage_gaps.get('edge_coverage_gap', {}).get('missing_count', 0)
        node_gap = coverage_gaps.get('node_coverage_gap', {}).get('missing_count', 0)
        
        if edge_gap > 0 or node_gap > 0:
            self.report['root_causes'].append(
                f"Incomplete embedding generation: {edge_gap:,} edges, {node_gap:,} nodes missing"
            )
        
        # Root cause 2: Text edge embedding failures  
        text_edge_analysis = coverage_gaps.get('text_edge_analysis', {})
        likely_missing_text_edges = text_edge_analysis.get('likely_missing_text_edges', 0)
        
        if likely_missing_text_edges > 40000:
            self.report['root_causes'].append(
                f"Text edge embedding failures: ~{likely_missing_text_edges:,} text edges lack embeddings"
            )
        
        # Recommendations based on root causes
        recommendations = [
            "Fix embedding generation pipeline to achieve 100% coverage",
            "Investigate memory/batch size constraints in embedding processing", 
            "Implement proper error handling and retry mechanisms",
            "Add progress tracking and resumption for embedding generation",
            "Validate embedding completeness before using in retrieval",
            "Make HippoRAG2 robust to missing embeddings with fallback strategies"
        ]
        
        self.report['recommendations'] = recommendations
        
        print("   🎯 ROOT CAUSES IDENTIFIED:")
        for i, cause in enumerate(self.report['root_causes'], 1):
            print(f"      {i}. {cause}")
        
        print("   💡 RECOMMENDATIONS:")
        for i, rec in enumerate(recommendations, 1):
            print(f"      {i}. {rec}")
        
        return True
    
    def save_audit_report(self):
        """Save comprehensive audit report."""
        report_path = self.data_directory.parent / "embedding_coverage_audit_report.json"
        
        try:
            with open(report_path, 'w') as f:
                json.dump(self.report, f, indent=2, default=str)
            
            print(f"\n📄 Audit Report Saved: {report_path}")
            return True
            
        except Exception as e:
            print(f"❌ Error saving report: {e}")
            return False
    
    def run_complete_audit(self):
        """Run the complete embedding coverage audit."""
        print("🚀 Starting Complete Embedding Coverage Audit...")
        print("   This investigates the ROOT CAUSE of retrieval issues")
        print()
        
        steps = [
            ("Load Knowledge Graph", self.load_knowledge_graph),
            ("Analyze Embedding Files", self.analyze_embedding_files), 
            ("Analyze Coverage Gaps", self.analyze_coverage_gaps),
            ("Investigate Generation Process", self.investigate_embedding_generation_process),
            ("Identify Root Causes", self.identify_root_causes_and_recommendations),
            ("Save Audit Report", self.save_audit_report)
        ]
        
        for step_name, step_func in steps:
            print(f"📋 {step_name}...")
            if not step_func():
                print(f"❌ {step_name} failed. Stopping audit.")
                return False
        
        print("\n✅ EMBEDDING COVERAGE AUDIT COMPLETE")
        print("="*60)
        print("🎯 KEY FINDINGS:")
        
        # Summary of key findings
        coverage_gaps = self.report['findings'].get('coverage_gaps', {})
        edge_gap = coverage_gaps.get('edge_coverage_gap', {}).get('missing_count', 0)
        node_gap = coverage_gaps.get('node_coverage_gap', {}).get('missing_count', 0)
        
        print(f"   • Missing edge embeddings: {edge_gap:,}")
        print(f"   • Missing node embeddings: {node_gap:,}")
        print(f"   • Root causes identified: {len(self.report['root_causes'])}")
        print(f"   • Recommendations provided: {len(self.report['recommendations'])}")
        
        if edge_gap > 40000:
            print(f"   🚨 CONFIRMED: Original '49K text edges' issue reproduced!")
        
        print(f"\n📄 Full report: embedding_coverage_audit_report.json")
        print(f"📋 Next: Phase 1.2 - HippoRAG2 Algorithm Analysis")
        
        return True

def main():
    """Main audit function."""
    auditor = EmbeddingCoverageAuditor()
    return auditor.run_complete_audit()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)