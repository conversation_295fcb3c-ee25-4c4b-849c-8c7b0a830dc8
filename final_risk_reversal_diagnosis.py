#!/usr/bin/env python3
"""
Final diagnosis: Check if Risk Reversal text nodes exist in the knowledge graph.
"""

import os
import sys
import pickle
from pathlib import Path

# Set environment and add path
os.environ["TOKENIZERS_PARALLELISM"] = "false"
sys.path.insert(0, str(Path(__file__).parent))

def main():
    print("🎯 FINAL DIAGNOSIS: Risk Reversal in Knowledge Graph")
    print("="*60)
    
    # Load data
    print("📊 Loading complete data...")
    data_file = Path("import/pdf_dataset/complete_data.pkl")
    with open(data_file, 'rb') as f:
        complete_data = pickle.load(f)
    
    # Get key components
    kg = complete_data.get('KG')
    text_dict = complete_data.get('text_dict', {})
    
    # Risk Reversal text IDs
    risk_reversal_ids = [
        "cdcd632ca440b3caf201d917d6a8ac2f109905b716b570b0eb557b9e753591ff",  # Table of contents
        "4eb22e1fee9bad1777d1e0d7493e5ae03450eb9402a4a3f08e638051a9dc2b87"   # Figure references
    ]
    
    print(f"📊 Data Overview:")
    print(f"   • Knowledge Graph nodes: {len(kg.nodes) if kg else 'No KG'}")
    print(f"   • Knowledge Graph edges: {len(kg.edges) if kg else 'No KG'}")
    print(f"   • Text dict entries: {len(text_dict)}")
    
    print(f"\n🔍 Risk Reversal Analysis:")
    
    # Check if Risk Reversal IDs exist in text_dict
    for i, text_id in enumerate(risk_reversal_ids, 1):
        print(f"\n--- Risk Reversal ID #{i} ---")
        print(f"ID: {text_id}")
        
        # Check text_dict
        in_text_dict = text_id in text_dict
        print(f"✅ In text_dict: {in_text_dict}")
        
        if in_text_dict:
            content = text_dict[text_id]
            print(f"📝 Content length: {len(content)} chars")
            
            # Check keywords
            risk_keywords = ['Risk Reversal', 'risk reversal', 'Zero Cost', 'zero cost', 'Strategy option', 'strategy option']
            found_keywords = [kw for kw in risk_keywords if kw in content]
            print(f"🎯 Keywords found: {found_keywords}")
        
        # Check if node exists in KG
        if kg:
            in_kg = text_id in kg.nodes
            print(f"🔗 In Knowledge Graph: {in_kg}")
            
            if in_kg:
                node_data = kg.nodes[text_id]
                print(f"📊 Node data: {dict(node_data)}")
            else:
                print(f"❌ Text ID not found as KG node - this is the problem!")
        else:
            print(f"❌ No Knowledge Graph found!")
    
    # Sample some KG nodes to see what text IDs are actually connected
    if kg and len(kg.nodes) > 0:
        print(f"\n🔍 Sample KG text nodes:")
        text_nodes = [n for n in kg.nodes if len(n) > 50]  # Likely text IDs (long hash-like)
        
        for i, node_id in enumerate(text_nodes[:5], 1):
            in_text_dict = node_id in text_dict
            print(f"   #{i}: {node_id[:32]}... (in text_dict: {in_text_dict})")
            
            if in_text_dict:
                content = text_dict[node_id]
                print(f"       Content: {content[:100]}...")
    
    # Check what HippoRAG2 would actually use as text_id_list
    print(f"\n🔍 HippoRAG2 text_id_list Analysis:")
    
    # Simulate what HippoRAG2 does: self.text_id_list = list(self.passage_dict.keys())
    # But then: self.KG = self.KG.subgraph(self.node_list + self.text_id_list)
    
    if kg:
        # All text dict keys
        all_text_ids = list(text_dict.keys())
        print(f"   • All text_dict keys: {len(all_text_ids)}")
        
        # Which text IDs are actually KG nodes?
        text_ids_in_kg = [tid for tid in all_text_ids if tid in kg.nodes]
        print(f"   • Text IDs that are KG nodes: {len(text_ids_in_kg)}")
        
        # Are our Risk Reversal IDs among them?
        risk_reversal_in_kg = [tid for tid in risk_reversal_ids if tid in kg.nodes]
        print(f"   • Risk Reversal IDs in KG: {len(risk_reversal_in_kg)} out of {len(risk_reversal_ids)}")
        
        if risk_reversal_in_kg:
            print(f"     ✅ Found Risk Reversal in KG: {risk_reversal_in_kg}")
        else:
            print(f"     ❌ Risk Reversal text not connected to KG!")
            
        # Show what would happen in HippoRAG2 subgraph creation
        print(f"\n🔧 HippoRAG2 Subgraph Analysis:")
        
        # Get node_list equivalent (entity/concept nodes)
        entity_nodes = [n for n in kg.nodes if len(n) < 50]  # Likely entity names (short)
        print(f"   • Entity nodes: {len(entity_nodes)}")
        
        # Combined node list for subgraph
        combined_nodes = entity_nodes + text_ids_in_kg
        print(f"   • Combined nodes for subgraph: {len(combined_nodes)}")
        
        # Create subgraph like HippoRAG2 does
        subgraph = kg.subgraph(combined_nodes)
        text_nodes_in_subgraph = [n for n in subgraph.nodes if n in text_dict]
        
        print(f"   • Text nodes in final subgraph: {len(text_nodes_in_subgraph)}")
        
        # Check Risk Reversal in final subgraph
        risk_reversal_in_subgraph = [tid for tid in risk_reversal_ids if tid in subgraph.nodes]
        print(f"   • Risk Reversal in final subgraph: {len(risk_reversal_in_subgraph)}")
        
        if not risk_reversal_in_subgraph:
            print(f"   ❌ PROBLEM IDENTIFIED: Risk Reversal text not in HippoRAG2 subgraph!")
            print(f"      This explains why it's not retrievable.")
        else:
            print(f"   ✅ Risk Reversal should be retrievable.")
    
    print(f"\n{'='*60}")
    print("🎯 DIAGNOSIS SUMMARY:")
    print("• Risk Reversal content exists in text_dict ✅")
    print("• Need to check if Risk Reversal text IDs are KG nodes")
    print("• HippoRAG2 only retrieves text that's connected to the KG")
    print("• This explains the retrieval failure!")

if __name__ == "__main__":
    main()