#!/usr/bin/env python3
"""
Test MMC Scenario Query with Ollama + Qwen3 LLM Integration

This script tests the complete HippoRAG2 system with LLM-based triple filtering
using the MMC scenario creation query that previously returned poor results.
"""

import os
import sys
import pickle
import requests
from pathlib import Path
from datetime import datetime

# Set environment variable to avoid tokenizer warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Add the project root to Python path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
from atlas_rag.llm_generator import LLMGenerator
from atlas_rag.retriever.inference_config import InferenceConfig
from openai import OpenAI
from sentence_transformers import SentenceTransformer

def test_ollama_connection():
    """Test if Ollama is running and has the required model."""
    print("🔌 Testing Ollama connection...")
    
    ollama_url = "http://localhost:11434"
    model_name = "qwen3:30b-a3b-instruct-2507-q4_K_M"
    
    try:
        response = requests.get(f"{ollama_url}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get("models", [])
            model_names = [model.get("name", "") for model in models]
            
            print(f"✅ Ollama server running at {ollama_url}")
            print(f"📋 Available models: {len(models)}")
            
            if any(model_name in name for name in model_names):
                print(f"✅ Model {model_name} is available")
                return True
            else:
                print(f"❌ Model {model_name} not found")
                return False
        else:
            print(f"❌ Ollama server not responding: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Failed to connect to Ollama: {e}")
        return False

def setup_complete_hipporag2_with_ollama():
    """Setup complete HippoRAG2 system with Ollama LLM."""
    print("\n🚀 Setting up complete HippoRAG2 with Ollama + Qwen3")
    print("=" * 55)
    
    # Load complete data
    data_file = Path("import/pdf_dataset/complete_data.pkl")
    if not data_file.exists():
        print(f"❌ Complete data file not found: {data_file}")
        return None
    
    try:
        with open(data_file, 'rb') as f:
            data = pickle.load(f)
        print(f"✅ Complete data loaded")
        print(f"   📊 Graph: {len(data['KG'].nodes)} nodes, {len(data['KG'].edges)} edges")
        print(f"   📚 Text passages: {len(data.get('text_dict', {}))} entries")
    except Exception as e:
        print(f"❌ Error loading complete data: {e}")
        return None
    
    # Setup Ollama LLM Generator
    print("\n🤖 Setting up Ollama LLM Generator...")
    try:
        ollama_url = "http://localhost:11434"
        model_name = "qwen3:30b-a3b-instruct-2507-q4_K_M"
        
        client = OpenAI(
            base_url=f"{ollama_url}/v1",
            api_key="dummy-key",
        )
        llm_generator = LLMGenerator(client=client, model_name=model_name)
        print(f"✅ LLM Generator created with {model_name}")
    except Exception as e:
        print(f"❌ Error creating LLM Generator: {e}")
        return None
    
    # Setup Sentence Encoder
    print("\n🔤 Setting up sentence encoder...")
    try:
        encoder_model_name = 'all-MiniLM-L6-v2'
        sentence_model = SentenceTransformer(encoder_model_name)
        sentence_encoder = SentenceEmbedding(sentence_model)
        print(f"✅ Sentence encoder created")
    except Exception as e:
        print(f"❌ Error creating sentence encoder: {e}")
        return None
    
    # Setup Inference Config
    inference_config = InferenceConfig()
    
    # Create complete HippoRAG2 system
    print("\n🔍 Creating complete HippoRAG2 system...")
    try:
        hipporag2_retriever = HippoRAG2Retriever(
            llm_generator=llm_generator,  # NOW WITH REAL LLM!
            sentence_encoder=sentence_encoder,
            data=data,
            inference_config=inference_config
        )
        print("✅ Complete HippoRAG2 system created successfully!")
        print("🎯 Now includes LLM-based triple filtering and query understanding")
        return hipporag2_retriever, data
    except Exception as e:
        print(f"❌ Error creating HippoRAG2 system: {e}")
        return None

def test_mmc_scenario_query_with_llm(hipporag2_retriever):
    """Test the MMC scenario query with full LLM-powered HippoRAG2."""
    print("\n🎯 TESTING MMC SCENARIO QUERY WITH LLM FILTERING")
    print("=" * 60)
    
    query = "how to create scenarios in mmc?"
    print(f"🔍 Query: '{query}'")
    print("📋 Expected: MMC Market Maker Cockpit scenario creation procedures")
    print("🚫 Should NOT return: Generic FIX protocol or currency trading docs")
    print()
    
    try:
        # Use complete HippoRAG2 with LLM filtering
        print("🔄 Running HippoRAG2 with LLM-based triple filtering...")
        content, sorted_context_ids = hipporag2_retriever.retrieve(query, topN=3)
        
        if not content:
            print("❌ No content retrieved")
            return False
        
        print(f"✅ Retrieved {len(content)} results with LLM filtering")
        print("\n📊 QUALITY ANALYSIS WITH LLM FILTERING:")
        print("=" * 50)
        
        # Enhanced relevance analysis for MMC
        mmc_terms = ['mmc', 'market maker cockpit', 'scenario', 'cockpit', 'market maker', 
                     'scenarios', 'create', 'setup', 'configuration']
        trading_terms = ['trading', 'position', 'order', 'risk', 'hedge']
        irrelevant_terms = ['fix', 'protocol', 'api', 'message', 'field', 'tag', 'currency', 'settlement']
        
        total_relevance_score = 0
        total_irrelevance_score = 0
        mmc_specific_hits = 0
        
        for i, text_content in enumerate(content):
            print(f"\n🔍 Result #{i+1}:")
            print("─" * 40)
            
            # Calculate MMC-specific relevance
            text_lower = text_content.lower()
            mmc_hits = sum(1 for term in mmc_terms if term in text_lower)
            trading_hits = sum(1 for term in trading_terms if term in text_lower)
            irrelevance_hits = sum(1 for term in irrelevant_terms if term in text_lower)
            
            # Calculate relevance score (MMC terms are weighted more heavily)
            relevance_score = (mmc_hits * 3) + trading_hits - (irrelevance_hits * 2)
            total_relevance_score += relevance_score
            total_irrelevance_score += irrelevance_hits
            
            if mmc_hits > 0:
                mmc_specific_hits += 1
            
            print(f"📊 MMC relevance score: {relevance_score}")
            print(f"   - MMC-specific terms: {mmc_hits}")
            print(f"   - Trading terms: {trading_hits}")
            print(f"   - Irrelevant terms: {irrelevance_hits}")
            
            # Show content preview focused on MMC content
            preview = text_content[:400] + "..." if len(text_content) > 400 else text_content
            lines = preview.split('\n')[:6]  # First 6 lines
            preview_text = '\n'.join(lines)
            print(f"📄 Content preview:")
            print(f"   {preview_text.replace(chr(10), chr(10) + '   ')}")
            
            if i < len(content) - 1:
                print()
        
        # Overall assessment with LLM filtering
        print(f"\n📈 OVERALL ASSESSMENT (WITH LLM FILTERING):")
        print("=" * 50)
        avg_relevance = total_relevance_score / len(content)
        mmc_relevance_rate = (mmc_specific_hits / len(content)) * 100
        
        print(f"📊 Average relevance score: {avg_relevance:.1f}")
        print(f"📊 MMC-specific results: {mmc_specific_hits}/{len(content)} ({mmc_relevance_rate:.1f}%)")
        print(f"📊 Results with irrelevant content: {total_irrelevance_score}/{len(content)}")
        
        # Success criteria for LLM-powered system
        if avg_relevance >= 4 and mmc_relevance_rate >= 66:
            print("🎉 EXCELLENT: High MMC relevance with LLM filtering!")
            success_level = "EXCELLENT"
        elif avg_relevance >= 2 and mmc_relevance_rate >= 33:
            print("👍 GOOD: Decent MMC relevance with LLM filtering")
            success_level = "GOOD"
        elif mmc_specific_hits > 0:
            print("✅ IMPROVED: Some MMC-specific content found")
            success_level = "IMPROVED"
        else:
            print("❌ POOR: Still returning mostly irrelevant content")
            success_level = "POOR"
        
        print(f"\n🏆 RESULT: {success_level}")
        print(f"💡 LLM filtering should significantly improve over semantic-only search")
        
        return success_level in ["EXCELLENT", "GOOD", "IMPROVED"]
            
    except Exception as e:
        print(f"❌ Error in HippoRAG2 retrieval with LLM: {e}")
        return False

def main():
    """Main test function."""
    print("🎯 MMC SCENARIO QUERY TEST WITH OLLAMA + QWEN3 LLM")
    print("=" * 65)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing complete HippoRAG2 algorithm with LLM-based triple filtering")
    print("Query: 'how to create scenarios in mmc?'")
    print()
    
    # Test 1: Ollama connection
    if not test_ollama_connection():
        print("\n❌ TEST FAILED: Ollama not available")
        print("💡 Start Ollama: ollama serve")
        print("💡 Check model: ollama list | grep qwen3")
        return False
    
    # Test 2: Setup complete HippoRAG2 system
    result = setup_complete_hipporag2_with_ollama()
    if not result:
        print("\n❌ TEST FAILED: Cannot setup HippoRAG2 with Ollama")
        return False
    
    hipporag2_retriever, data = result
    
    # Test 3: Test MMC query with LLM filtering
    if not test_mmc_scenario_query_with_llm(hipporag2_retriever):
        print("\n❌ TEST FAILED: Poor quality results even with LLM")
        return False
    
    # Success!
    print("\n" + "="*65)
    print("🎉 MMC SCENARIO QUERY TEST SUCCESSFUL!")
    print("✅ Ollama + Qwen3 30B LLM integration working")
    print("✅ Complete HippoRAG2 algorithm operational")
    print("✅ LLM-based triple filtering improving results")
    print("✅ MMC-specific content retrieved successfully")
    print("🚀 Full HippoRAG2 system ready for production use!")
    print("="*65)
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)