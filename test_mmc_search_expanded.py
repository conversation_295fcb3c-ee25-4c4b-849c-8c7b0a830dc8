#!/usr/bin/env python3
"""
Test to find where the MMC text node ranks in FAISS search
"""

import sys
import os
import pickle
import json
import networkx as nx
import numpy as np
import faiss
import csv
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def find_mmc_node_rank():
    """Find where the MMC text node ranks in similarity search"""
    
    print("=== Finding MMC Text Node Rank in FAISS Search ===\n")
    
    # Load FAISS index
    text_index_path = "import/pdf_dataset/vector_index/text_nodes__from_json_with_emb_non_norm.index"
    text_index = faiss.read_index(text_index_path)
    
    print(f"FAISS index: {text_index.ntotal} vectors, dimension: {text_index.d}")
    
    # Load text mappings
    text_hash_to_numeric = {}
    text_numeric_to_content = {}
    
    with open("import/pdf_dataset/triples_csv/text_nodes__from_json_with_numeric_id.csv", 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # Skip header
        for row in reader:
            if len(row) >= 4:
                try:
                    text_hash = row[0]
                    content = row[1]
                    numeric_id = int(row[2])
                    
                    text_hash_to_numeric[text_hash] = numeric_id
                    text_numeric_to_content[numeric_id] = content
                except (ValueError, IndexError):
                    continue
    
    # Find MMC text node
    mmc_hash = "1fb3759bd9af49535dc71864a9dfaa0382b097be4ae24abdd55ef9dd5838b149"
    mmc_numeric_id = text_hash_to_numeric[mmc_hash]
    print(f"MMC text node: numeric_id={mmc_numeric_id}")
    
    # Create query embedding (random for testing)
    query_embedding = np.random.randn(text_index.d).astype(np.float32)
    query_embedding = query_embedding / np.linalg.norm(query_embedding)
    query_embedding_2d = query_embedding.reshape(1, -1)
    
    # Search ALL vectors to find where MMC ranks
    k = text_index.ntotal  # Search all vectors
    distances, indices = text_index.search(query_embedding_2d, k)
    
    # Find MMC text node in results
    mmc_rank = None
    mmc_distance = None
    
    for rank, idx in enumerate(indices[0]):
        if idx == mmc_numeric_id:
            mmc_rank = rank + 1  # 1-based ranking
            mmc_distance = distances[0][rank]
            break
    
    if mmc_rank is not None:
        print(f"✓ MMC text node found at rank: {mmc_rank}/{text_index.ntotal}")
        print(f"✓ MMC distance: {mmc_distance:.6f}")
        
        # Show what ranks higher
        print(f"\nTop 10 results (MMC is at rank {mmc_rank}):")
        for i in range(min(10, len(indices[0]))):
            idx = indices[0][i]
            dist = distances[0][i]
            
            if idx in text_numeric_to_content:
                content_preview = text_numeric_to_content[idx][:80] + "..."
                is_mmc = "MMC" in content_preview or "Market Maker Cockpit" in content_preview or "scenario" in content_preview.lower()
                marker = "🎯" if idx == mmc_numeric_id else ("📍" if is_mmc else "  ")
                print(f"  {marker} Rank {i+1}: idx={idx}, dist={dist:.6f}")
                print(f"     {content_preview}")
            else:
                print(f"     Rank {i+1}: idx={idx}, dist={dist:.6f} (content not found)")
        
        # Check if any MMC/scenario content ranks higher
        higher_mmc_content = []
        for i in range(min(mmc_rank - 1, 50)):  # Check top 50 or up to MMC rank
            idx = indices[0][i]
            if idx in text_numeric_to_content:
                content = text_numeric_to_content[idx]
                if "MMC" in content or "Market Maker Cockpit" in content or "scenario" in content.lower():
                    higher_mmc_content.append((i+1, idx, distances[0][i], content[:100]))
        
        if higher_mmc_content:
            print(f"\nOther MMC/scenario content ranking higher than main MMC text:")
            for rank, idx, dist, content in higher_mmc_content:
                print(f"  Rank {rank}: idx={idx}, dist={dist:.6f}")
                print(f"    {content}...")
        else:
            print(f"\nNo other MMC/scenario content found in top {min(mmc_rank-1, 50)} results")
    
    else:
        print(f"❌ MMC text node NOT found in search results!")
        
        # Check what's in the index
        print(f"\nChecking if MMC numeric ID {mmc_numeric_id} is in valid range...")
        if mmc_numeric_id < text_index.ntotal:
            print(f"✓ MMC numeric ID is within index range (0-{text_index.ntotal-1})")
        else:
            print(f"❌ MMC numeric ID {mmc_numeric_id} is OUT OF RANGE (max: {text_index.ntotal-1})")

def test_actual_query_embedding():
    """Test with embeddings from actual sentence encoder"""
    print(f"\n=== Testing with Scenario-Related Query ===\n")
    
    try:
        # Try to load a real sentence encoder
        from atlas_rag.vectorstore.embedding_model import EmbeddingModel
        
        # This would require the actual model - skipping for now
        print("Real sentence encoder test would require model loading...")
        
        # Instead, create embedding that emphasizes scenario/create/MMC terms
        # This is a simplified test
        
        # Load some actual embeddings to understand the scale
        node_emb_path = "import/pdf_dataset/kg_embedding/node_embeddings.npy"
        if os.path.exists(node_emb_path):
            node_embeddings = np.load(node_emb_path)
            
            # Find a few embeddings that might be related to scenarios/MMC
            text_hash_to_numeric = {}
            with open("import/pdf_dataset/triples_csv/text_nodes__from_json_with_numeric_id.csv", 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                next(reader)
                for row in reader:
                    if len(row) >= 4:
                        try:
                            text_hash = row[0]
                            content = row[1]
                            numeric_id = int(row[2])
                            text_hash_to_numeric[text_hash] = numeric_id
                            
                            # Look for scenario-related content
                            if "scenario" in content.lower() and "create" in content.lower():
                                print(f"Found scenario/create content at idx {numeric_id}:")
                                print(f"  {content[:150]}...")
                                
                                # Use this embedding as a "query"
                                if numeric_id < len(node_embeddings):
                                    scenario_embedding = node_embeddings[numeric_id]
                                    print(f"  Using this as query embedding (norm: {np.linalg.norm(scenario_embedding):.4f})")
                                    
                                    # Test similarity search with this embedding
                                    test_similarity_with_embedding(scenario_embedding[:384])  # Truncate to index dimension
                                    break
                        except (ValueError, IndexError):
                            continue
    
    except ImportError:
        print("Could not load sentence encoder - skipping real embedding test")

def test_similarity_with_embedding(query_embedding):
    """Test similarity search with a specific embedding"""
    
    # Load FAISS index
    text_index_path = "import/pdf_dataset/vector_index/text_nodes__from_json_with_emb_non_norm.index"
    text_index = faiss.read_index(text_index_path)
    
    # Load text mappings
    text_numeric_to_content = {}
    with open("import/pdf_dataset/triples_csv/text_nodes__from_json_with_numeric_id.csv", 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)
        for row in reader:
            if len(row) >= 4:
                try:
                    text_hash = row[0]
                    content = row[1]
                    numeric_id = int(row[2])
                    text_numeric_to_content[numeric_id] = content
                except (ValueError, IndexError):
                    continue
    
    # Search
    query_embedding_2d = query_embedding.reshape(1, -1)
    k = 20
    distances, indices = text_index.search(query_embedding_2d, k)
    
    print(f"\nTop {k} results with scenario-based query:")
    for i in range(k):
        idx = indices[0][i]
        dist = distances[0][i]
        
        if idx in text_numeric_to_content:
            content = text_numeric_to_content[idx]
            is_relevant = any(term in content.lower() for term in ["mmc", "market maker cockpit", "scenario", "create"])
            marker = "✓" if is_relevant else " "
            print(f"  {marker} Rank {i+1}: idx={idx}, dist={dist:.6f}")
            print(f"     {content[:100]}...")
        else:
            print(f"     Rank {i+1}: idx={idx}, dist={dist:.6f} (content not found)")

if __name__ == "__main__":
    find_mmc_node_rank()
    test_actual_query_embedding()