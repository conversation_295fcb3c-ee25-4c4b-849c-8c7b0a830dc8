#!/bin/bash
# Ollama Memory Cleanup Script
echo "🧹 Ollama Memory Cleanup Starting..."

# Stop Ollama service gracefully
echo "Stopping Ollama service..."
pkill -TERM ollama
sleep 5

# Force kill if still running
if pgrep ollama > /dev/null; then
    echo "Force stopping Ollama..."
    pkill -KILL ollama
    sleep 2
fi

# Clear system caches (requires sudo)
echo "Clearing system caches..."
sync
echo 3 | sudo tee /proc/sys/vm/drop_caches > /dev/null 2>&1 || echo "Cache clear requires sudo"

# Set optimized environment variables
export OLLAMA_KV_CACHE_TYPE="q8_0"
export OLLAMA_MAX_LOADED_MODELS="1"
export OLLAMA_NUM_PARALLEL="1"

# Restart Ollama with optimizations
echo "Restarting Ollama with memory optimizations..."
ollama serve &

# Wait for startup
sleep 10

echo "✅ Ollama restart complete!"
echo "Environment optimizations:"
echo "  - K/V Cache Quantization: Enabled"
echo "  - Max Models: 1"
echo "  - Parallel Requests: 1"
