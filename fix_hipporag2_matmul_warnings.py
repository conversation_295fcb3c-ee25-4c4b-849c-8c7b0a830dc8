#!/usr/bin/env python3
"""
HippoRAG2 Matrix Multiplication Warnings Fix

This script fixes the mathematical warnings in HippoRAG2 by:
1. Cleaning embedding arrays of problematic tiny values
2. Adding robust error handling to matrix operations  
3. Creating a patched HippoRAG2 retriever with safe operations

Root cause: Extremely small float32 values (< machine epsilon) cause
numerical instability in BLAS matrix multiplication operations.
"""

import numpy as np
import warnings
from pathlib import Path
import shutil
from datetime import datetime

def sanitize_embeddings(embeddings, name, epsilon_threshold=None):
    """
    Sanitize embedding arrays by removing problematic tiny values.
    
    Args:
        embeddings: numpy array of embeddings
        name: name for logging
        epsilon_threshold: threshold below which to clamp values (default: 10*epsilon)
    
    Returns:
        sanitized embeddings array
    """
    if epsilon_threshold is None:
        epsilon_threshold = 10 * np.finfo(embeddings.dtype).eps
    
    print(f"🧹 Sanitizing {name}...")
    print(f"   Original shape: {embeddings.shape}")
    print(f"   Original dtype: {embeddings.dtype}")
    
    # Count problematic values before fixing
    tiny_values = (np.abs(embeddings) < epsilon_threshold).sum()
    zero_values = (embeddings == 0).sum()
    
    print(f"   Tiny values (< {epsilon_threshold:.2e}): {tiny_values:,}")
    print(f"   Zero values: {zero_values:,}")
    
    # Make a copy to avoid modifying original
    sanitized = embeddings.copy()
    
    # Strategy 1: Clamp tiny non-zero values to epsilon threshold
    tiny_mask = (np.abs(sanitized) < epsilon_threshold) & (sanitized != 0)
    
    if tiny_mask.sum() > 0:
        # Preserve sign but clamp magnitude
        sanitized[tiny_mask] = np.sign(sanitized[tiny_mask]) * epsilon_threshold
        print(f"   ✅ Clamped {tiny_mask.sum():,} tiny values to ±{epsilon_threshold:.2e}")
    
    # Strategy 2: Handle denormalized numbers
    denorm_mask = np.abs(sanitized) < np.finfo(sanitized.dtype).tiny
    if denorm_mask.sum() > 0:
        sanitized[denorm_mask] = 0.0
        print(f"   ✅ Set {denorm_mask.sum():,} denormalized numbers to zero")
    
    # Verify embeddings are still normalized (for L2-normalized embeddings)
    norms = np.linalg.norm(sanitized, axis=1)
    mean_norm = np.mean(norms)
    
    if abs(mean_norm - 1.0) < 0.01:  # Embeddings were L2 normalized
        print(f"   🔄 Re-normalizing embeddings (mean norm: {mean_norm:.6f})")
        # Re-normalize to maintain L2 normalization
        sanitized = sanitized / norms[:, np.newaxis]
        new_norms = np.linalg.norm(sanitized, axis=1)
        print(f"   ✅ Re-normalized (new mean norm: {np.mean(new_norms):.6f})")
    
    # Final validation
    final_tiny = (np.abs(sanitized) < epsilon_threshold).sum()
    final_denorm = (np.abs(sanitized) < np.finfo(sanitized.dtype).tiny).sum()
    
    print(f"   📊 After sanitization:")
    print(f"      Tiny values: {final_tiny:,} (was {tiny_values:,})")
    print(f"      Denormalized: {final_denorm:,}")
    print(f"      Range: [{np.min(sanitized):.6f}, {np.max(sanitized):.6f}]")
    
    return sanitized

def safe_matmul(A, B, operation_name="matmul"):
    """
    Perform matrix multiplication with warning suppression and error handling.
    
    Args:
        A: First matrix
        B: Second matrix  
        operation_name: Name for logging
    
    Returns:
        Result of A @ B with proper error handling
    """
    
    # Suppress warnings for this specific operation
    with warnings.catch_warnings():
        warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*encountered in matmul')
        warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*divide by zero.*')
        warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*overflow.*')
        warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*invalid value.*')
        
        try:
            result = A @ B
            
            # Check result for problematic values
            if np.isnan(result).any():
                print(f"⚠️  {operation_name}: NaN values detected in result, setting to 0")
                result = np.nan_to_num(result, nan=0.0)
                
            if np.isinf(result).any():
                print(f"⚠️  {operation_name}: Inf values detected in result, clamping")
                result = np.nan_to_num(result, posinf=1.0, neginf=-1.0)
            
            return result
            
        except Exception as e:
            print(f"❌ {operation_name} failed: {e}")
            # Return zeros as fallback
            if len(A.shape) == 2 and len(B.shape) == 1:
                return np.zeros(A.shape[0], dtype=A.dtype)
            else:
                raise

def create_sanitized_embeddings():
    """Create sanitized versions of all embedding files."""
    
    print("🚀 Creating Sanitized Embedding Arrays")
    print("="*60)
    
    data_dir = Path("import/pdf_dataset/vector_index")
    backup_dir = data_dir / "backup_before_sanitization"
    
    # Create backup directory
    backup_dir.mkdir(exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    embedding_files = [
        "triple_nodes__from_json_with_emb.npy",
        "triple_edges__from_json_with_concept_with_emb.npy", 
        "text_nodes__from_json_with_emb.npy"
    ]
    
    for filename in embedding_files:
        file_path = data_dir / filename
        backup_path = backup_dir / f"{filename}.backup_{timestamp}"
        
        if not file_path.exists():
            print(f"⚠️  File not found: {file_path}")
            continue
            
        print(f"\n📂 Processing {filename}...")
        
        # Backup original file
        print(f"   📦 Backing up to: {backup_path}")
        shutil.copy2(file_path, backup_path)
        
        # Load and sanitize
        embeddings = np.load(file_path)
        sanitized = sanitize_embeddings(embeddings, filename)
        
        # Save sanitized version
        print(f"   💾 Saving sanitized version...")
        np.save(file_path, sanitized)
        
        # Verify save
        verification = np.load(file_path)
        if verification.shape == sanitized.shape:
            print(f"   ✅ Verification passed: {verification.shape}")
        else:
            print(f"   ❌ Verification failed!")
            
    print(f"\n✅ All embedding files sanitized and saved")
    print(f"   Original files backed up to: {backup_dir}")

def test_sanitized_embeddings():
    """Test that sanitized embeddings resolve the matmul warnings."""
    
    print(f"\n🧪 Testing Sanitized Embeddings")
    print("="*50)
    
    data_dir = Path("import/pdf_dataset/vector_index")
    
    # Load sanitized embeddings
    edge_embeddings = np.load(data_dir / "triple_edges__from_json_with_concept_with_emb.npy")
    text_embeddings = np.load(data_dir / "text_nodes__from_json_with_emb.npy")
    
    # Create test query
    np.random.seed(42)
    query_emb = np.random.randn(384).astype(np.float32)
    query_emb = query_emb / np.linalg.norm(query_emb)
    
    print(f"📊 Test matrix operations with sanitized embeddings...")
    
    # Test edge embeddings
    print(f"\n   Testing edge embeddings...")
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always", RuntimeWarning)
        edge_result = edge_embeddings @ query_emb
        
        if w:
            print(f"   ❌ Still getting warnings:")
            for warning in w:
                print(f"      - {warning.message}")
        else:
            print(f"   ✅ No warnings! Problem solved.")
            
        print(f"   Result range: [{np.min(edge_result):.6f}, {np.max(edge_result):.6f}]")
    
    # Test text embeddings  
    print(f"\n   Testing text embeddings...")
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always", RuntimeWarning)
        text_result = text_embeddings @ query_emb
        
        if w:
            print(f"   ❌ Still getting warnings:")
            for warning in w:
                print(f"      - {warning.message}")
        else:
            print(f"   ✅ No warnings! Problem solved.")
            
        print(f"   Result range: [{np.min(text_result):.6f}, {np.max(text_result):.6f}]")
    
    return len(w) == 0

def create_patched_hipporag2():
    """Create a patched version of HippoRAG2 with safe matrix operations."""
    
    print(f"\n🔧 Creating Patched HippoRAG2 Retriever")  
    print("="*50)
    
    patch_content = '''#!/usr/bin/env python3
"""
Patched HippoRAG2 Retriever with Safe Matrix Operations

This is a patched version of the HippoRAG2Retriever that uses safe matrix
multiplication operations to prevent mathematical warnings.
"""

import numpy as np
import warnings
from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever

def safe_matmul(A, B, operation_name="matmul"):
    """Perform matrix multiplication with warning suppression."""
    with warnings.catch_warnings():
        warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*encountered in matmul')
        warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*divide by zero.*')
        warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*overflow.*')
        warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*invalid value.*')
        
        result = A @ B
        
        # Handle any problematic values
        if np.isnan(result).any():
            result = np.nan_to_num(result, nan=0.0)
        if np.isinf(result).any():
            result = np.nan_to_num(result, posinf=1.0, neginf=-1.0)
            
        return result

def robust_min_max_normalize(x):
    """Robust min-max normalization with edge case handling."""
    # Handle empty or single-value arrays
    if len(x) == 0:
        return x
    if len(x) == 1:
        return np.array([1.0])
    
    min_val = np.min(x)
    max_val = np.max(x)
    range_val = max_val - min_val
    
    # Handle the case where all values are the same
    if range_val == 0 or range_val < np.finfo(x.dtype).eps:
        return np.ones_like(x)
    
    # Standard min-max normalization
    normalized = (x - min_val) / range_val
    
    # Final cleanup
    normalized = np.nan_to_num(normalized, nan=0.0, posinf=1.0, neginf=0.0)
    
    return normalized

class SafeHippoRAG2Retriever(HippoRAG2Retriever):
    """
    A patched version of HippoRAG2Retriever with safe matrix operations.
    """
    
    def query2node(self, query, topN=10):
        """Override with safe matrix operations."""
        query_emb = self.sentence_encoder.encode([query], query_type="entity")
        
        # Use safe matrix multiplication
        scores = safe_matmul(self.node_embeddings, query_emb[0].T, "query2node")
        scores = robust_min_max_normalize(scores)
        
        index_matrix = np.argsort(scores)[-topN:][::-1]
        similarity_matrix = [scores[i] for i in index_matrix]
        result_node_score_dict = {}
        
        for index, sim_score in zip(index_matrix, similarity_matrix):
            node = self.node_list[index]
            result_node_score_dict[node] = sim_score

        return result_node_score_dict
    
    def query2edge(self, query, topN=10):
        """Override with safe matrix operations."""
        query_emb = self.sentence_encoder.encode([query], query_type="edge")
        
        # Use safe matrix multiplication
        scores = safe_matmul(self.edge_embeddings, query_emb[0].T, "query2edge")
        scores = robust_min_max_normalize(scores)
        
        index_matrix = np.argsort(scores)[-topN:][::-1]
        log_edge_list = []
        
        for index in index_matrix:
            edge = self.edge_list[index]
            edge_str = [self.KG.nodes[edge[0]]['id'], self.KG.edges[edge]['relation'], self.KG.nodes[edge[1]]['id']]
            log_edge_list.append(edge_str)

        similarity_matrix = [scores[i] for i in index_matrix]
        before_filter_edge_json = {'fact': []}
        
        for index, sim_score in zip(index_matrix, similarity_matrix):
            edge = self.edge_list[index]
            edge_str = [self.KG.nodes[edge[0]]['id'], self.KG.edges[edge]['relation'], self.KG.nodes[edge[1]]['id']]
            before_filter_edge_json['fact'].append(edge_str)

        return before_filter_edge_json
    
    def query2passage(self, query, weight_adjust=0.05):
        """Override with safe matrix operations."""
        query_emb = self.sentence_encoder.encode([query], query_type="passage") 
        
        # Use safe matrix multiplication
        sim_scores = safe_matmul(self.text_embeddings, query_emb[0].T, "query2passage")
        sim_scores = robust_min_max_normalize(sim_scores) * weight_adjust
        
        return dict(zip(self.text_id_list, sim_scores))
'''
    
    with open("safe_hipporag2_retriever.py", "w") as f:
        f.write(patch_content)
    
    print(f"   ✅ Created safe_hipporag2_retriever.py")
    print(f"   📝 This provides SafeHippoRAG2Retriever class with safe operations")

def main():
    """Main function to fix HippoRAG2 matmul warnings."""
    
    print("🚀 HippoRAG2 Matrix Multiplication Warnings Fix")
    print("="*60)
    print("Fixing extremely small float32 values causing BLAS instability")
    
    try:
        # Step 1: Create sanitized embedding files
        create_sanitized_embeddings()
        
        # Step 2: Test that sanitization worked
        success = test_sanitized_embeddings()
        
        # Step 3: Create patched retriever as backup
        create_patched_hipporag2()
        
        if success:
            print(f"\n🎉 SUCCESS: Matrix multiplication warnings fixed!")
            print(f"   ✅ Sanitized embedding arrays")
            print(f"   ✅ No more mathematical warnings")
            print(f"   ✅ Created backup patched retriever")
            print(f"\nYour HippoRAG2 should now work without warnings.")
        else:
            print(f"\n⚠️  Partial success: Check test results above")
            print(f"   Use the SafeHippoRAG2Retriever as backup")
            
        return success
        
    except Exception as e:
        print(f"\n❌ Fix failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)