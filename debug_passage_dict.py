#!/usr/bin/env python3
"""
Debug what's actually in passage_dict at runtime.
"""

import os
import sys

# Set environment variables
os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'

def debug_passage_dict():
    """Debug passage_dict content."""
    
    print("=" * 70)
    print("🔍 Debugging passage_dict Content")
    print("=" * 70)
    
    try:
        from hipporag2_interactive_debug import InteractiveHippoRAG2Debug
        
        # Initialize
        console = InteractiveHippoRAG2Debug()
        
        # Load data
        print("\n1. Loading data...")
        if not console.load_data():
            return False
        
        # Setup LLM (mock is fine)
        console.params['use_ollama'] = False
        if not console.setup_llm():
            return False
        
        # Initialize retriever
        if not console.initialize_retriever():
            return False
        
        # Now check what's in passage_dict
        print("\n2. Checking passage_dict content...")
        
        passage_dict = console.retriever.passage_dict
        print(f"   Total entries: {len(passage_dict)}")
        
        # Check sample entries
        print("\n📄 Sample entries:")
        for i, (key, value) in enumerate(list(passage_dict.items())[:3]):
            print(f"\n{i+1}. Key: {key[:50]}...")
            print(f"   Value type: {type(value)}")
            if isinstance(value, str):
                preview = value[:200] + "..." if len(value) > 200 else value
                print(f"   Content: {preview}")
        
        # Check specific nodes that were retrieved
        test_nodes = [
            "0917600ee52eb9af9d473627cc8eb786f2b58de51e04e5123c184efc1a329c9c",
            "3463e381bb001372402dc283d29d723553f0302533fa90659e9bedefe2a35835"
        ]
        
        print("\n🔍 Checking specific nodes:")
        for node_id in test_nodes:
            if node_id in passage_dict:
                value = passage_dict[node_id]
                print(f"\nNode: {node_id[:50]}...")
                print(f"Value: {value[:100] if isinstance(value, str) else value}")
            else:
                print(f"\nNode: {node_id[:50]}... NOT FOUND in passage_dict")
        
        # Check if any entry has actual text
        print("\n🔍 Checking for actual text content:")
        has_real_text = 0
        for value in passage_dict.values():
            if isinstance(value, str) and len(value) > 100 and not value.isalnum():
                has_real_text += 1
        
        print(f"   Entries with real text (>100 chars): {has_real_text}/{len(passage_dict)}")
        
        # Search for specific terms
        print("\n🔍 Searching for specific terms:")
        terms = ["resting", "Supersonic", "OCO"]
        for term in terms:
            count = sum(1 for v in passage_dict.values() 
                       if isinstance(v, str) and term.lower() in v.lower())
            print(f"   '{term}': {count} passages")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    debug_passage_dict()

if __name__ == "__main__":
    main()