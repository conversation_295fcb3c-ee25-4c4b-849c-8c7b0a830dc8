#!/usr/bin/env python3
"""
Final test of Q&A with corrected text_dict.
"""

import os
import sys

# Set environment variables
os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'

def test_qa_final():
    """Test Q&A with corrected text."""
    
    print("=" * 70)
    print("🧪 Final Q&A Test with Corrected Text")
    print("=" * 70)
    
    try:
        from hipporag2_interactive_debug import InteractiveHippoRAG2Debug
        
        # Initialize
        console = InteractiveHippoRAG2Debug()
        console.params['detail_level'] = 'normal'  # Show readable output
        
        # Load data
        print("\n1. Loading data with corrected text_dict...")
        if not console.load_data():
            return False
        
        # Setup LLM
        print("\n2. Setting up LLM...")
        if not console.setup_llm():
            return False
        
        # Initialize retriever
        print("\n3. Initializing retriever...")
        if not console.initialize_retriever():
            return False
        
        # Test query about resting orders
        print("\n" + "=" * 70)
        print("TEST: Resting order in Supersonic")
        print("=" * 70)
        
        console.ask_question("What is a resting order in Supersonic?")
        
        print("\n" + "=" * 70)
        print("✅ Test completed!")
        print("=" * 70)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    
    success = test_qa_final()
    
    if success:
        print("\n✅ SUCCESS! The Q&A system is now working with actual text!")
        print("\nYou can now run the full interactive console:")
        print("  python hipporag2_interactive_debug.py")
        print("\nExample queries to try:")
        print("  - What is a resting order in Supersonic?")
        print("  - How does an OCO order work?")
        print("  - Explain risk reversal trading")
        print("  - What is Market Maker Cockpit?")
    else:
        print("\n❌ Test failed")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())