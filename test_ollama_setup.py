"""
Simple test script to verify OLLAMA setup and configuration.
This script tests the OLLAMA connection and basic functionality before running the full pipeline.
"""

import sys
import os
from pathlib import Path
from setup_ollama_llm_generator import setup_ollama_llm_generator, OllamaLLMGenerator
from setup_embedding_model import setup_qwen_embedding_model
import requests
from configparser import Config<PERSON>arser

def test_ollama_connection():
    """Test OLLAMA server connection."""
    print("🔌 Testing OLLAMA server connection...")
    
    # Load config
    config = ConfigParser()
    config.read('config.ini')
    
    base_url = config.get('settings', 'OLLAMA_BASE_URL', fallback='http://localhost:11434')
    model_name = config.get('settings', 'OLLAMA_MODEL', fallback='qwen3:30b-a3b-instruct-2507-q4_K_M')
    
    print(f"   Base URL: {base_url}")
    print(f"   Model: {model_name}")
    
    try:
        # Test server connectivity
        response = requests.get(f"{base_url.rstrip('/')}/api/tags", timeout=10)
        response.raise_for_status()
        
        models = response.json().get("models", [])
        model_names = [model.get("name", "") for model in models]
        
        print(f"✅ OLLAMA server is running")
        print(f"   Available models: {len(models)}")
        for model in models[:3]:  # Show first 3 models
            print(f"   - {model.get('name', 'Unknown')}")
        
        # Check if our model is available
        if any(model_name in name for name in model_names):
            print(f"✅ Model '{model_name}' is available")
            return True
        else:
            print(f"⚠️  Model '{model_name}' not found in available models")
            print(f"   Model will be pulled on first use")
            return True
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Cannot connect to OLLAMA server at {base_url}")
        print(f"   Please ensure OLLAMA is running:")
        print(f"   1. Start OLLAMA: ollama serve")
        print(f"   2. Or check if OLLAMA is running on a different port")
        return False
    except Exception as e:
        print(f"❌ Error connecting to OLLAMA: {e}")
        return False

def test_ollama_generator():
    """Test OLLAMA generator functionality."""
    print("\n🤖 Testing OLLAMA generator...")
    
    try:
        # Setup generator
        generator = setup_ollama_llm_generator()
        print(f"✅ OLLAMA generator created successfully")
        
        # Test basic generation
        print("   Testing basic text generation...")
        response = generator.generate_response(
            "What is artificial intelligence in one sentence?",
            max_new_tokens=50,
            temperature=0.7
        )
        print(f"   Response: {response[:100]}...")
        
        # Test NER
        print("   Testing Named Entity Recognition...")
        entities = generator.ner("Apple Inc. was founded by Steve Jobs in California.")
        print(f"   Entities: {entities}")
        
        # Test context generation
        print("   Testing context-based generation...")
        context = "Apple Inc. is a technology company founded in 1976 by Steve Jobs."
        answer = generator.generate_with_context(
            "When was Apple founded?",
            context,
            max_new_tokens=30
        )
        print(f"   Answer: {answer}")
        
        print(f"✅ OLLAMA generator tests passed")
        return True
        
    except Exception as e:
        print(f"❌ OLLAMA generator test failed: {e}")
        return False

def test_embedding_model():
    """Test embedding model setup."""
    print("\n🔤 Testing embedding model...")
    
    try:
        # Setup embedding model
        sentence_encoder = setup_qwen_embedding_model()
        print(f"✅ Embedding model created successfully")
        
        # Test encoding
        test_texts = [
            "This is a test sentence.",
            "Knowledge graphs represent information as entities and relationships."
        ]
        
        embeddings = sentence_encoder.encode(test_texts, batch_size=2)
        print(f"   Embeddings shape: {embeddings.shape}")
        print(f"   Embedding dimension: {embeddings.shape[1]}")
        
        print(f"✅ Embedding model tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Embedding model test failed: {e}")
        return False

def test_data_directory():
    """Test if the expected data directory exists."""
    print("\n📁 Testing data directory...")
    
    data_dir = Path("import/pdf_dataset")
    
    if not data_dir.exists():
        print(f"❌ Data directory not found: {data_dir}")
        print(f"   Expected directory structure:")
        print(f"   - import/pdf_dataset/kg_graphml/")
        print(f"   - import/pdf_dataset/vector_index/")
        print(f"   - import/pdf_dataset/triples_csv/")
        return False
    
    # Check subdirectories
    required_subdirs = ["kg_graphml", "vector_index", "triples_csv"]
    missing_dirs = []
    
    for subdir in required_subdirs:
        if not (data_dir / subdir).exists():
            missing_dirs.append(subdir)
    
    if missing_dirs:
        print(f"❌ Missing required subdirectories: {missing_dirs}")
        return False
    
    # Check for key files
    graphml_file = data_dir / "kg_graphml" / "knowledge_graph.graphml"
    pkl_file = data_dir / "kg_graphml" / "knowledge_graph.pkl"
    
    if not (graphml_file.exists() or pkl_file.exists()):
        print(f"❌ No graph file found (knowledge_graph.graphml or .pkl)")
        return False
    
    # Check embeddings
    embedding_files = [
        "triple_nodes__from_json_with_emb.npy",
        "triple_edges__from_json_with_concept_with_emb.npy",
        "text_nodes__from_json_with_emb.npy"
    ]
    
    missing_embeddings = []
    for emb_file in embedding_files:
        if not (data_dir / "vector_index" / emb_file).exists():
            missing_embeddings.append(emb_file)
    
    if missing_embeddings:
        print(f"⚠️  Missing embedding files: {missing_embeddings}")
        print(f"   Pipeline may still work with available embeddings")
    
    print(f"✅ Data directory structure is valid")
    print(f"   Graph file: {'✅' if graphml_file.exists() or pkl_file.exists() else '❌'}")
    print(f"   Embeddings: {len(embedding_files) - len(missing_embeddings)}/{len(embedding_files)} found")
    
    return True

def test_config_file():
    """Test configuration file."""
    print("\n⚙️  Testing configuration file...")
    
    config_file = "config.ini"
    
    if not os.path.exists(config_file):
        print(f"❌ Configuration file not found: {config_file}")
        return False
    
    config = ConfigParser()
    config.read(config_file)
    
    # Check required settings
    required_settings = [
        ('OLLAMA_BASE_URL', 'http://localhost:11434'),
        ('OLLAMA_MODEL', 'qwen3:30b-a3b-instruct-2507-q4_K_M'),
        ('EMBEDDING_MODEL', 'Qwen/Qwen3-Embedding-4B'),
        ('NEO4J_URI', 'bolt://localhost:7687'),
        ('DATABASE_NAME', 'autoschemakg')
    ]
    
    missing_settings = []
    
    for setting, default in required_settings:
        if not config.has_section('settings') or not config.has_option('settings', setting):
            missing_settings.append(setting)
        else:
            value = config.get('settings', setting)
            print(f"   {setting}: {value}")
    
    if missing_settings:
        print(f"❌ Missing required settings: {missing_settings}")
        return False
    
    print(f"✅ Configuration file is valid")
    return True

def run_all_tests():
    """Run all tests and report results."""
    print("🧪 Running HippoRAG2 Setup Tests")
    print("=" * 50)
    
    tests = [
        ("Configuration File", test_config_file),
        ("Data Directory", test_data_directory), 
        ("OLLAMA Connection", test_ollama_connection),
        ("OLLAMA Generator", test_ollama_generator),
        ("Embedding Model", test_embedding_model)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print(f"📊 Test Results Summary")
    print(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name:<20} {status}")
    
    print(f"\n📈 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print(f"🎉 All tests passed! HippoRAG2 setup is ready.")
        print(f"\nNext steps:")
        print(f"   1. Run: python hipporag2_pipeline.py")
        print(f"   2. Or run tests: python test_hipporag2_integration.py")
        return True
    else:
        print(f"⚠️  Some tests failed. Please fix the issues above before proceeding.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)