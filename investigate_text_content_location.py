#!/usr/bin/env python3
"""
Investigate Text Content Location

This script investigates where the actual text content is stored,
since the GraphML text nodes are empty.
"""

import os
import sys
import json
import pandas as pd
import networkx as nx
from pathlib import Path
from datetime import datetime

class TextContentInvestigator:
    """Investigates where text content is actually stored."""
    
    def __init__(self, data_directory="import/pdf_dataset"):
        self.data_directory = Path(data_directory)
        
        print("🔍 TEXT CONTENT LOCATION INVESTIGATION")
        print("="*60)
        print(f"Finding where actual text content is stored")
        print(f"Data Directory: {self.data_directory}")
        print()
    
    def check_all_possible_sources(self):
        """Check all possible sources for text content."""
        print("📊 Checking All Possible Text Content Sources...")
        
        sources_found = {}
        
        # 1. Check GraphML (we know this is empty, but let's confirm)
        print("\n   1. GraphML File Analysis:")
        graphml_path = self.data_directory / "kg_graphml" / "knowledge_graph.graphml"
        if graphml_path.exists():
            KG = nx.read_graphml(str(graphml_path))
            
            # Check all node attributes for text content
            text_attributes = set()
            sample_text_node = None
            
            for node_id, node_data in KG.nodes(data=True):
                if node_data.get('type') == 'text':
                    sample_text_node = (node_id, node_data)
                    for attr, value in node_data.items():
                        text_attributes.add(attr)
                    break
            
            print(f"      Text node attributes: {sorted(text_attributes)}")
            if sample_text_node:
                node_id, node_data = sample_text_node
                print(f"      Sample text node {node_id}:")
                for attr, value in node_data.items():
                    value_preview = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                    print(f"         {attr}: {value_preview}")
            
            sources_found['graphml'] = {
                'exists': True,
                'text_nodes': sum(1 for _, data in KG.nodes(data=True) if data.get('type') == 'text'),
                'text_attributes': list(text_attributes),
                'has_content': any('text' in attr.lower() or 'content' in attr.lower() for attr in text_attributes)
            }
        
        # 2. Check CSV files
        print(f"\n   2. CSV Files Analysis:")
        csv_files = list(self.data_directory.glob("*.csv"))
        
        for csv_file in csv_files:
            print(f"      📄 {csv_file.name}")
            try:
                df = pd.read_csv(csv_file)
                print(f"         Rows: {len(df):,}")
                print(f"         Columns: {list(df.columns)}")
                
                # Look for text content columns
                text_columns = []
                for col in df.columns:
                    if any(keyword in col.lower() for keyword in ['text', 'content', 'original', 'passage']):
                        text_columns.append(col)
                        
                        # Sample the content
                        sample_values = df[col].dropna().head(3)
                        print(f"         Text column '{col}' samples:")
                        for idx, value in sample_values.items():
                            value_preview = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                            print(f"            Row {idx}: {value_preview}")
                
                if text_columns:
                    sources_found[csv_file.name] = {
                        'exists': True,
                        'rows': len(df),
                        'text_columns': text_columns,
                        'has_content': True
                    }
                
            except Exception as e:
                print(f"         Error: {e}")
        
        # 3. Check JSON files
        print(f"\n   3. JSON Files Analysis:")
        json_files = list(self.data_directory.glob("*.json"))
        
        for json_file in json_files:
            print(f"      📄 {json_file.name}")
            try:
                with open(json_file, 'r') as f:
                    data = json.load(f)
                
                if isinstance(data, dict):
                    print(f"         Type: Dictionary with {len(data)} keys")
                    print(f"         Keys: {list(data.keys())[:10]}...")  # First 10 keys
                    
                    # Look for text content in values
                    text_found = False
                    for key, value in list(data.items())[:3]:  # Sample first 3
                        if isinstance(value, str) and len(value) > 100:
                            print(f"         Sample text from '{key}': {value[:100]}...")
                            text_found = True
                            break
                    
                    if text_found:
                        sources_found[json_file.name] = {
                            'exists': True,
                            'type': 'dictionary',
                            'entries': len(data),
                            'has_content': True
                        }
                
                elif isinstance(data, list):
                    print(f"         Type: List with {len(data)} items")
                    
                    # Sample first item
                    if data and isinstance(data[0], dict):
                        sample_keys = list(data[0].keys())
                        print(f"         Sample item keys: {sample_keys}")
                
            except Exception as e:
                print(f"         Error: {e}")
        
        # 4. Check subdirectories
        print(f"\n   4. Subdirectories Analysis:")
        subdirs = [p for p in self.data_directory.iterdir() if p.is_dir()]
        
        for subdir in subdirs:
            print(f"      📁 {subdir.name}/")
            
            # Look for text-related files in subdirectory
            text_files = []
            for ext in ['*.txt', '*.json', '*.csv']:
                text_files.extend(list(subdir.glob(ext)))
            
            if text_files:
                print(f"         Files: {[f.name for f in text_files[:5]]}...")
                
                # Sample one file
                sample_file = text_files[0]
                try:
                    if sample_file.suffix == '.json':
                        with open(sample_file, 'r') as f:
                            data = json.load(f)
                        if isinstance(data, dict) and len(data) > 0:
                            sample_key = list(data.keys())[0]
                            sample_value = data[sample_key]
                            if isinstance(sample_value, str) and len(sample_value) > 50:
                                print(f"         Sample content: {sample_value[:100]}...")
                                sources_found[f"{subdir.name}/{sample_file.name}"] = {
                                    'exists': True,
                                    'has_content': True
                                }
                except Exception as e:
                    print(f"         Error sampling {sample_file.name}: {e}")
            else:
                print(f"         No text files found")
        
        return sources_found
    
    def find_text_content_solution(self, sources_found):
        """Analyze findings and propose a solution."""
        print(f"\n💡 Text Content Solution Analysis...")
        
        print(f"   📊 Sources Summary:")
        for source, info in sources_found.items():
            has_content = info.get('has_content', False)
            status = "✅ HAS CONTENT" if has_content else "❌ NO CONTENT"
            print(f"      {source}: {status}")
        
        # Find the best source for text content
        best_sources = [source for source, info in sources_found.items() if info.get('has_content', False)]
        
        if best_sources:
            print(f"\n   🎯 Best Text Content Sources:")
            for source in best_sources:
                info = sources_found[source]
                print(f"      ✅ {source}")
                if 'text_columns' in info:
                    print(f"         Text columns: {info['text_columns']}")
                if 'entries' in info:
                    print(f"         Entries: {info['entries']:,}")
        else:
            print(f"\n   ❌ NO TEXT CONTENT SOURCES FOUND!")
            print(f"   💡 This explains why retrieval returns 0 passages")
        
        return best_sources
    
    def run_investigation(self):
        """Run the complete investigation."""
        print("🚀 Starting Text Content Location Investigation...")
        print()
        
        sources_found = self.check_all_possible_sources()
        best_sources = self.find_text_content_solution(sources_found)
        
        print(f"\n✅ INVESTIGATION COMPLETE")
        print("="*60)
        print("🎯 KEY FINDINGS:")
        
        if best_sources:
            print(f"   • Text content sources found: {len(best_sources)}")
            for source in best_sources:
                print(f"     - {source}")
            print(f"   💡 Solution: Load text content from these sources")
        else:
            print(f"   • NO text content sources found")
            print(f"   • GraphML text nodes are empty")
            print(f"   • This explains the retrieval failure")
            print(f"   💡 Need to investigate data pipeline or use different dataset")
        
        return len(best_sources) > 0

def main():
    """Main investigation function."""
    investigator = TextContentInvestigator()
    return investigator.run_investigation()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)