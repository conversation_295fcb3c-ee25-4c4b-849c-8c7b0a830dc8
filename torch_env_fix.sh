#!/bin/bash
# Torchvision compatibility environment variables
export PYTORCH_DISABLE_VERSIONED_SYMBOLS=1
export TORCH_DISABLE_TVM=1
export PYTORCH_MPS_HIGH_WATERMARK_RATIO=0.0
export OMP_NUM_THREADS=1
export TOKENIZERS_PARALLELISM=false
export MKL_NUM_THREADS=1
export PYTORCH_ENABLE_MPS_FALLBACK=1

echo "🔧 Torch environment variables configured for compatibility"
echo "   - PYTORCH_DISABLE_VERSIONED_SYMBOLS=1"
echo "   - TORCH_DISABLE_TVM=1"
echo "   - PYTORCH_MPS_HIGH_WATERMARK_RATIO=0.0"
echo "   - PYTORCH_ENABLE_MPS_FALLBACK=1"
echo "   - Threading optimized for stability"