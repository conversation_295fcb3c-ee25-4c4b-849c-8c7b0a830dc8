#!/usr/bin/env python3
"""
Quick test for OCO query with fixed embedding dimensions
"""

import os
import sys
from pathlib import Path

# Set environment and add path
os.environ["TOKENIZERS_PARALLELISM"] = "false"  
sys.path.insert(0, str(Path(__file__).parent))

from test_entity_fix_simple import setup_ollama_llm
from setup_embedding_model import setup_embedding_model
from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
from atlas_rag.retriever.inference_config import InferenceConfig
import pickle

def main():
    print("🔧 Testing OCO Query with Fixed Embeddings")
    print("="*50)
    
    # Setup
    embedding_model = setup_embedding_model()
    llm_generator = setup_ollama_llm()
    
    # Load data
    data_file = Path("import/pdf_dataset/complete_data.pkl")
    with open(data_file, 'rb') as f:
        complete_data = pickle.load(f)
    
    # Create retriever
    inference_config = InferenceConfig()
    inference_config.topk_edges = 30
    inference_config.ppr_alpha = 0.15
    inference_config.ppr_max_iter = 200
    inference_config.weight_adjust = 0.1
    
    retriever = HippoRAG2Retriever(
        llm_generator=llm_generator,
        sentence_encoder=embedding_model,
        data=complete_data,
        inference_config=inference_config  
    )
    
    print(f"✅ Retriever ready with mode: {retriever.retrieve_node_fn.__name__}")
    
    # Test OCO query
    query = "what is an OCO?"
    print(f"\n🎯 Testing: '{query}'")
    
    try:
        passages, passage_ids = retriever.retrieve(query, topN=3)
        
        print(f"📊 Retrieved {len(passages)} passages:")
        for i, (passage, pid) in enumerate(zip(passages, passage_ids), 1):
            print(f"   #{i}: {passage[:150]}...")
            
    except Exception as e:
        print(f"❌ Query failed: {e}")
        
    # Test entity matching directly
    print(f"\n🔍 Testing direct entity matching:")
    try:
        entity_scores = retriever.query2node(query, topN=5)  
        print(f"Found {len(entity_scores)} entities:")
        
        sorted_entities = sorted(entity_scores.items(), key=lambda x: x[1], reverse=True)
        for i, (entity, score) in enumerate(sorted_entities[:5], 1):
            print(f"   #{i}: {entity[:40]}... (score: {score:.4f})")
            
    except Exception as e:
        print(f"❌ Entity matching failed: {e}")

if __name__ == "__main__":
    main()