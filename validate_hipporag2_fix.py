#!/usr/bin/env python3
"""
Quick validation to confirm HippoRAG2 indexing fix is working.
Tests the specific issues that were causing "list index out of range" errors.
"""

import numpy as np
from hipporag2_pipeline import HippoRAG2Pipeline

def validate_fix():
    """Validate that the indexing fix is working correctly."""
    print("🔍 HippoRAG2 Fix Validation")
    print("=" * 40)
    
    try:
        # Initialize pipeline
        pipeline = HippoRAG2Pipeline()
        pipeline.data = pipeline.load_existing_data()
        
        # Check dimensions
        edge_embeddings_count = pipeline.data["edge_embeddings"].shape[0]
        edge_list_count = len(pipeline.data["edge_list"])
        
        print(f"📊 Data Dimensions:")
        print(f"   Edge embeddings: {edge_embeddings_count:,}")
        print(f"   Edge list: {edge_list_count:,}")
        print(f"   Match: {'✅' if edge_list_count <= edge_embeddings_count else '❌'}")
        
        # Test critical indexing scenarios
        print(f"\n🧪 Testing Critical Indexing Scenarios:")
        
        # Test 1: Direct array access at boundaries
        if edge_list_count > 0:
            try:
                last_edge_idx = edge_list_count - 1
                test_embedding = pipeline.data["edge_embeddings"][last_edge_idx]
                print(f"   ✅ Last edge embedding accessible: index {last_edge_idx}")
            except IndexError as e:
                print(f"   ❌ Last edge indexing failed: {e}")
                return False
        
        # Test 2: Setup models and retriever
        print(f"\n🤖 Testing Model Setup:")
        pipeline.setup_models()
        pipeline.initialize_hipporag2()
        print(f"   ✅ HippoRAG2Retriever initialized successfully")
        
        # Test 3: Run actual queries that previously failed
        print(f"\n🎯 Testing Previously Failing Queries:")
        
        test_queries = [
            "How to connect to 360T?",
            "Network connectivity options", 
            "Testing and go-live process"
        ]
        
        success_count = 0
        for i, query in enumerate(test_queries, 1):
            try:
                passages, passage_ids = pipeline.query_knowledge_graph(query, topN=2)
                if passages and len(passages) > 0:
                    print(f"   ✅ Query {i}: '{query}' - {len(passages)} results")
                    success_count += 1
                else:
                    print(f"   ⚠️  Query {i}: '{query}' - No results")
            except Exception as e:
                if "index" in str(e).lower() and "out of" in str(e).lower():
                    print(f"   ❌ Query {i}: '{query}' - INDEXING ERROR: {e}")
                    return False
                else:
                    print(f"   ⚠️  Query {i}: '{query}' - Other error: {e}")
        
        print(f"\n📊 Results:")
        print(f"   Successful queries: {success_count}/{len(test_queries)}")
        print(f"   No indexing errors: ✅")
        
        if success_count >= len(test_queries) * 0.7:  # 70% success rate
            print(f"\n🎉 FIX VALIDATION PASSED!")
            print(f"   - No 'list index out of range' errors")
            print(f"   - Edge embeddings complete: {edge_embeddings_count:,}")
            print(f"   - Queries working successfully")
            return True
        else:
            print(f"\n❌ FIX VALIDATION FAILED!")
            print(f"   - Low query success rate: {success_count}/{len(test_queries)}")
            return False
            
    except Exception as e:
        print(f"\n❌ FIX VALIDATION FAILED!")
        print(f"   Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = validate_fix()
    exit(0 if success else 1)