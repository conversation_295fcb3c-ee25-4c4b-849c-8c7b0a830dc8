#!/usr/bin/env python3
"""
Comprehensive HippoRAG2 Debugging Pipeline

This script provides detailed query flow visualization for the entire HippoRAG2 pipeline,
showing exactly where and why the system is failing to retrieve relevant financial content.

Features:
- Query embedding analysis
- Edge similarity search with content visualization
- LLM filtering decisions with reasoning
- PageRank propagation analysis
- Final scoring breakdown
- Content relevance assessment
"""

import os
import sys
import json
import pickle
import numpy as np
import networkx as nx
import warnings
import json_repair
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional

# Set environment variable to avoid tokenizer warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Add the project root to Python path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from atlas_rag.retriever.hipporag2 import min_max_normalize
from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
from atlas_rag.llm_generator import LLMGenerator
from atlas_rag.retriever.inference_config import InferenceConfig
from openai import OpenAI
from sentence_transformers import SentenceTransformer
from setup_embedding_model import setup_embedding_model

class HippoRAG2Debugger:
    """Comprehensive debugger for HippoRAG2 pipeline with detailed flow visualization."""
    
    def __init__(self, data_file: str = "import/pdf_dataset/complete_data.pkl"):
        self.data_file = Path(data_file)
        self.data = None
        self.llm_generator = None
        self.sentence_encoder = None
        self.debug_log = []
        
    def setup_environment(self):
        """Setup the complete HippoRAG2 environment for debugging."""
        print("🔧 COMPREHENSIVE HIPPORAG2 DEBUGGER")
        print("=" * 60)
        print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("Setting up complete debugging environment...")
        print()
        
        # Load complete data
        if not self.data_file.exists():
            print(f"❌ Complete data file not found: {self.data_file}")
            return False
        
        try:
            with open(self.data_file, 'rb') as f:
                self.data = pickle.load(f)
            print(f"✅ Complete data loaded")
            print(f"   📊 Graph: {len(self.data['KG'].nodes)} nodes, {len(self.data['KG'].edges)} edges")
            print(f"   📚 Text passages: {len(self.data.get('text_dict', {}))} entries")
            print(f"   🔗 Edge embeddings: {self.data['edge_embeddings'].shape}")
            print(f"   📝 Edge list: {len(self.data['edge_list'])} edges")
        except Exception as e:
            print(f"❌ Error loading complete data: {e}")
            return False
        
        # Setup Ollama LLM Generator
        print("\n🤖 Setting up Ollama LLM Generator...")
        try:
            ollama_url = "http://localhost:11434"
            model_name = "qwen3:30b-a3b-instruct-2507-q4_K_M"
            
            client = OpenAI(
                base_url=f"{ollama_url}/v1",
                api_key="dummy-key",
            )
            self.llm_generator = LLMGenerator(client=client, model_name=model_name)
            print(f"✅ LLM Generator created with {model_name}")
        except Exception as e:
            print(f"❌ Error creating LLM Generator: {e}")
            return False
        
        # Setup Sentence Encoder - CRITICAL: Use same model as edge embeddings!
        print("\n🔤 Setting up sentence encoder - using nvidia/NV-Embed-v2...")
        try:
            self.sentence_encoder = setup_embedding_model()
            print(f"✅ nvidia/NV-Embed-v2 encoder created - MATCHES edge embeddings!")
        except Exception as e:
            print(f"❌ Error creating nvidia/NV-Embed-v2 encoder: {e}")
            print("🔧 This is critical - edge embeddings were generated with nvidia/NV-Embed-v2")
            return False
        
        print("\n🎯 Debug environment ready!")
        return True
    
    def log_step(self, step: str, data: dict):
        """Log a debugging step with structured data."""
        self.debug_log.append({
            'timestamp': datetime.now().isoformat(),
            'step': step,
            'data': data
        })
    
    def analyze_query_embedding(self, query: str) -> np.ndarray:
        """Analyze query embedding generation."""
        print(f"\n📍 STEP 1: QUERY EMBEDDING ANALYSIS")
        print("=" * 50)
        print(f"🎯 Query: '{query}'")
        
        try:
            query_emb = self.sentence_encoder.encode([query], query_type="edge")
            print(f"✅ Query embedding generated")
            print(f"   - Shape: {query_emb.shape}")
            print(f"   - Norm: {np.linalg.norm(query_emb):.3f}")
            print(f"   - Min/Max values: [{query_emb.min():.3f}, {query_emb.max():.3f}]")
            
            self.log_step("query_embedding", {
                'query': query,
                'embedding_shape': query_emb.shape,
                'embedding_norm': float(np.linalg.norm(query_emb)),
                'embedding_range': [float(query_emb.min()), float(query_emb.max())],
                'success': True
            })
            
            return query_emb
            
        except Exception as e:
            print(f"❌ Query embedding failed: {e}")
            self.log_step("query_embedding", {
                'query': query,
                'error': str(e),
                'success': False
            })
            return None
    
    def analyze_edge_similarity_search(self, query_emb: np.ndarray, topN: int = 10) -> Tuple[np.ndarray, np.ndarray]:
        """Analyze edge similarity search with detailed edge content."""
        print(f"\n📍 STEP 2: EDGE SIMILARITY SEARCH ANALYSIS")
        print("=" * 50)
        
        edge_embeddings = self.data['edge_embeddings']
        edge_list = self.data['edge_list']
        KG = self.data['KG']
        
        try:
            # Compute similarities
            with warnings.catch_warnings():
                warnings.filterwarnings('ignore', category=RuntimeWarning)
                raw_scores = edge_embeddings @ query_emb[0].T
            
            scores = min_max_normalize(raw_scores)
            index_matrix = np.argsort(scores)[-topN:][::-1]
            
            print(f"✅ Edge similarity search completed")
            print(f"   - Total edges: {len(edge_list)}")
            print(f"   - Top {topN} similarity scores: {[f'{scores[i]:.3f}' for i in index_matrix[:5]]}")
            
            # Analyze edge content in detail
            print(f"\n🔍 TOP {topN} EDGE CANDIDATES WITH CONTENT:")
            print("-" * 50)
            
            edge_analysis = []
            for rank, index in enumerate(index_matrix):
                edge = edge_list[index]
                score = scores[index]
                
                # Get edge details
                head_node = KG.nodes[edge[0]]
                tail_node = KG.nodes[edge[1]]
                relation = KG.edges[edge]['relation']
                
                head_id = head_node['id']
                tail_id = tail_node['id']
                edge_str = [head_id, relation, tail_id]
                
                print(f"\n#{rank+1} | Score: {score:.3f} | Edge: {edge_str}")
                
                # Get actual content from nodes
                head_content = head_node.get('text', head_id)[:100] + "..." if len(head_node.get('text', head_id)) > 100 else head_node.get('text', head_id)
                tail_content = tail_node.get('text', tail_id)[:100] + "..." if len(tail_node.get('text', tail_id)) > 100 else tail_node.get('text', tail_id)
                
                print(f"   Head: {head_content}")
                print(f"   Relation: {relation}")
                print(f"   Tail: {tail_content}")
                
                # Analyze relevance to financial/MMC content
                combined_text = f"{head_content} {relation} {tail_content}".lower()
                financial_terms = ['risk', 'option', 'reversal', 'mmc', 'market', 'scenario', 'trading', 'cockpit', 'bridge']
                irrelevant_terms = ['fix', 'protocol', 'message', 'field', 'tag', 'api']
                
                financial_hits = sum(1 for term in financial_terms if term in combined_text)
                irrelevant_hits = sum(1 for term in irrelevant_terms if term in combined_text)
                relevance = "HIGH" if financial_hits >= 2 else "MEDIUM" if financial_hits >= 1 else "LOW"
                if irrelevant_hits >= 2:
                    relevance = "IRRELEVANT"
                
                print(f"   Relevance: {relevance} (financial={financial_hits}, irrelevant={irrelevant_hits})")
                
                edge_analysis.append({
                    'rank': rank + 1,
                    'score': float(score),
                    'edge': edge_str,
                    'head_content': head_content,
                    'tail_content': tail_content,
                    'relation': relation,
                    'relevance': relevance,
                    'financial_hits': financial_hits,
                    'irrelevant_hits': irrelevant_hits
                })
            
            self.log_step("edge_similarity_search", {
                'total_edges': len(edge_list),
                'topN': topN,
                'top_scores': [float(scores[i]) for i in index_matrix],
                'edge_analysis': edge_analysis,
                'success': True
            })
            
            return index_matrix, scores
            
        except Exception as e:
            print(f"❌ Edge similarity search failed: {e}")
            self.log_step("edge_similarity_search", {
                'error': str(e),
                'success': False
            })
            return None, None
    
    def analyze_llm_filtering(self, query: str, index_matrix: np.ndarray, scores: np.ndarray) -> List:
        """Analyze LLM filtering decisions with detailed reasoning."""
        print(f"\n📍 STEP 3: LLM FILTERING ANALYSIS")
        print("=" * 50)
        
        edge_list = self.data['edge_list']
        KG = self.data['KG']
        
        # Construct candidate triples (same as HippoRAG2)
        before_filter_edge_json = {'fact': []}
        for index in index_matrix:
            edge = edge_list[index]
            edge_str = [KG.nodes[edge[0]]['id'], KG.edges[edge]['relation'], KG.nodes[edge[1]]['id']]
            before_filter_edge_json['fact'].append(edge_str)
        
        triples_json = json.dumps(before_filter_edge_json, ensure_ascii=False)
        
        print(f"📤 Sending {len(before_filter_edge_json['fact'])} candidate triples to LLM")
        print(f"   Query: '{query}'")
        print(f"   Candidates: {before_filter_edge_json['fact'][:3]}...")
        
        try:
            # Call LLM filtering
            filtered_facts_raw = self.llm_generator.filter_triples_with_entity_event(query, triples_json)
            
            print(f"📥 LLM response received ({len(filtered_facts_raw)} chars)")
            print(f"   Raw response: {filtered_facts_raw}")
            
            # Parse response
            filtered_facts_dict = json_repair.loads(filtered_facts_raw)
            filtered_facts = filtered_facts_dict.get('fact', [])
            
            print(f"✅ LLM filtering completed")
            print(f"   - Input facts: {len(before_filter_edge_json['fact'])}")
            print(f"   - Filtered facts: {len(filtered_facts)}")
            print(f"   - Filtering rate: {len(filtered_facts)/len(before_filter_edge_json['fact'])*100:.1f}%")
            
            if len(filtered_facts) == 0:
                print("⚠️  CRITICAL: LLM returned 0 filtered facts - this causes fallback!")
                print("🔍 Analyzing why LLM rejected all candidates...")
                
                # Analyze each candidate to understand rejection
                print("\n🤔 REJECTION ANALYSIS:")
                for i, fact in enumerate(before_filter_edge_json['fact'][:5]):
                    combined = f"{fact[0]} {fact[1]} {fact[2]}".lower()
                    print(f"   #{i+1}: {fact}")
                    if 'fix' in combined or 'protocol' in combined:
                        print(f"        → Likely rejected: Contains FIX/protocol terms")
                    elif 'currency' in combined:
                        print(f"        → Likely rejected: Generic currency trading")
                    else:
                        print(f"        → Unclear rejection reason")
                
            else:
                print(f"📋 Filtered facts: {filtered_facts}")
            
            self.log_step("llm_filtering", {
                'query': query,
                'input_facts_count': len(before_filter_edge_json['fact']),
                'input_facts': before_filter_edge_json['fact'],
                'filtered_facts_count': len(filtered_facts),
                'filtered_facts': filtered_facts,
                'raw_response': filtered_facts_raw,
                'filtering_rate': len(filtered_facts)/len(before_filter_edge_json['fact'])*100,
                'success': True,
                'causes_fallback': len(filtered_facts) == 0
            })
            
            return filtered_facts
            
        except Exception as e:
            print(f"❌ LLM filtering failed: {e}")
            self.log_step("llm_filtering", {
                'query': query,
                'error': str(e),
                'success': False
            })
            return []
    
    def analyze_pagerank_propagation(self, query: str, filtered_facts: List) -> Dict:
        """Analyze PageRank propagation if filtered facts exist."""
        print(f"\n📍 STEP 4: PAGERANK PROPAGATION ANALYSIS")
        print("=" * 50)
        
        if len(filtered_facts) == 0:
            print("❌ No filtered facts available - PageRank will not run")
            print("🔄 System will fallback to basic text similarity")
            
            self.log_step("pagerank_propagation", {
                'query': query,
                'filtered_facts_count': 0,
                'pagerank_executed': False,
                'fallback_mode': True,
                'success': False
            })
            
            return {}
        
        edge_faiss_index = self.data['edge_faiss_index']
        edge_list = self.data['edge_list']
        KG = self.data['KG']
        
        print(f"🔄 Processing {len(filtered_facts)} filtered facts for PageRank...")
        
        try:
            # Reproduce the node_score_dict creation from HippoRAG2
            node_score_dict = {}
            edge_lookups = []
            
            for i, edge in enumerate(filtered_facts):
                print(f"   Processing fact #{i+1}: {edge}")
                
                edge_str = f'{edge[0]} {edge[1]} {edge[2]}'
                search_emb = self.sentence_encoder.encode([edge_str], query_type="search")
                D, I = edge_faiss_index.search(search_emb, 1)
                filtered_index = I[0][0]
                
                # Get the actual edge and score
                actual_edge = edge_list[filtered_index]
                head, tail = actual_edge[0], actual_edge[1]
                
                print(f"     → FAISS lookup: index {filtered_index}")
                print(f"     → Actual edge: {actual_edge}")
                print(f"     → Nodes: {head} → {tail}")
                
                # Add to node score dict (simplified scoring for now)
                score = 1.0  # In real HippoRAG2, this comes from similarity scores
                if head not in node_score_dict:
                    node_score_dict[head] = [score]
                else:
                    node_score_dict[head].append(score)
                if tail not in node_score_dict:
                    node_score_dict[tail] = [score]
                else:
                    node_score_dict[tail].append(score)
                
                edge_lookups.append({
                    'fact': edge,
                    'edge_string': edge_str,
                    'faiss_index': int(filtered_index),
                    'actual_edge': [int(head), int(tail)],
                    'score': score
                })
            
            # Average the scores
            for node in node_score_dict:
                node_score_dict[node] = np.mean(node_score_dict[node])
            
            print(f"✅ Node scoring completed")
            print(f"   - Query nodes for PageRank: {len(node_score_dict)}")
            print(f"   - Sample nodes: {list(node_score_dict.keys())[:3]}")
            
            # Now PageRank would run with these nodes as personalization
            print(f"\n🔄 PageRank would propagate from {len(node_score_dict)} seed nodes")
            print(f"   This would score all {len(KG.nodes)} nodes in the graph")
            
            self.log_step("pagerank_propagation", {
                'query': query,
                'filtered_facts_count': len(filtered_facts),
                'edge_lookups': edge_lookups,
                'seed_nodes_count': len(node_score_dict),
                'seed_nodes': list(node_score_dict.keys()),
                'pagerank_executed': True,
                'fallback_mode': False,
                'success': True
            })
            
            return node_score_dict
            
        except Exception as e:
            print(f"❌ PageRank analysis failed: {e}")
            self.log_step("pagerank_propagation", {
                'query': query,
                'error': str(e),
                'success': False
            })
            return {}
    
    def debug_complete_pipeline(self, query: str, topN: int = 10) -> Dict:
        """Run complete pipeline debugging with comprehensive analysis."""
        print(f"\n🎯 COMPLETE PIPELINE DEBUG FOR QUERY")
        print("=" * 60)
        print(f"Query: '{query}'")
        print(f"Expected: Financial content (risk-reversal, MMC scenarios)")
        print(f"Debugging TopN={topN} edge candidates")
        print()
        
        self.debug_log = []  # Reset debug log
        
        # Step 1: Query embedding
        query_emb = self.analyze_query_embedding(query)
        if query_emb is None:
            return {'success': False, 'error': 'Query embedding failed'}
        
        # Step 2: Edge similarity search
        index_matrix, scores = self.analyze_edge_similarity_search(query_emb, topN)
        if index_matrix is None:
            return {'success': False, 'error': 'Edge similarity search failed'}
        
        # Step 3: LLM filtering
        filtered_facts = self.analyze_llm_filtering(query, index_matrix, scores)
        
        # Step 4: PageRank propagation
        node_score_dict = self.analyze_pagerank_propagation(query, filtered_facts)
        
        # Step 5: Final analysis
        print(f"\n📍 STEP 5: FINAL PIPELINE ANALYSIS")
        print("=" * 50)
        
        if len(filtered_facts) == 0:
            print("❌ PIPELINE FAILURE IDENTIFIED:")
            print("   → Edge similarity search returns irrelevant edges")
            print("   → LLM correctly rejects all irrelevant edges")
            print("   → System falls back to basic text similarity")
            print("   → No graph traversal or PageRank benefits")
            
            failure_analysis = {
                'failure_point': 'edge_similarity_search',
                'root_cause': 'Edge embeddings not capturing financial semantic relationships',
                'evidence': 'LLM correctly identifies edges as irrelevant to query',
                'impact': 'Complete loss of graph-based retrieval benefits'
            }
        else:
            print("✅ PIPELINE SUCCESS:")
            print(f"   → Found {len(filtered_facts)} relevant edges")
            print(f"   → Generated {len(node_score_dict)} seed nodes for PageRank")
            print("   → Graph traversal would proceed normally")
            
            failure_analysis = {
                'failure_point': None,
                'success': True,
                'relevant_edges': len(filtered_facts),
                'seed_nodes': len(node_score_dict)
            }
        
        self.log_step("final_analysis", failure_analysis)
        
        return {
            'success': len(filtered_facts) > 0,
            'query': query,
            'debug_log': self.debug_log,
            'analysis': failure_analysis
        }

def main():
    """Main debugging function."""
    print("🔍 COMPREHENSIVE HIPPORAG2 PIPELINE DEBUGGER")
    print("=" * 65)
    print("Providing detailed query flow visualization to identify exact failure points")
    print()
    
    # Initialize debugger
    debugger = HippoRAG2Debugger()
    
    # Setup environment
    if not debugger.setup_environment():
        print("❌ Failed to setup debugging environment")
        return False
    
    # Test queries
    test_queries = [
        "what is a risk reversal option, and how to create one in Bridge?",
        "how to create scenarios in mmc?",
        "Bridge trading platform features"
    ]
    
    results = []
    for query in test_queries:
        print(f"\n{'='*60}")
        print(f"DEBUGGING QUERY: '{query}'")
        print(f"{'='*60}")
        
        result = debugger.debug_complete_pipeline(query, topN=10)
        results.append(result)
        
        if not result['success']:
            print(f"\n🚨 FAILURE DETECTED FOR QUERY: '{query}'")
            if 'analysis' in result:
                analysis = result['analysis']
                print(f"   Failure Point: {analysis.get('failure_point', 'Unknown')}")
                print(f"   Root Cause: {analysis.get('root_cause', 'Unknown')}")
                print(f"   Impact: {analysis.get('impact', 'Unknown')}")
        
        print(f"\n📊 Debug log saved with {len(result.get('debug_log', []))} steps")
    
    # Overall summary
    print(f"\n{'='*65}")
    print("🎯 DEBUGGING SUMMARY")
    print(f"{'='*65}")
    
    successful_queries = sum(1 for r in results if r['success'])
    print(f"📊 Successful queries: {successful_queries}/{len(test_queries)}")
    
    if successful_queries == 0:
        print("❌ CRITICAL ISSUE: All queries failing")
        print("💡 Primary fix needed: Edge embedding quality for financial domain")
    elif successful_queries < len(test_queries):
        print("⚠️  PARTIAL ISSUE: Some queries failing")
        print("💡 Domain-specific embedding improvements needed")
    else:
        print("✅ All queries successful - system working correctly")
    
    return successful_queries > 0

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)