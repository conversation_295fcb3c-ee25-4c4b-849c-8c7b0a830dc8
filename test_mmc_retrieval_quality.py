#!/usr/bin/env python3
"""
Test MMC Query Retrieval Quality

This script analyzes why MMC-related queries return irrelevant results
despite the diagnostic showing MMC content exists in the knowledge graph.

We'll test the full retrieval pipeline and analyze semantic similarity scores.
"""

from hipporag2_pipeline import HippoRAG2Pipeline
import numpy as np

def test_mmc_query_detailed():
    """Test MMC query with detailed analysis of retrieval pipeline."""
    
    print("🔍 DETAILED MMC QUERY ANALYSIS")
    print("="*60)
    
    # Setup pipeline
    pipeline = HippoRAG2Pipeline()
    pipeline.data = pipeline.load_existing_data()
    pipeline.setup_models()
    pipeline.initialize_hipporag2()
    
    # Test the original failing query
    mmc_query = "What are the step of creating a new scenario in MMC?"
    
    print(f"🤔 Testing query: '{mmc_query}'")
    print(f"   Expected: Information about MMC scenario creation steps")
    
    # Step 1: Test query embedding
    print(f"\n📊 Step 1: Query Embedding Analysis")
    query_emb = pipeline.sentence_encoder.encode([mmc_query], query_type="passage")
    print(f"   Query embedding shape: {query_emb.shape}")
    print(f"   Query embedding norm: {np.linalg.norm(query_emb):.6f}")
    
    # Step 2: Test individual retrieval components
    print(f"\n📊 Step 2: Component-wise Retrieval Testing")
    
    # Test edge retrieval
    print(f"\n   🔗 Edge Retrieval (query2edge):")
    edge_results = pipeline.hipporag2_retriever.query2edge(mmc_query, topN=5)
    print(f"      Found {len(edge_results.get('fact', []))} edge facts:")
    for i, fact in enumerate(edge_results.get('fact', [])[:3], 1):
        print(f"         {i}. {' | '.join(fact)}")
    
    # Test node retrieval  
    print(f"\n   🎯 Node Retrieval (query2node):")
    node_results = pipeline.hipporag2_retriever.query2node(mmc_query, topN=5)
    print(f"      Found {len(node_results)} nodes:")
    for i, (node, score) in enumerate(list(node_results.items())[:3], 1):
        node_name = pipeline.data["KG"].nodes[node].get('name', node)
        print(f"         {i}. {node_name} (score: {score:.4f})")
    
    # Test passage retrieval
    print(f"\n   📄 Passage Retrieval (query2passage):")
    passage_results = pipeline.hipporag2_retriever.query2passage(mmc_query)
    # Sort by score to get top results
    sorted_passages = sorted(passage_results.items(), key=lambda x: x[1], reverse=True)[:5]
    print(f"      Found {len(passage_results)} passages, top 5:")
    for i, (passage_id, score) in enumerate(sorted_passages, 1):
        passage_text = pipeline.data["text_dict"].get(passage_id, "No text found")[:100]
        print(f"         {i}. Score: {score:.4f} | {passage_text}...")
    
    # Step 3: Test full retrieval pipeline
    print(f"\n📊 Step 3: Full Retrieval Pipeline")
    passages, passage_ids = pipeline.query_knowledge_graph(mmc_query, topN=3)
    
    print(f"   Final retrieved passages ({len(passages)}):")
    for i, (passage, pid) in enumerate(zip(passages, passage_ids), 1):
        print(f"      {i}. [{pid}] {passage[:200]}...")
        
        # Check if this passage actually contains MMC-related content
        mmc_terms = ["MMC", "Market Management Console", "scenario", "create", "step"]
        matches = sum(1 for term in mmc_terms if term.lower() in passage.lower())
        print(f"         MMC relevance: {matches}/{len(mmc_terms)} terms matched")
    
    # Step 4: Test known good MMC content
    print(f"\n📊 Step 4: Testing Known MMC Content")
    
    # Search for nodes/text that definitely contain MMC
    mmc_nodes = []
    mmc_texts = []
    
    for node_id in pipeline.data["KG"].nodes():
        node_name = pipeline.data["KG"].nodes[node_id].get('name', '')
        if 'MMC' in node_name:
            mmc_nodes.append((node_id, node_name))
            
    for text_id, text_content in pipeline.data["text_dict"].items():
        if 'MMC' in text_content and 'scenario' in text_content.lower():
            mmc_texts.append((text_id, text_content[:200]))
            
    print(f"   Found {len(mmc_nodes)} MMC nodes:")  
    for i, (node_id, name) in enumerate(mmc_nodes[:5], 1):
        print(f"      {i}. {name}")
        
    print(f"   Found {len(mmc_texts)} MMC texts with 'scenario':")
    for i, (text_id, content) in enumerate(mmc_texts[:3], 1):
        print(f"      {i}. [{text_id}] {content}...")
    
    # Step 5: Test semantic similarity directly
    print(f"\n📊 Step 5: Direct Semantic Similarity Testing")
    
    if mmc_texts:
        print(f"   Testing direct similarity with known MMC content...")
        
        # Get embeddings for a known MMC text
        sample_mmc_text = mmc_texts[0][1]
        mmc_text_emb = pipeline.sentence_encoder.encode([sample_mmc_text], query_type="passage")
        
        # Calculate similarity
        similarity = np.dot(query_emb[0], mmc_text_emb[0])
        print(f"   Direct similarity with MMC text: {similarity:.4f}")
        print(f"   MMC text sample: {sample_mmc_text[:150]}...")
        
        # Compare with what was actually retrieved
        if passages:
            top_passage_emb = pipeline.sentence_encoder.encode([passages[0]], query_type="passage")
            top_similarity = np.dot(query_emb[0], top_passage_emb[0])
            print(f"   Similarity with top retrieved passage: {top_similarity:.4f}")
            print(f"   Top passage: {passages[0][:150]}...")

def test_different_mmc_queries():
    """Test various MMC-related queries to understand the pattern."""
    
    print(f"\n🧪 TESTING DIFFERENT MMC QUERIES")
    print("="*50)
    
    # Setup pipeline
    pipeline = HippoRAG2Pipeline()  
    pipeline.data = pipeline.load_existing_data()
    pipeline.setup_models()
    pipeline.initialize_hipporag2()
    
    test_queries = [
        "MMC scenario creation",
        "Market Management Console",
        "How to create scenario in MMC",
        "MMC scenario parameters",
        "MMC scenario activation",
        "360T MMC"  # Mix of MMC with known domain content
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🤔 Query {i}: '{query}'")
        
        try:
            passages, passage_ids = pipeline.query_knowledge_graph(query, topN=2)
            
            if passages:
                print(f"   ✅ Retrieved {len(passages)} passages")
                
                # Quick relevance check
                for j, passage in enumerate(passages, 1):
                    mmc_mentions = passage.lower().count('mmc')
                    scenario_mentions = passage.lower().count('scenario')
                    print(f"      {j}. MMC: {mmc_mentions}, Scenario: {scenario_mentions} | {passage[:100]}...")
            else:
                print(f"   ❌ No passages retrieved")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def main():
    """Main testing function."""
    
    try:
        print("🚀 MMC Retrieval Quality Analysis")
        print("="*60)
        
        # Detailed analysis of the failing query
        test_mmc_query_detailed()
        
        # Test variations to understand the pattern
        test_different_mmc_queries()
        
        print(f"\n💡 ANALYSIS COMPLETE")
        print("="*50)
        print("Check the results above to understand why MMC queries")
        print("may be returning irrelevant results despite MMC content existing.")
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()