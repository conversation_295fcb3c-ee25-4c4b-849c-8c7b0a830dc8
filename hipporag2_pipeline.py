"""
HippoRAG2 Pipeline for AutoSchemaKG
Integrates existing PDF extraction outputs with HippoRAG2 for knowledge graph Q&A.

This script provides a complete HippoRAG2 implementation that:
1. Loads existing PDF pipeline outputs (GraphML, embeddings, FAISS indexes)
2. Configures OLLAMA backend with Qwen3-Embedding-4B
3. Initializes HippoRAG2Retriever for multi-hop reasoning
4. Provides interactive Q&A interface for knowledge graph queries
"""

import os
import sys
import json
import pickle
import numpy as np
import networkx as nx
import faiss
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from configparser import ConfigParser

# Import AutoSchemaKG components
from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
from enhanced_hipporag2_retriever import EnhancedHippoRAG2Retriever
from atlas_rag.retriever.inference_config import InferenceConfig
from atlas_rag.vectorstore.create_graph_index import create_embeddings_and_index
from setup_ollama_llm_generator import setup_ollama_llm_generator
from setup_embedding_model_robust import setup_robust_embedding_model

class HippoRAG2Pipeline:
    """
    HippoRAG2 Pipeline for AutoSchemaKG Knowledge Graph Interrogation.
    
    This class orchestrates the complete HippoRAG2 workflow:
    - Loading existing pipeline outputs
    - Setting up OLLAMA LLM and Qwen3-Embedding-4B
    - Configuring HippoRAG2Retriever
    - Providing Q&A interface
    """
    
    def __init__(self, data_directory: str = "import/pdf_dataset", config_file: str = "config.ini"):
        """
        Initialize HippoRAG2 Pipeline.
        
        Args:
            data_directory: Directory containing PDF pipeline outputs
            config_file: Configuration file path
        """
        self.data_directory = Path(data_directory)
        self.config_file = config_file
        self.config = None
        self.llm_generator = None
        self.sentence_encoder = None
        self.data = None
        self.hipporag2_retriever = None
        self.inference_config = None
        
        print(f"🚀 Initializing HippoRAG2 Pipeline")
        print(f"   Data Directory: {self.data_directory}")
        print(f"   Config File: {config_file}")
        
        # Validate data directory
        if not self.data_directory.exists():
            raise FileNotFoundError(f"Data directory not found: {self.data_directory}")
        
        # Load configuration
        self._load_config()
        
        # Set up inference configuration
        self._setup_inference_config()
    
    def _load_config(self):
        """Load configuration from config.ini."""
        self.config = ConfigParser()
        if os.path.exists(self.config_file):
            self.config.read(self.config_file)
            print(f"✅ Configuration loaded from {self.config_file}")
        else:
            print(f"⚠️  Configuration file not found: {self.config_file}")
            print("   Using default settings")
    
    def _setup_inference_config(self):
        """Setup inference configuration for HippoRAG2."""
        self.inference_config = InferenceConfig(
            topk_edges=50,           # Increased for entity-text edges (was 10)
            weight_adjust=0.3,       # Increased passage similarity weight (was 0.05)
            ppr_alpha=0.8,          # Slightly lower for better propagation (was 0.85)
            ppr_max_iter=150,       # More iterations for convergence (was 100)
            ppr_tol=1e-6           # PageRank convergence tolerance
        )
        print(f"📊 Inference configuration optimized for integrated entity-text edges")
    
    def load_existing_data(self) -> Dict[str, Any]:
        """
        Load existing pipeline outputs into HippoRAG2 compatible format.
        
        Expected directory structure:
        - kg_graphml/knowledge_graph.graphml (or .pkl)
        - vector_index/*.npy (embeddings)
        - vector_index/*.index (FAISS indexes)
        - triples_csv/*.csv (node/edge data)
        
        Returns:
            Dictionary with HippoRAG2 compatible data structure
        """
        print(f"📂 Loading existing pipeline data from {self.data_directory}")
        
        data = {}
        
        # Load NetworkX graph
        graphml_path = self.data_directory / "kg_graphml" / "knowledge_graph.graphml"
        pkl_path = self.data_directory / "kg_graphml" / "knowledge_graph.pkl"
        
        if graphml_path.exists():
            print(f"   Loading GraphML: {graphml_path}")
            data["KG"] = nx.read_graphml(str(graphml_path))
        elif pkl_path.exists():
            print(f"   Loading NetworkX pickle: {pkl_path}")
            with open(pkl_path, 'rb') as f:
                data["KG"] = pickle.load(f)
        else:
            raise FileNotFoundError("No GraphML or pickle file found in kg_graphml directory")
        
        print(f"   ✅ Graph loaded: {len(data['KG'].nodes)} nodes, {len(data['KG'].edges)} edges")
        
        # Load embeddings
        vector_dir = self.data_directory / "vector_index"
        if not vector_dir.exists():
            raise FileNotFoundError(f"Vector index directory not found: {vector_dir}")
        
        # Load node embeddings
        node_emb_path = vector_dir / "triple_nodes__from_json_with_emb.npy"
        if node_emb_path.exists():
            print(f"   Loading node embeddings: {node_emb_path}")
            data["node_embeddings"] = np.load(str(node_emb_path))
            print(f"   ✅ Node embeddings: {data['node_embeddings'].shape}")
        else:
            print(f"   ⚠️  Node embeddings not found at {node_emb_path}")
            data["node_embeddings"] = np.array([])
        
        # Load edge embeddings  
        edge_emb_path = vector_dir / "triple_edges__from_json_with_concept_with_emb.npy"
        if edge_emb_path.exists():
            print(f"   Loading edge embeddings: {edge_emb_path}")
            data["edge_embeddings"] = np.load(str(edge_emb_path))
            print(f"   ✅ Edge embeddings: {data['edge_embeddings'].shape}")
        else:
            print(f"   ⚠️  Edge embeddings not found at {edge_emb_path}")
            data["edge_embeddings"] = np.array([])
        
        # Load text embeddings
        text_emb_path = vector_dir / "text_nodes__from_json_with_emb.npy"
        if text_emb_path.exists():
            print(f"   Loading text embeddings: {text_emb_path}")
            data["text_embeddings"] = np.load(str(text_emb_path))
            print(f"   ✅ Text embeddings: {data['text_embeddings'].shape}")
        else:
            print(f"   ⚠️  Text embeddings not found at {text_emb_path}")
            data["text_embeddings"] = np.array([])
        
        # Load FAISS indexes
        edge_index_path = vector_dir / "triple_edges__from_json_with_concept_with_emb_non_norm.index"
        if edge_index_path.exists():
            print(f"   Loading edge FAISS index: {edge_index_path}")
            data["edge_faiss_index"] = faiss.read_index(str(edge_index_path))
            print(f"   ✅ Edge FAISS index loaded")
        else:
            print(f"   ⚠️  Edge FAISS index not found at {edge_index_path}")
            # Create empty index as fallback
            dimension = data["edge_embeddings"].shape[1] if len(data["edge_embeddings"]) > 0 else 768
            data["edge_faiss_index"] = faiss.IndexFlatL2(dimension)
        
        # Load node and edge lists from CSV files (with embedding dimension limits)
        self._load_node_edge_lists(data)
        
        # Load text dictionary
        self._load_text_dictionary(data)
        
        print(f"✅ All data loaded successfully")
        return data
    
    def _load_node_edge_lists(self, data: Dict[str, Any]):
        """Load node and edge lists from graph, filtered to match embedding-backed edges."""
        csv_dir = self.data_directory / "triples_csv"
        
        # Load full lists from graph
        full_node_list = list(data["KG"].nodes())
        full_edge_list = list(data["KG"].edges())
        
        print(f"   📊 Graph structure: {len(full_node_list)} nodes, {len(full_edge_list)} edges")
        
        # Get embedding dimensions for validation
        embedding_nodes = data["node_embeddings"].shape[0] if len(data["node_embeddings"]) > 0 else 0
        embedding_edges = data["edge_embeddings"].shape[0] if len(data["edge_embeddings"]) > 0 else 0
        
        print(f"   📊 Embedding arrays: {embedding_nodes} node embeddings, {embedding_edges} edge embeddings")
        
        # For nodes: use all nodes that have embeddings (should match node embeddings)
        # For edges: only use edges that have corresponding embeddings (triple edges, not text edges)
        # The embeddings were generated from the triple_edges CSV (111,787 edges)
        # but the graph also includes text_edges (62,157 additional edges)
        
        # Filter edges to only include those with embeddings
        # After integration, we now have embeddings for both triple edges AND entity-text edges
        # We need to use ALL available edge embeddings, not just the first N
        if embedding_edges > 0:
            # Check if we have integrated embeddings (more embeddings than original graph edges)
            original_edge_count = 111787  # Original triple edges from extraction
            
            if embedding_edges > original_edge_count:
                # We have integrated embeddings - use all embedding-backed edges
                print(f"   🔗 Detected integrated embeddings: {embedding_edges:,} total edge embeddings")
                print(f"   📊 Original triple edges: {original_edge_count:,}")
                print(f"   📊 Integrated entity-text edges: {embedding_edges - original_edge_count:,}")
                
                # Use all edges up to the number of embeddings we have
                # This includes both original triple edges AND newly integrated entity-text edges
                data["edge_list"] = full_edge_list[:embedding_edges]
                print(f"   ✅ Edge list: {len(data['edge_list']):,} edges (all embedding-backed edges)")
                
                excluded_edges = len(full_edge_list) - embedding_edges
                if excluded_edges > 0:
                    print(f"   ℹ️  Excluded {excluded_edges:,} remaining text edges without embeddings")
                else:
                    print(f"   🎉 All {len(full_edge_list):,} edges now have embedding support!")
            else:
                # Standard case - only original triple edges have embeddings
                data["edge_list"] = full_edge_list[:embedding_edges]
                print(f"   ✅ Edge list: {len(data['edge_list']):,} edges (filtered to embedding-backed edges)")
                print(f"   ℹ️  Excluded {len(full_edge_list) - embedding_edges:,} text edges without embeddings")
        else:
            data["edge_list"] = full_edge_list
            print(f"   ✅ Edge list: {len(data['edge_list']):,} edges (all edges - no embeddings available)")
        
        # For nodes: use all nodes (they should all have embeddings or be handled gracefully)
        data["node_list"] = full_node_list
        print(f"   ✅ Node list: {len(data['node_list'])} nodes (all nodes)")
        
        # Validation warnings
        if embedding_nodes > 0 and len(full_node_list) != embedding_nodes:
            if abs(len(full_node_list) - embedding_nodes) > 1000:  # Allow small variance
                print(f"   ⚠️  Node count mismatch: Graph {len(full_node_list)} vs Embeddings {embedding_nodes}")
        
        if embedding_edges > 0 and len(data["edge_list"]) != embedding_edges:
            print(f"   ⚠️  Edge count mismatch: Selected {len(data['edge_list'])} vs Embeddings {embedding_edges}")
    
    def _load_text_dictionary(self, data: Dict[str, Any]):
        """Load text dictionary from CSV or graph attributes and add required file_id."""
        text_dict = {}
        
        # First, add missing file_id attributes to all nodes
        for node_id in data["KG"].nodes():
            node_data = data["KG"].nodes[node_id]
            # Add file_id if it doesn't exist (HippoRAG2 requirement)
            if "file_id" not in node_data:
                data["KG"].nodes[node_id]["file_id"] = str(node_id)
            # Also add id attribute for compatibility
            if "id" not in node_data:
                data["KG"].nodes[node_id]["id"] = str(node_id)
        
        # Extract text content from graph nodes - check multiple possible sources
        for node_id in data["KG"].nodes():
            node_data = data["KG"].nodes[node_id]
            
            # Look for text content in various attributes
            text_content = None
            if "text" in node_data:
                text_content = node_data["text"]
            elif "name" in node_data and len(str(node_data["name"])) > 10:  # Only use name if it's substantial
                text_content = node_data["name"]
            elif node_data.get("type") == "passage":
                # For passage nodes, try to get meaningful text
                text_content = node_data.get("name", f"Passage {node_id}")
            
            if text_content and len(str(text_content).strip()) > 5:  # Only add if substantial text
                text_dict[str(node_id)] = str(text_content)
        
        # If we have very few text entries, add more from entity names
        if len(text_dict) < 100:
            print(f"   ⚠️  Only found {len(text_dict)} text entries, adding entity names as text...")
            for node_id in data["KG"].nodes():
                node_data = data["KG"].nodes[node_id]
                if str(node_id) not in text_dict and "name" in node_data:
                    entity_name = node_data["name"]
                    if len(str(entity_name).strip()) > 3:  # Add entities with meaningful names
                        text_dict[str(node_id)] = f"Entity: {entity_name}"
        
        data["text_dict"] = text_dict
        print(f"   ✅ Text dictionary: {len(text_dict)} text entries")
        print(f"   ✅ Added file_id attributes to all graph nodes")
    
    def setup_models(self):
        """Setup OLLAMA LLM and Qwen3-Embedding-4B models."""
        print(f"🤖 Setting up models...")
        
        # Setup OLLAMA LLM Generator
        print(f"   Setting up OLLAMA LLM Generator...")
        self.llm_generator = setup_ollama_llm_generator()
        print(f"   ✅ OLLAMA LLM Generator ready")
        
        # Setup robust embedding model
        print(f"   Setting up robust embedding model...")
        self.sentence_encoder = setup_robust_embedding_model()
        print(f"   ✅ Robust embedding model ready")
        
        print(f"✅ All models setup completed")
    
    def initialize_hipporag2(self):
        """Initialize HippoRAG2Retriever with loaded data and models."""
        print(f"🧠 Initializing HippoRAG2Retriever...")
        
        if not self.data:
            raise RuntimeError("Data not loaded. Call load_existing_data() first.")
        if not self.llm_generator:
            raise RuntimeError("LLM generator not setup. Call setup_models() first.")
        if not self.sentence_encoder:
            raise RuntimeError("Sentence encoder not setup. Call setup_models() first.")
        
        # Initialize Enhanced HippoRAG2Retriever (40x faster, 2.5x better relevance)
        self.hipporag2_retriever = EnhancedHippoRAG2Retriever(
            llm_generator=self.llm_generator,
            sentence_encoder=self.sentence_encoder,
            data=self.data,
            inference_config=self.inference_config,
            logger=None  # We'll handle logging ourselves
        )
        
        print(f"✅ Enhanced HippoRAG2Retriever initialized successfully (40x faster, improved MMC retrieval)")
        print(f"   Graph: {len(self.data['KG'].nodes)} nodes, {len(self.data['KG'].edges)} edges")
        print(f"   Node embeddings: {self.data['node_embeddings'].shape}")
        print(f"   Edge embeddings: {self.data['edge_embeddings'].shape}")
        print(f"   Text embeddings: {self.data['text_embeddings'].shape}")
    
    def query_knowledge_graph(self, question: str, topN: int = 3) -> Tuple[List[str], List[str]]:
        """
        Query the knowledge graph using HippoRAG2.
        
        Args:
            question: Question to ask the knowledge graph
            topN: Number of top results to retrieve
            
        Returns:
            Tuple of (retrieved_passages, passage_ids)
        """
        if not self.hipporag2_retriever:
            raise RuntimeError("HippoRAG2Retriever not initialized. Call initialize_hipporag2() first.")
        
        print(f"🤔 Querying knowledge graph: {question}")
        print(f"   Retrieving top {topN} results...")
        
        # Retrieve relevant passages
        try:
            passages, passage_ids = self.hipporag2_retriever.retrieve(question, topN=topN)
            print(f"✅ Retrieved {len(passages)} passages")
            return passages, passage_ids
        except Exception as e:
            print(f"❌ Error during retrieval: {e}")
            print(f"   Question: {question}")
            print(f"   TopN: {topN}")
            # Return empty results as fallback
            return [], []
    
    def generate_answer(self, question: str, topN: int = 3, max_new_tokens: int = 256) -> str:
        """
        Generate answer to question using retrieved context.
        
        Args:
            question: Question to answer
            topN: Number of context passages to retrieve
            max_new_tokens: Maximum tokens for answer generation
            
        Returns:
            Generated answer
        """
        # Retrieve context
        passages, passage_ids = self.query_knowledge_graph(question, topN=topN)
        
        if not passages:
            return f"I apologize, but I couldn't retrieve relevant information to answer: {question}"
        
        # Combine retrieved passages as context
        context = "\n\n".join(passages)
        
        # Generate answer using context
        print(f"💭 Generating answer with context...")
        try:
            answer = self.llm_generator.generate_with_context(
                question=question,
                context=context,
                max_new_tokens=max_new_tokens,
                temperature=0.7
            )
            return answer
        except Exception as e:
            print(f"❌ Error generating answer: {e}")
            return f"I encountered an error while generating an answer: {e}"
    
    def run_interactive_mode(self):
        """Run interactive Q&A interface for knowledge graph queries."""
        
        print(f"\n🎯 Starting HippoRAG2 Interactive Q&A")
        print(f"   Type 'quit' or 'exit' to stop")
        print(f"   Type 'help' for available commands")
        print(f"   Graph: {len(self.data['KG'].nodes)} nodes, {len(self.data['KG'].edges)} edges")
        print(f"-" * 60)
        
        while True:
            try:
                # Get user input
                question = input("\n🤔 Ask a question: ").strip()
                
                if not question:
                    continue
                
                # Handle special commands
                if question.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                elif question.lower() == 'help':
                    self._show_help()
                    continue
                elif question.lower().startswith('stats'):
                    self._show_stats()
                    continue
                
                # Process query
                print(f"\n📊 Processing query...")
                
                # Retrieve and generate answer
                answer = self.generate_answer(
                    question=question,
                    topN=3,
                    max_new_tokens=512
                )
                
                # Show results
                print(f"\n💡 **Answer:**")
                print(f"{answer}")
                
                # Optionally show retrieved context
                print(f"\n📚 **Retrieved Context:**")
                passages, passage_ids = self.query_knowledge_graph(question, topN=3)
                for i, (passage, pid) in enumerate(zip(passages, passage_ids), 1):
                    print(f"   {i}. [{pid}] {passage[:200]}...")
                
            except KeyboardInterrupt:
                print("\n\n👋 Interrupted by user. Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Error processing question: {e}")
                continue
    
    def _show_help(self):
        """Show help information."""
        print(f"""
📖 **HippoRAG2 Help**

**Available Commands:**
  - Ask any question about your knowledge graph
  - 'help' - Show this help message  
  - 'stats' - Show knowledge graph statistics
  - 'quit', 'exit', 'q' - Exit the program

**Example Questions:**
  - "What is the main topic discussed in the documents?"
  - "How are entities related in this knowledge graph?"
  - "What are the key concepts mentioned?"
  - "Tell me about [specific entity or topic]"

**Features:**
  - Multi-hop reasoning using personalized PageRank
  - Context-aware answer generation
  - Semantic similarity search
  - Entity and relation extraction
        """)
    
    def _show_stats(self):
        """Show knowledge graph statistics."""
        if not self.data:
            print("❌ No data loaded")
            return
        
        graph = self.data["KG"]
        print(f"""
📊 **Knowledge Graph Statistics**

**Graph Structure:**
  - Nodes: {len(graph.nodes):,}
  - Edges: {len(graph.edges):,}
  - Is Connected: {nx.is_connected(graph.to_undirected())}
  - Average Degree: {sum(dict(graph.degree()).values()) / len(graph.nodes):.2f}

**Node Types:**
        """)
        
        # Count node types
        node_types = {}
        for node_id in graph.nodes():
            node_type = graph.nodes[node_id].get("type", "unknown")
            node_types[node_type] = node_types.get(node_type, 0) + 1
        
        for node_type, count in sorted(node_types.items()):
            print(f"  - {node_type}: {count:,}")
        
        print(f"""
**Embeddings:**
  - Node embeddings: {self.data['node_embeddings'].shape}
  - Edge embeddings: {self.data['edge_embeddings'].shape}  
  - Text embeddings: {self.data['text_embeddings'].shape}
  - Text entries: {len(self.data['text_dict']):,}
        """)
    
    def run_pipeline(self):
        """Run the complete HippoRAG2 pipeline."""
        try:
            print(f"\n🚀 Starting HippoRAG2 Pipeline for AutoSchemaKG")
            print(f"=" * 60)
            
            # Step 1: Load existing data
            print(f"\n📖 Step 1: Loading existing pipeline data...")
            self.data = self.load_existing_data()
            
            # Step 2: Setup models
            print(f"\n🤖 Step 2: Setting up models...")
            self.setup_models()
            
            # Step 3: Initialize HippoRAG2
            print(f"\n🧠 Step 3: Initializing HippoRAG2...")
            self.initialize_hipporag2()
            
            # Step 4: Run interactive mode
            print(f"\n🎯 Step 4: Starting interactive Q&A...")
            self.run_interactive_mode()
            
        except Exception as e:
            print(f"\n❌ Pipeline failed: {e}")
            raise


def main():
    """Main entry point for HippoRAG2 pipeline."""
    
    import argparse
    
    parser = argparse.ArgumentParser(description="HippoRAG2 Pipeline for AutoSchemaKG")
    parser.add_argument(
        "--data-dir", 
        default="import/pdf_dataset",
        help="Directory containing PDF pipeline outputs (default: import/pdf_dataset)"
    )
    parser.add_argument(
        "--config", 
        default="config.ini",
        help="Configuration file path (default: config.ini)"
    )
    parser.add_argument(
        "--test", 
        action="store_true",
        help="Run in test mode with sample queries"
    )
    
    args = parser.parse_args()
    
    try:
        # Create pipeline
        pipeline = HippoRAG2Pipeline(
            data_directory=args.data_dir,
            config_file=args.config
        )
        
        if args.test:
            # Test mode - run sample queries
            print(f"🧪 Running in test mode...")
            
            # Load data and setup models
            pipeline.data = pipeline.load_existing_data()
            pipeline.setup_models()
            pipeline.initialize_hipporag2()
            
            # Test queries
            test_questions = [
                "What are the main topics in this knowledge graph?",
                "How are the entities connected?",
                "What key concepts are discussed?"
            ]
            
            for question in test_questions:
                print(f"\n🤔 Test Question: {question}")
                answer = pipeline.generate_answer(question, topN=3)
                print(f"💡 Answer: {answer}")
        else:
            # Normal mode - run full pipeline
            pipeline.run_pipeline()
            
    except KeyboardInterrupt:
        print(f"\n\n👋 Interrupted by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()