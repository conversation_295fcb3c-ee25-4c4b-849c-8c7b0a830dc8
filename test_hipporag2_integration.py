"""
Integration tests for HippoRAG2 pipeline with OLLAMA backend.

This test suite validates the complete HippoRAG2 integration including:
- OLLAMA LLM generator setup and functionality
- Qwen3-Embedding-4B embedding model integration
- Data loading from existing PDF pipeline outputs
- HippoRAG2Retriever initialization and query processing
- End-to-end knowledge graph Q&A functionality
"""

import pytest
import os
import tempfile
import shutil
import json
import numpy as np
import networkx as nx
import faiss
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from configparser import ConfigParser

# Import modules to test
from setup_ollama_llm_generator import setup_ollama_llm_generator, OllamaLLMGenerator
from setup_embedding_model import setup_qwen_embedding_model
from hipporag2_pipeline import HippoRAG2Pipeline


class TestOllamaLLMGenerator:
    """Test OLLAMA LLM Generator functionality."""
    
    @pytest.fixture
    def mock_ollama_response(self):
        """Mock OLLAMA API response."""
        return {
            "message": {"content": "This is a test response"},
            "prompt_eval_count": 10,
            "eval_count": 20
        }
    
    @pytest.fixture
    def ollama_generator(self):
        """Create OLLAMA generator with mocked HTTP calls."""
        with patch('requests.get') as mock_get, \
             patch('requests.post') as mock_post:
            
            # Mock server connectivity test
            mock_get.return_value.json.return_value = {"models": [{"name": "qwen3:30b-a3b-instruct-2507-q4_K_M"}]}
            mock_get.return_value.raise_for_status = Mock()
            
            # Mock API responses
            mock_post.return_value.json.return_value = {
                "message": {"content": "Test response"},
                "prompt_eval_count": 10,
                "eval_count": 20
            }
            mock_post.return_value.raise_for_status = Mock()
            
            generator = OllamaLLMGenerator(
                model_name="qwen3:30b-a3b-instruct-2507-q4_K_M",
                base_url="http://localhost:11434"
            )
            
            return generator
    
    def test_ollama_generator_initialization(self, ollama_generator):
        """Test OLLAMA generator initialization."""
        assert ollama_generator.model_name == "qwen3:30b-a3b-instruct-2507-q4_K_M"
        assert ollama_generator.base_url == "http://localhost:11434"
        assert ollama_generator.inference_type == "ollama"
    
    def test_generate_response_string_input(self, ollama_generator):
        """Test response generation with string input."""
        with patch.object(ollama_generator, '_make_request') as mock_request:
            mock_request.return_value = {
                "message": {"content": "Test answer"},
                "prompt_eval_count": 10,
                "eval_count": 15
            }
            
            response = ollama_generator.generate_response(
                "What is AI?",
                max_new_tokens=100,
                temperature=0.7
            )
            
            assert response == "Test answer"
            mock_request.assert_called_once()
    
    def test_generate_response_message_list_input(self, ollama_generator):
        """Test response generation with message list input."""
        with patch.object(ollama_generator, '_make_request') as mock_request:
            mock_request.return_value = {
                "message": {"content": "Structured answer"},
                "prompt_eval_count": 12,
                "eval_count": 18
            }
            
            messages = [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Explain machine learning"}
            ]
            
            response = ollama_generator.generate_response(
                messages,
                max_new_tokens=200,
                temperature=0.5
            )
            
            assert response == "Structured answer"
    
    def test_ner_extraction(self, ollama_generator):
        """Test Named Entity Recognition."""
        with patch.object(ollama_generator, '_make_request') as mock_request:
            mock_request.return_value = {
                "message": {"content": "Apple Inc., Steve Jobs, iPhone"}
            }
            
            entities = ollama_generator.ner("Apple Inc. was founded by Steve Jobs and created the iPhone.")
            
            assert entities == "Apple Inc., Steve Jobs, iPhone"
            mock_request.assert_called_once()
    
    def test_filter_triples_extraction(self, ollama_generator):
        """Test triple filtering and extraction."""
        with patch.object(ollama_generator, '_make_request') as mock_request:
            mock_request.return_value = {
                "message": {"content": '{"fact": [["Apple Inc", "founded_by", "Steve Jobs"]]}'}
            }
            
            triples = ollama_generator.filter_triples_with_entity_event(
                "Apple Inc. was founded by Steve Jobs",
                "Apple Inc., Steve Jobs"
            )
            
            # Should return valid JSON
            parsed = json.loads(triples)
            assert "fact" in parsed
            assert len(parsed["fact"]) == 1
            assert parsed["fact"][0] == ["Apple Inc", "founded_by", "Steve Jobs"]
    
    def test_generate_with_context(self, ollama_generator):
        """Test context-based answer generation."""
        with patch.object(ollama_generator, '_make_request') as mock_request:
            mock_request.return_value = {
                "message": {"content": "Apple was founded in 1976."}
            }
            
            answer = ollama_generator.generate_with_context(
                question="When was Apple founded?",
                context="Apple Inc. is a technology company founded in 1976 by Steve Jobs.",
                max_new_tokens=50
            )
            
            assert "1976" in answer
    
    def test_error_handling(self, ollama_generator):
        """Test error handling in OLLAMA generator."""
        with patch.object(ollama_generator, '_make_request') as mock_request:
            mock_request.side_effect = Exception("Connection error")
            
            # NER should handle errors gracefully
            entities = ollama_generator.ner("Test text")
            assert entities == ""
            
            # Triple filtering should return empty structure
            triples = ollama_generator.filter_triples_with_entity_event("text", "entities")
            parsed = json.loads(triples)
            assert parsed == {"fact": []}


class TestHippoRAG2Pipeline:
    """Test HippoRAG2Pipeline functionality."""
    
    @pytest.fixture
    def temp_data_dir(self):
        """Create temporary data directory with mock files."""
        temp_dir = Path(tempfile.mkdtemp())
        
        # Create directory structure
        (temp_dir / "kg_graphml").mkdir()
        (temp_dir / "vector_index").mkdir()
        (temp_dir / "triples_csv").mkdir()
        
        # Create mock NetworkX graph
        G = nx.DiGraph()
        G.add_node("node1", name="Entity 1", type="entity")
        G.add_node("node2", name="Entity 2", type="entity") 
        G.add_node("text1", name="Sample text passage", type="passage", text="This is sample text")
        G.add_edge("node1", "node2", relation="related_to")
        
        # Save as GraphML
        nx.write_graphml(G, temp_dir / "kg_graphml" / "knowledge_graph.graphml")
        
        # Create mock embeddings
        np.save(temp_dir / "vector_index" / "triple_nodes__from_json_with_emb.npy", 
                np.random.rand(3, 768))
        np.save(temp_dir / "vector_index" / "triple_edges__from_json_with_concept_with_emb.npy", 
                np.random.rand(1, 768))
        np.save(temp_dir / "vector_index" / "text_nodes__from_json_with_emb.npy", 
                np.random.rand(1, 768))
        
        # Create mock FAISS index
        index = faiss.IndexFlatL2(768)
        index.add(np.random.rand(1, 768).astype(np.float32))
        faiss.write_index(index, str(temp_dir / "vector_index" / "triple_edges__from_json_with_concept_with_emb_non_norm.index"))
        
        yield temp_dir
        
        # Cleanup
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def mock_config_file(self):
        """Create mock config file."""
        config_content = """[settings]
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen3:30b-a3b-instruct-2507-q4_K_M
EMBEDDING_MODEL=Qwen/Qwen3-Embedding-4B
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=1979@rabu
DATABASE_NAME=autoschemakg
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.ini', delete=False) as f:
            f.write(config_content)
            f.flush()
            yield f.name
        
        os.unlink(f.name)
    
    @pytest.fixture
    def hipporag2_pipeline(self, temp_data_dir, mock_config_file):
        """Create HippoRAG2Pipeline with mocked dependencies."""
        return HippoRAG2Pipeline(
            data_directory=str(temp_data_dir),
            config_file=mock_config_file
        )
    
    def test_pipeline_initialization(self, hipporag2_pipeline, temp_data_dir):
        """Test pipeline initialization."""
        assert hipporag2_pipeline.data_directory == Path(temp_data_dir)
        assert hipporag2_pipeline.config is not None
        assert hipporag2_pipeline.inference_config is not None
    
    def test_load_existing_data(self, hipporag2_pipeline):
        """Test loading existing pipeline data."""
        data = hipporag2_pipeline.load_existing_data()
        
        # Validate data structure
        assert "KG" in data
        assert "node_embeddings" in data
        assert "edge_embeddings" in data
        assert "text_embeddings" in data
        assert "edge_faiss_index" in data
        assert "node_list" in data
        assert "edge_list" in data
        assert "text_dict" in data
        
        # Validate graph
        assert len(data["KG"].nodes) == 3
        assert len(data["KG"].edges) == 1
        
        # Validate embeddings
        assert data["node_embeddings"].shape == (3, 768)
        assert data["edge_embeddings"].shape == (1, 768)
        assert data["text_embeddings"].shape == (1, 768)
        
        # Validate lists
        assert len(data["node_list"]) == 3
        assert len(data["edge_list"]) == 1
        
        # Validate text dictionary
        assert len(data["text_dict"]) > 0
    
    @patch('setup_embedding_model.setup_qwen_embedding_model')
    @patch('setup_ollama_llm_generator.setup_ollama_llm_generator')
    def test_setup_models(self, mock_ollama_setup, mock_embedding_setup, hipporag2_pipeline):
        """Test model setup."""
        # Mock return values
        mock_llm = Mock()
        mock_encoder = Mock()
        mock_ollama_setup.return_value = mock_llm
        mock_embedding_setup.return_value = mock_encoder
        
        hipporag2_pipeline.setup_models()
        
        assert hipporag2_pipeline.llm_generator == mock_llm
        assert hipporag2_pipeline.sentence_encoder == mock_encoder
        mock_ollama_setup.assert_called_once()
        mock_embedding_setup.assert_called_once()
    
    @patch('atlas_rag.retriever.hipporag2.HippoRAG2Retriever')
    def test_initialize_hipporag2(self, mock_retriever_class, hipporag2_pipeline):
        """Test HippoRAG2 initialization."""
        # Setup prerequisites
        hipporag2_pipeline.data = {"KG": nx.DiGraph(), "node_embeddings": np.array([]), 
                                  "edge_embeddings": np.array([]), "text_embeddings": np.array([])}
        hipporag2_pipeline.llm_generator = Mock()
        hipporag2_pipeline.sentence_encoder = Mock()
        
        # Mock retriever
        mock_retriever = Mock()
        mock_retriever_class.return_value = mock_retriever
        
        hipporag2_pipeline.initialize_hipporag2()
        
        assert hipporag2_pipeline.hipporag2_retriever == mock_retriever
        mock_retriever_class.assert_called_once()
    
    def test_query_knowledge_graph(self, hipporag2_pipeline):
        """Test knowledge graph querying."""
        # Setup mock retriever
        mock_retriever = Mock()
        mock_retriever.retrieve.return_value = (["Sample passage"], ["text1"])
        hipporag2_pipeline.hipporag2_retriever = mock_retriever
        
        passages, passage_ids = hipporag2_pipeline.query_knowledge_graph("Test question")
        
        assert passages == ["Sample passage"]
        assert passage_ids == ["text1"]
        mock_retriever.retrieve.assert_called_once_with("Test question", topN=3)
    
    def test_generate_answer(self, hipporag2_pipeline):
        """Test answer generation."""
        # Setup mocks
        mock_retriever = Mock()
        mock_retriever.retrieve.return_value = (["Context passage"], ["text1"])
        hipporag2_pipeline.hipporag2_retriever = mock_retriever
        
        mock_llm = Mock()
        mock_llm.generate_with_context.return_value = "Generated answer"
        hipporag2_pipeline.llm_generator = mock_llm
        
        answer = hipporag2_pipeline.generate_answer("What is AI?")
        
        assert answer == "Generated answer"
        mock_retriever.retrieve.assert_called_once()
        mock_llm.generate_with_context.assert_called_once()


class TestIntegrationScenarios:
    """Test complete integration scenarios."""
    
    @pytest.fixture
    def full_pipeline_setup(self, temp_data_dir, mock_config_file):
        """Setup for full pipeline integration tests."""
        return {
            "data_dir": temp_data_dir,
            "config_file": mock_config_file
        }
    
    @patch('requests.get')
    @patch('requests.post')
    @patch('sentence_transformers.SentenceTransformer')
    def test_end_to_end_pipeline(self, mock_sentence_transformer, mock_post, mock_get, full_pipeline_setup):
        """Test complete end-to-end pipeline functionality."""
        
        # Mock OLLAMA server responses
        mock_get.return_value.json.return_value = {"models": [{"name": "qwen3:30b-a3b-instruct-2507-q4_K_M"}]}
        mock_get.return_value.raise_for_status = Mock()
        
        mock_post.return_value.json.return_value = {
            "message": {"content": "This is a comprehensive answer about the knowledge graph."},
            "prompt_eval_count": 25,
            "eval_count": 50
        }
        mock_post.return_value.raise_for_status = Mock()
        
        # Mock sentence transformer
        mock_model = Mock()
        mock_model.max_seq_length = 8192
        mock_model.get_sentence_embedding_dimension.return_value = 768
        mock_sentence_transformer.return_value = mock_model
        
        # Mock encoding
        mock_encode = Mock()
        mock_encode.encode.return_value = np.random.rand(1, 768)
        
        try:
            # Create pipeline
            pipeline = HippoRAG2Pipeline(
                data_directory=str(full_pipeline_setup["data_dir"]),
                config_file=full_pipeline_setup["config_file"]
            )
            
            # Load data
            data = pipeline.load_existing_data()
            assert data is not None
            assert "KG" in data
            
            # Setup models (with mocking)
            with patch.object(pipeline, 'setup_models') as mock_setup:
                mock_setup.return_value = None
                pipeline.llm_generator = Mock()
                pipeline.sentence_encoder = mock_encode
                
                # Initialize HippoRAG2 (with mocking)
                with patch('atlas_rag.retriever.hipporag2.HippoRAG2Retriever') as mock_retriever_class:
                    mock_retriever = Mock()
                    mock_retriever.retrieve.return_value = (
                        ["Retrieved context about the knowledge graph"],
                        ["context1"]
                    )
                    mock_retriever_class.return_value = mock_retriever
                    
                    pipeline.data = data
                    pipeline.initialize_hipporag2()
                    
                    # Test query
                    answer = pipeline.generate_answer("What is this knowledge graph about?")
                    assert answer is not None
                    
                    # Validate calls
                    mock_retriever.retrieve.assert_called()
                    
        except Exception as e:
            pytest.fail(f"End-to-end pipeline test failed: {e}")
    
    def test_error_handling_scenarios(self, full_pipeline_setup):
        """Test various error handling scenarios."""
        
        # Test with non-existent data directory
        with pytest.raises(FileNotFoundError):
            HippoRAG2Pipeline(data_directory="/non/existent/path")
        
        # Test with missing required files
        empty_dir = Path(tempfile.mkdtemp())
        try:
            pipeline = HippoRAG2Pipeline(data_directory=str(empty_dir))
            with pytest.raises(FileNotFoundError):
                pipeline.load_existing_data()
        finally:
            shutil.rmtree(empty_dir)
    
    def test_configuration_validation(self, temp_data_dir):
        """Test configuration validation and fallbacks."""
        
        # Test with missing config file
        pipeline = HippoRAG2Pipeline(
            data_directory=str(temp_data_dir),
            config_file="non_existent_config.ini"
        )
        assert pipeline.config is not None
        
        # Test with empty config file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.ini', delete=False) as f:
            f.write("")
            f.flush()
            
            try:
                pipeline = HippoRAG2Pipeline(
                    data_directory=str(temp_data_dir),
                    config_file=f.name
                )
                assert pipeline.config is not None
            finally:
                os.unlink(f.name)


def test_setup_functions():
    """Test individual setup functions with mocking."""
    
    # Test OLLAMA setup with mocking
    with patch('requests.get') as mock_get, \
         patch('requests.post') as mock_post, \
         patch('configparser.ConfigParser.read') as mock_config_read:
        
        # Mock successful connection
        mock_get.return_value.json.return_value = {"models": [{"name": "qwen3:30b-a3b-instruct-2507-q4_K_M"}]}
        mock_get.return_value.raise_for_status = Mock()
        
        mock_post.return_value.json.return_value = {
            "message": {"content": "Test response"},
            "prompt_eval_count": 10,
            "eval_count": 20
        }
        mock_post.return_value.raise_for_status = Mock()
        
        # Mock config
        mock_config = Mock()
        mock_config.has_section.return_value = True
        mock_config.get.side_effect = lambda section, key, fallback=None: {
            'OLLAMA_BASE_URL': 'http://localhost:11434',
            'OLLAMA_MODEL': 'qwen3:30b-a3b-instruct-2507-q4_K_M'
        }.get(key, fallback)
        
        with patch('setup_ollama_llm_generator.ConfigParser') as mock_parser:
            mock_parser.return_value = mock_config
            
            generator = setup_ollama_llm_generator()
            assert generator is not None
            assert generator.model_name == "qwen3:30b-a3b-instruct-2507-q4_K_M"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])