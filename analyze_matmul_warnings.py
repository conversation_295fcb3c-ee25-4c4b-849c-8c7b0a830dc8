#!/usr/bin/env python3
"""
Deep Analysis of Matrix Multiplication Warnings

This script isolates and analyzes the exact cause of the matmul warnings
in HippoRAG2, even when embeddings appear healthy.
"""

import numpy as np
import warnings
from pathlib import Path

def analyze_matmul_issue():
    """Analyze the matrix multiplication warnings in detail."""
    
    print("🔬 DEEP ANALYSIS: Matrix Multiplication Warnings")
    print("="*60)
    
    # Load the actual embeddings that are causing issues
    data_dir = Path("import/pdf_dataset/vector_index")
    
    print("📂 Loading embeddings...")
    edge_embeddings = np.load(data_dir / "triple_edges__from_json_with_concept_with_emb.npy")
    text_embeddings = np.load(data_dir / "text_nodes__from_json_with_emb.npy")
    
    print(f"   Edge embeddings: {edge_embeddings.shape}")
    print(f"   Text embeddings: {text_embeddings.shape}")
    
    # Create a test query embedding (normalized like real ones)
    np.random.seed(42)
    query_emb = np.random.randn(384).astype(np.float32)
    query_emb = query_emb / np.linalg.norm(query_emb)  # L2 normalize
    
    print(f"   Test query embedding: {query_emb.shape}, norm: {np.linalg.norm(query_emb):.6f}")
    
    # Test the exact operations that cause warnings
    print(f"\n🧪 Testing problematic operations...")
    
    # Test 1: Edge embeddings @ query
    print(f"\n📊 Test 1: Edge Embeddings Matrix Multiplication")
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always", RuntimeWarning)
        
        # The exact operation from line 124
        result_edges = edge_embeddings @ query_emb
        
        print(f"   Operation: edge_embeddings @ query_emb")
        print(f"   Input shapes: {edge_embeddings.shape} @ {query_emb.shape}")
        print(f"   Result shape: {result_edges.shape}")
        print(f"   Result range: [{np.min(result_edges):.6f}, {np.max(result_edges):.6f}]")
        
        if w:
            print(f"   ❌ Warnings generated:")
            for warning in w:
                print(f"      - {warning.category.__name__}: {warning.message}")
                print(f"        File: {warning.filename}:{warning.lineno}")
        else:
            print(f"   ✅ No warnings")
    
    # Test 2: Text embeddings @ query 
    print(f"\n📊 Test 2: Text Embeddings Matrix Multiplication")
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always", RuntimeWarning)
        
        # The exact operation from line 180
        result_text = text_embeddings @ query_emb
        
        print(f"   Operation: text_embeddings @ query_emb")
        print(f"   Input shapes: {text_embeddings.shape} @ {query_emb.shape}")
        print(f"   Result shape: {result_text.shape}")
        print(f"   Result range: [{np.min(result_text):.6f}, {np.max(result_text):.6f}]")
        
        if w:
            print(f"   ❌ Warnings generated:")
            for warning in w:
                print(f"      - {warning.category.__name__}: {warning.message}")
                print(f"        File: {warning.filename}:{warning.lineno}")
        else:
            print(f"   ✅ No warnings")
    
    # Deep analysis: Check for specific problematic values
    print(f"\n🔍 Deep Analysis: Looking for root causes...")
    
    # Check for extremely small values that might cause issues in floating point math
    edge_min = np.min(np.abs(edge_embeddings[edge_embeddings != 0]))
    edge_max = np.max(np.abs(edge_embeddings))
    text_min = np.min(np.abs(text_embeddings[text_embeddings != 0]))
    text_max = np.max(np.abs(text_embeddings))
    
    print(f"   Edge embeddings - Min non-zero: {edge_min:.2e}, Max: {edge_max:.6f}")
    print(f"   Text embeddings - Min non-zero: {text_min:.2e}, Max: {text_max:.6f}")
    
    # Check if the issue is in the data types or precision
    print(f"   Edge embeddings dtype: {edge_embeddings.dtype}")
    print(f"   Text embeddings dtype: {text_embeddings.dtype}")
    print(f"   Query embedding dtype: {query_emb.dtype}")
    
    # Test with different data types
    print(f"\n🧪 Testing with different data types...")
    
    # Test with float64
    edge_emb_f64 = edge_embeddings.astype(np.float64)
    query_emb_f64 = query_emb.astype(np.float64)
    
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always", RuntimeWarning)
        result_f64 = edge_emb_f64 @ query_emb_f64
        
        if w:
            print(f"   ❌ Float64 still generates warnings:")
            for warning in w:
                print(f"      - {warning.message}")
        else:
            print(f"   ✅ Float64: No warnings")
    
    # Check specific edge cases that might trigger warnings
    print(f"\n🔍 Checking for edge cases...")
    
    # Look for patterns in the embeddings that might cause issues
    edge_zeros = (edge_embeddings == 0).sum()
    text_zeros = (text_embeddings == 0).sum()
    
    print(f"   Zero values - Edges: {edge_zeros:,}, Text: {text_zeros:,}")
    
    # Check for denormalized numbers (very small floats)
    edge_denorm = (np.abs(edge_embeddings) < np.finfo(np.float32).tiny).sum()
    text_denorm = (np.abs(text_embeddings) < np.finfo(np.float32).tiny).sum()
    
    print(f"   Denormalized numbers - Edges: {edge_denorm:,}, Text: {text_denorm:,}")
    
    # Check for values close to machine epsilon
    eps = np.finfo(np.float32).eps
    edge_tiny = (np.abs(edge_embeddings) < eps).sum()
    text_tiny = (np.abs(text_embeddings) < eps).sum()
    
    print(f"   Values < epsilon - Edges: {edge_tiny:,}, Text: {text_tiny:,}")
    
    return result_edges, result_text

def test_min_max_normalize_with_warnings():
    """Test the min_max_normalize function with the actual problematic data."""
    
    print(f"\n🔬 TESTING min_max_normalize WITH REAL DATA")
    print("="*60)
    
    # Load real embeddings and create a test result
    data_dir = Path("import/pdf_dataset/vector_index")
    edge_embeddings = np.load(data_dir / "triple_edges__from_json_with_concept_with_emb.npy")
    
    # Create a test query
    np.random.seed(42)
    query_emb = np.random.randn(384).astype(np.float32)
    query_emb = query_emb / np.linalg.norm(query_emb)
    
    # Do the matrix multiplication (this generates warnings)
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always", RuntimeWarning)
        matmul_result = edge_embeddings @ query_emb
        
        if w:
            print(f"✅ Reproduced warnings in matmul:")
            for warning in w:
                print(f"   - {warning.message}")
    
    # Now test the min_max_normalize function
    print(f"\n📊 Testing min_max_normalize with the matmul result...")
    print(f"   Input shape: {matmul_result.shape}")
    print(f"   Input range: [{np.min(matmul_result):.6f}, {np.max(matmul_result):.6f}]")
    print(f"   Input dtype: {matmul_result.dtype}")
    
    # Check for problematic values in the matmul result
    nan_count = np.isnan(matmul_result).sum()
    inf_count = np.isinf(matmul_result).sum()
    
    print(f"   NaN values: {nan_count}")
    print(f"   Inf values: {inf_count}")
    
    if nan_count > 0 or inf_count > 0:
        print(f"   ❌ Problematic values found in matmul result!")
        return False
    
    # Test the current min_max_normalize function
    def current_min_max_normalize(x):
        min_val = np.min(x)
        max_val = np.max(x)
        range_val = max_val - min_val
        
        if range_val == 0:
            return np.ones_like(x)
        
        return (x - min_val) / range_val
    
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always", RuntimeWarning)
        
        normalized_result = current_min_max_normalize(matmul_result)
        
        if w:
            print(f"   ❌ min_max_normalize generates warnings:")
            for warning in w:
                print(f"      - {warning.message}")
        else:
            print(f"   ✅ min_max_normalize: No warnings")
    
    print(f"   Output range: [{np.min(normalized_result):.6f}, {np.max(normalized_result):.6f}]")
    
    return True

def main():
    """Main analysis function."""
    
    try:
        # Analyze the matrix multiplication issue
        edge_result, text_result = analyze_matmul_issue()
        
        # Test the normalization function
        test_min_max_normalize_with_warnings()
        
        print(f"\n💡 ANALYSIS COMPLETE")
        print("="*60)
        print("The warnings are definitely coming from the matrix multiplication")
        print("operations themselves, not from the normalization function.")
        print("This suggests a deeper numerical issue in numpy/BLAS operations")
        print("with specific float32 values or array sizes.")
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()