#!/usr/bin/env python3
"""
Test Official Data Loading Workflow

This script tests the official create_embeddings_and_index() function with our most
compatible dataset (pdf_dataset) to verify if it resolves the HippoRAG2 retrieval issues.
"""

import os
import sys
from pathlib import Path
import numpy as np
from datetime import datetime

# Set environment variable to avoid tokenizer warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Add the project root to Python path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_models():
    """Initialize the sentence encoder model."""
    print("🚀 Setting up Qwen3-Embedding-4B model...")
    
    try:
        from sentence_transformers import SentenceTransformer
        from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
        
        # Use the same model as in working examples
        encoder_model_name = 'sentence-transformers/all-MiniLM-L6-v2'
        sentence_model = SentenceTransformer(encoder_model_name)
        sentence_encoder = SentenceEmbedding(sentence_model)
        
        print(f"✅ Sentence encoder initialized: {encoder_model_name}")
        return sentence_encoder, encoder_model_name
        
    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        return None, None

def test_official_data_loading():
    """Test the official create_embeddings_and_index workflow."""
    print("🧪 TESTING OFFICIAL DATA LOADING WORKFLOW")
    print("="*60)
    
    # Initialize models
    sentence_encoder, encoder_model_name = setup_models()
    if not sentence_encoder:
        return False
    
    # Use our most compatible dataset
    keyword = 'pdf_dataset'
    working_directory = f'import/{keyword}'
    
    print(f"\n📂 Working directory: {working_directory}")
    print(f"🎯 Dataset keyword: {keyword}")
    print(f"🤖 Encoder model: {encoder_model_name}")
    
    try:
        # Import the official function
        from atlas_rag.vectorstore import create_embeddings_and_index
        
        print(f"\n🔄 Running create_embeddings_and_index()...")
        print("   This may take several minutes...")
        
        # Use the same parameters as in official examples
        data = create_embeddings_and_index(
            sentence_encoder=sentence_encoder,
            model_name=encoder_model_name,
            working_directory=working_directory,
            keyword=keyword,
            include_concept=True,
            include_events=True,
            normalize_embeddings=True,
            text_batch_size=64,
            node_and_edge_batch_size=64,
        )
        
        print(f"\n✅ Official data loading completed successfully!")
        
        # Analyze the returned data structure
        print(f"\n📊 OFFICIAL DATA STRUCTURE ANALYSIS:")
        print("-" * 40)
        
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, np.ndarray):
                    print(f"   {key}: {value.shape} (numpy array)")
                elif hasattr(value, '__len__'):
                    print(f"   {key}: {len(value)} items ({type(value).__name__})")
                else:
                    print(f"   {key}: {type(value).__name__}")
        
        # Save the official data for comparison
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Test HippoRAG2 initialization with official data
        return test_hipporag2_with_official_data(data, sentence_encoder)
        
    except Exception as e:
        print(f"❌ Official data loading failed: {e}")
        print(f"💡 This might indicate our CSV format is not compatible")
        import traceback
        traceback.print_exc()
        return False

def test_hipporag2_with_official_data(data, sentence_encoder):
    """Test HippoRAG2 retrieval with officially loaded data."""
    print(f"\n🧪 TESTING HippoRAG2 WITH OFFICIAL DATA")
    print("-" * 40)
    
    try:
        # Initialize LLM generator (simplified for testing)
        from atlas_rag.llm_generator import LLMGenerator
        from openai import OpenAI
        from configparser import ConfigParser
        
        # Load API configuration
        config = ConfigParser()
        config.read('config.ini')
        
        client = OpenAI(
            base_url="https://api.deepinfra.com/v1/openai",
            api_key=config['settings']['DEEPINFRA_API_KEY'],
        )
        
        llm_generator = LLMGenerator(client=client, model_name="meta-llama/Llama-3.3-70B-Instruct")
        print("✅ LLM generator initialized")
        
        # Initialize HippoRAG2 with official data
        from atlas_rag.retriever import HippoRAG2Retriever
        
        hipporag2_retriever = HippoRAG2Retriever(
            llm_generator=llm_generator,
            sentence_encoder=sentence_encoder,
            data=data,
        )
        
        print("✅ HippoRAG2 retriever initialized with official data")
        
        # Test the problematic MMC query
        test_queries = [
            "how to create scenarios in MMC?",
            "MMC Market Maker Cockpit configuration",
            "FIX protocol API usage",
            "360T trading system setup"
        ]
        
        print(f"\n🔍 TESTING RETRIEVAL QUALITY:")
        print("-" * 30)
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{i}. Query: '{query}'")
            try:
                results = hipporag2_retriever.retrieve(query, topN=3)
                
                if isinstance(results, tuple) and len(results) == 2:
                    contents, passage_ids = results
                    print(f"   Retrieved: {len(contents)} passages")
                    
                    # Check relevance
                    for j, content in enumerate(contents):
                        content_preview = content[:100] + "..." if len(content) > 100 else content
                        print(f"   {j+1}. {content_preview}")
                        
                        # Check for MMC relevance
                        if any(term in content.lower() for term in ['mmc', 'market maker cockpit']):
                            print(f"      ✅ MMC-relevant content found!")
                        elif 'fix' in content.lower() and any(term in query.lower() for term in ['mmc', 'market maker']):
                            print(f"      ⚠️  FIX content for MMC query (potential issue)")
                        
                else:
                    print(f"   Unexpected result format: {type(results)}")
                    
            except Exception as e:
                print(f"   ❌ Query failed: {e}")
        
        print(f"\n✅ HippoRAG2 testing completed with official data workflow")
        return True
        
    except Exception as e:
        print(f"❌ HippoRAG2 testing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_data_loading_approaches():
    """Compare our custom approach vs official approach."""
    print(f"\n📈 COMPARISON: CUSTOM vs OFFICIAL DATA LOADING")
    print("="*60)
    
    # Load our custom data for comparison
    try:
        from hipporag2_pipeline import HippoRAG2Pipeline
        
        print("📊 Loading data with our custom approach...")
        custom_pipeline = HippoRAG2Pipeline(data_directory="import/pdf_dataset")
        custom_data = custom_pipeline.load_existing_data()
        
        print("✅ Custom data loaded")
        print(f"   Nodes: {len(custom_data.get('node_list', []))}")
        print(f"   Edges: {len(custom_data.get('edge_list', []))}")
        
        if 'edge_embeddings' in custom_data:
            print(f"   Edge embeddings: {custom_data['edge_embeddings'].shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Custom data loading failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 TESTING OFFICIAL AutoSchemaKG DATA LOADING WORKFLOW")
    print("="*80)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Test official data loading
        success = test_official_data_loading()
        
        if success:
            print(f"\n✅ OFFICIAL WORKFLOW TEST SUCCESSFUL!")
            print("🎯 This confirms that using create_embeddings_and_index() resolves the data ingestion issues")
        else:
            print(f"\n❌ OFFICIAL WORKFLOW TEST FAILED")
            print("💡 Our CSV format may need additional fixes before compatibility")
        
        # Compare approaches
        compare_data_loading_approaches()
        
        print(f"\n🎯 RECOMMENDATIONS:")
        print("-" * 30)
        if success:
            print("1. ✅ Replace custom data loading with create_embeddings_and_index()")
            print("2. ✅ Our pdf_dataset is compatible with official workflow")
            print("3. 🔧 Generate numeric_id files for 360t_guide datasets")
            print("4. 🧪 Test MMC query performance improvement")
        else:
            print("1. 🔧 Fix CSV column schema compatibility issues")
            print("2. 🔧 Ensure proper numeric_id generation")
            print("3. 🧪 Retry official workflow after format fixes")
        
        return success
        
    except Exception as e:
        print(f"💥 Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)