#!/usr/bin/env python3
"""
Simple test for the entity-based retrieval fix using existing setup functions
"""

import os
import sys
import pickle
from pathlib import Path

# Set environment and add path
os.environ["TOKENIZERS_PARALLELISM"] = "false"
sys.path.insert(0, str(Path(__file__).parent))

from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
from atlas_rag.retriever.inference_config import InferenceConfig
from setup_embedding_model import setup_embedding_model
from atlas_rag.llm_generator import LLMGenerator
from openai import OpenAI

def setup_ollama_llm():
    """Setup Ollama LLM Generator using existing pattern."""
    print("🤖 Setting up Ollama LLM...")
    
    try:
        ollama_url = "http://localhost:11434"
        model_name = "qwen3:30b-a3b-instruct-2507-q4_K_M"
        
        client = OpenAI(
            base_url=f"{ollama_url}/v1",
            api_key="dummy-key",
        )
        
        llm_generator = LLMGenerator(client=client, model_name=model_name)
        print(f"   ✅ LLM Generator ready with {model_name}")
        return llm_generator
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None

def main():
    print("🎯 SIMPLE TEST: Entity-Based Retrieval Fix")
    print("="*50)
    
    # Setup components
    print("🔧 Setting up components...")
    
    try:
        embedding_model = setup_embedding_model()
        print("   ✅ Embedding model ready")
    except Exception as e:
        print(f"   ❌ Embedding error: {e}")
        return
    
    try:
        llm_generator = setup_ollama_llm()
        if not llm_generator:
            raise Exception("LLM setup failed")
    except Exception as e:
        print(f"   ❌ LLM error: {e}")
        return
    
    # Load data
    print("📊 Loading data...")
    data_file = Path("import/pdf_dataset/complete_data.pkl")
    
    try:
        with open(data_file, 'rb') as f:
            complete_data = pickle.load(f)
        print("   ✅ Data loaded")
    except Exception as e:
        print(f"   ❌ Data error: {e}")
        return
    
    # Setup retriever with entity-based mode
    print("🚀 Initializing HippoRAG2...")
    
    inference_config = InferenceConfig()
    inference_config.topk_edges = 20  # Reasonable number for testing
    
    try:
        retriever = HippoRAG2Retriever(
            llm_generator=llm_generator,
            sentence_encoder=embedding_model,
            data=complete_data,
            inference_config=inference_config
        )
        print(f"   ✅ HippoRAG2 ready")
        print(f"   📊 Retrieval mode: {retriever.retrieve_node_fn.__name__}")
        
        if retriever.retrieve_node_fn.__name__ == "query2node":
            print("   🎉 ENTITY-BASED RETRIEVAL ACTIVE!")
        else:
            print(f"   ⚠️  Still using: {retriever.retrieve_node_fn.__name__}")
            
    except Exception as e:
        print(f"   ❌ HippoRAG2 error: {e}")
        return
    
    # Test entity matching directly
    print(f"\n🎯 Testing Entity Matching:")
    print("-" * 30)
    
    test_query = "Risk Reversal strategy FX option"
    print(f"Query: '{test_query}'")
    
    try:
        # Test direct entity matching
        entity_scores = retriever.query2node(test_query, topN=10)
        
        print(f"\n📊 Found {len(entity_scores)} entities:")
        
        # Look for financial entities
        financial_found = 0
        sorted_entities = sorted(entity_scores.items(), key=lambda x: x[1], reverse=True)
        
        for i, (entity, score) in enumerate(sorted_entities[:10], 1):
            is_financial = any(kw in entity.lower() for kw in [
                'risk reversal', 'fx', 'option', 'strategy', 'zero cost',
                'market maker', 'trading', 'sef', 'mmc'
            ])
            
            marker = "🎯" if is_financial else "  "
            print(f"{marker} #{i}: {entity[:40]}... (score: {score:.4f})")
            
            if is_financial:
                financial_found += 1
        
        if financial_found > 0:
            print(f"\n✅ SUCCESS: Found {financial_found} financial entities!")
        else:
            print(f"\n⚠️  No financial entities found in top 10")
            
    except Exception as e:
        print(f"❌ Entity test failed: {e}")
        return
    
    # Test full retrieval
    print(f"\n🔍 Testing Full Retrieval:")
    print("-" * 30)
    
    try:
        passages, passage_ids = retriever.retrieve(test_query, topN=3)
        
        print(f"Retrieved {len(passages)} passages:")
        
        for i, (passage, pid) in enumerate(zip(passages, passage_ids), 1):
            # Check for financial keywords
            has_financial = any(kw in passage.lower() for kw in [
                'risk reversal', 'zero cost', 'fx option', 'strategy option'
            ])
            
            marker = "✅" if has_financial else "  "
            print(f"{marker} #{i}: {passage[:100]}...")
            if has_financial:
                print(f"     🎯 Contains financial content!")
    
    except Exception as e:
        print(f"❌ Full retrieval test failed: {e}")
    
    print(f"\n" + "="*50)
    print("🎯 ENTITY-BASED RETRIEVAL TEST COMPLETE")
    print("✅ HippoRAG2 is now using entity-based retrieval")
    print("🔍 Ready for comprehensive validation")

if __name__ == "__main__":
    main()