# How to Run HippoRAG2 Q&A

## Prerequisites
Make sure <PERSON><PERSON><PERSON> is running with the qwen3 model:
```bash
ollama run qwen3:30b-a3b-thinking-2507-q4_K_M
```

## Option 1: Quick Start (Simple Q&A)
```bash
python run_hipporag2_qa.py
```

This provides:
- Simple interactive Q&A
- Shows retrieved passages and scores
- Generates answers with Ollama
- Type 'test' to run test queries
- Type 'exit' to quit

## Option 2: Full Debug Console (Recommended for Investigation)
```bash
python hipporag2_interactive_debug.py
```

This provides complete visibility with:
- **Adjustable Parameters** via interactive menu:
  - topN (number of passages to retrieve)
  - temperature (LLM creativity)
  - detail_level (minimal, normal, full, debug)
  - response_mode (concise, balanced, detailed, comprehensive)
  - show_graph_traversal (see graph navigation)
  - show_pagerank_scores (see scoring details)
  - show_context (see what's sent to LLM)

### Interactive Menu Options:
1. **Ask a question** - Enter any query
2. **Modify parameters** - Change retrieval and generation settings
3. **Show current parameters** - View all settings
4. **Run test suite** - Execute predefined test queries
5. **Show debug info** - Detailed info from last query
6. **Search content** - Find specific terms in passages

### Debug Visibility Includes:
- **Query Embedding Phase** - How query is processed
- **Graph Traversal Results** - Which nodes are selected
- **PageRank Scores** - Scoring for each passage
- **Retrieved Passages** - Full or preview based on detail level
- **Context for LLM** - Exact text sent to the model
- **Full Query to LLM** - Complete prompt with instructions
- **Generation Metrics** - Time taken, token count

## Example Session

### Simple Q&A:
```bash
python run_hipporag2_qa.py
❓ Your question: What is an OCO order?
📊 Retrieved 5 passages
   Scores: ['0.1234', '0.1122', '0.0987', '0.0876', '0.0765']
💡 Answer: An OCO (One-Cancels-Other) order consists of two orders...
```

### Debug Console:
```bash
python hipporag2_interactive_debug.py

Select option: 1
❓ Enter your question: What is risk reversal?

🔍 Query Embedding Phase
  Query: 'What is risk reversal?'
  
📊 Graph Traversal Results:
  Nodes selected: 847
  
📈 PageRank Scores:
  1. Node 7f1f1d3be805b79799579d91...: 0.125432
  2. Node 6105d6cc76af400325e94d58...: 0.098765
  
📄 Retrieved 5 Passages:
--- Passage 1 ---
Node ID: 7f1f1d3be805b79799579d91180adbd3e5138279
Score: 0.125432
Content: Risk reversal is a strategy in FX options trading...

📝 Context for LLM (2543 chars):
[Full context shown based on detail level]

💬 Generating Answer
  Model: qwen3:30b-a3b-thinking-2507-q4_K_M
  Temperature: 0.7
  
💡 Answer:
Risk reversal is an options strategy that involves...
```

## Modifying Parameters in Debug Console

Select option 2 from menu, then:
```
Available parameters:
  1. dataset_dir         = import/pdf_dataset
  2. topN                = 5
  3. temperature         = 0.7
  4. detail_level        = full
  5. show_graph_traversal = True
  ...
  
Select parameter to modify: 4
Current value of 'detail_level': full
Options: minimal, normal, full, debug
New value: debug
✅ Updated detail_level to debug
```

## Tips

1. **First Run**: Embedding computation takes 5-10 minutes. Subsequent runs are fast (cached).

2. **For Debugging Retrieval Issues**: Use the debug console with:
   - detail_level = debug
   - show_graph_traversal = True
   - show_pagerank_scores = True
   - show_context = True

3. **For Quick Answers**: Use the simple Q&A script

4. **Test Queries to Try**:
   - "What is an OCO order?"
   - "How does risk reversal work in FX trading?"
   - "Explain Market Maker Cockpit features"
   - "What are Bank Baskets?"
   - "How to configure FIX protocol?"

## Dataset Info
Both scripts use **pdf_dataset** which contains:
- 1,207 text passages (not 12!)
- 30 passages about risk reversal
- 125 passages about OCO
- Complete 360T financial documentation

This is the CORRECT dataset, not the tiny 360t_guide_direct_api_v2 dataset.