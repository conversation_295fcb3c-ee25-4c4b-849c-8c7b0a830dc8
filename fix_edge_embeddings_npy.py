#!/usr/bin/env python3
"""
Fix Edge Embeddings NPY - Regenerate Complete File

This script fixes the critical issue where edge embeddings NPY file only contains
100,000 embeddings but the CSV has 111,787 complete embeddings.

The issue caused HippoRAG2 indexing errors because:
- CSV: 111,787 edges with embeddings ✅
- NPY: 100,000 edges (truncated) ❌  
- GraphML: 160,795 edges (111K triple + 62K text) 

This script regenerates the NPY with ALL 111,787 embeddings.
"""

import os
import numpy as np
import pandas as pd
from ast import literal_eval
from pathlib import Path
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_embedding(embed_str):
    """Convert embedding string to numpy array."""
    return np.array(literal_eval(embed_str), dtype=np.float32)

def regenerate_complete_edge_embeddings(
    csv_path="/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/pdf_dataset/triples_csv/triple_edges__from_json_with_concept_with_emb.csv",
    npy_path="/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/pdf_dataset/vector_index/triple_edges__from_json_with_concept_with_emb.npy",
    backup_old=True
):
    """
    Regenerate complete edge embeddings NPY file from CSV.
    
    Args:
        csv_path: Path to CSV with complete embeddings
        npy_path: Path to NPY file to create/overwrite
        backup_old: Whether to backup existing NPY file
    """
    
    csv_path = Path(csv_path)
    npy_path = Path(npy_path)
    
    logger.info("🔧 Starting Edge Embeddings NPY Regeneration")
    logger.info(f"   CSV Source: {csv_path}")
    logger.info(f"   NPY Target: {npy_path}")
    
    # Verify CSV exists
    if not csv_path.exists():
        raise FileNotFoundError(f"CSV file not found: {csv_path}")
    
    # Backup existing NPY if requested
    if backup_old and npy_path.exists():
        backup_path = npy_path.parent / f"{npy_path.stem}_truncated_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.npy"
        logger.info(f"📦 Backing up existing NPY to: {backup_path}")
        os.rename(npy_path, backup_path)
    
    # Count total rows first
    logger.info("📊 Analyzing CSV structure...")
    total_rows = len(pd.read_csv(csv_path))
    logger.info(f"   Total CSV rows: {total_rows:,}")
    
    # Check if embedding column exists
    sample_df = pd.read_csv(csv_path, nrows=1)
    embedding_col = "embedding:STRING"
    if embedding_col not in sample_df.columns:
        raise ValueError(f"Embedding column '{embedding_col}' not found in CSV. Available columns: {list(sample_df.columns)}")
    
    logger.info(f"   ✅ Found embedding column: {embedding_col}")
    
    # Process CSV in chunks to handle memory efficiently
    chunk_size = 10000  # Smaller chunks for safer processing
    all_embeddings = []
    total_processed = 0
    
    logger.info(f"🔄 Processing CSV in chunks of {chunk_size:,}...")
    
    for chunk_idx, chunk_df in enumerate(pd.read_csv(csv_path, chunksize=chunk_size, usecols=[embedding_col])):
        logger.info(f"   Processing chunk {chunk_idx + 1} ({len(chunk_df):,} rows)...")
        
        # Parse embeddings in this chunk
        try:
            chunk_embeddings = np.stack(chunk_df[embedding_col].apply(parse_embedding).values)
            all_embeddings.append(chunk_embeddings)
            total_processed += len(chunk_embeddings)
            
            logger.info(f"   ✅ Chunk {chunk_idx + 1} processed: {chunk_embeddings.shape}")
            
        except Exception as e:
            logger.error(f"   ❌ Error processing chunk {chunk_idx + 1}: {e}")
            raise
    
    # Combine all embeddings
    logger.info("🔗 Combining all embedding chunks...")
    complete_embeddings = np.vstack(all_embeddings)
    
    logger.info(f"✅ Complete embeddings shape: {complete_embeddings.shape}")
    logger.info(f"   Total embeddings: {complete_embeddings.shape[0]:,}")
    logger.info(f"   Embedding dimension: {complete_embeddings.shape[1]}")
    logger.info(f"   Data type: {complete_embeddings.dtype}")
    
    # Verify we got all rows
    if complete_embeddings.shape[0] != total_rows:
        logger.warning(f"⚠️  Row count mismatch! CSV: {total_rows}, Processed: {complete_embeddings.shape[0]}")
    else:
        logger.info(f"✅ Row count matches: {total_rows:,}")
    
    # Save complete NPY file
    logger.info(f"💾 Saving complete embeddings to: {npy_path}")
    os.makedirs(npy_path.parent, exist_ok=True)
    np.save(npy_path, complete_embeddings)
    
    # Verify saved file
    logger.info("🔍 Verifying saved NPY file...")
    try:
        loaded_embeddings = np.load(npy_path)
        logger.info(f"   ✅ Verification passed: {loaded_embeddings.shape}")
        
        # Compare with original
        if np.array_equal(complete_embeddings, loaded_embeddings):
            logger.info("   ✅ Saved file matches generated embeddings")
        else:
            logger.error("   ❌ Saved file doesn't match generated embeddings!")
            
    except Exception as e:
        logger.error(f"   ❌ Verification failed: {e}")
        raise
    
    # Summary
    file_size_mb = npy_path.stat().st_size / (1024 * 1024)
    logger.info("📋 Regeneration Summary:")
    logger.info(f"   Original (truncated): 100,000 embeddings")
    logger.info(f"   New (complete): {complete_embeddings.shape[0]:,} embeddings")
    logger.info(f"   Gained: {complete_embeddings.shape[0] - 100000:,} embeddings")
    logger.info(f"   File size: {file_size_mb:.1f} MB")
    logger.info(f"   Embedding dimension: {complete_embeddings.shape[1]}")
    
    return complete_embeddings

def verify_fix():
    """Verify the fix worked correctly."""
    logger.info("🔍 Verifying the fix...")
    
    # Load the new NPY
    npy_path = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/pdf_dataset/vector_index/triple_edges__from_json_with_concept_with_emb.npy"
    embeddings = np.load(npy_path)
    
    # Count CSV rows
    csv_path = "/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/pdf_dataset/triples_csv/triple_edges__from_json_with_concept_with_emb.csv"
    csv_rows = len(pd.read_csv(csv_path))
    
    logger.info(f"NPY embeddings: {embeddings.shape[0]:,}")
    logger.info(f"CSV rows: {csv_rows:,}")
    
    if embeddings.shape[0] == csv_rows:
        logger.info("✅ SUCCESS: NPY now matches CSV completely!")
        return True
    else:
        logger.error(f"❌ FAILED: NPY ({embeddings.shape[0]}) != CSV ({csv_rows})")
        return False

def main():
    """Main function to regenerate edge embeddings."""
    try:
        print("🚀 Edge Embeddings NPY Regeneration Tool")
        print("=" * 50)
        
        # Regenerate embeddings
        embeddings = regenerate_complete_edge_embeddings()
        
        # Verify the fix
        success = verify_fix()
        
        if success:
            print("\n🎉 SUCCESS: Edge embeddings NPY regenerated successfully!")
            print(f"   New shape: {embeddings.shape}")
            print(f"   Ready for HippoRAG2 without indexing errors!")
        else:
            print("\n❌ FAILED: Regeneration completed but verification failed!")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Regeneration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)