#!/usr/bin/env python3
"""
Quick fix script for model selection issues.
This script clears potential caches and provides a direct model selection interface.
"""

import os
import sys
import glob
from pathlib import Path

def clear_python_cache():
    """Clear Python cache files that might be causing issues."""
    print("🧹 Clearing Python cache files...")
    
    # Find and remove .pyc files
    pyc_files = glob.glob("**/__pycache__", recursive=True)
    pyc_files.extend(glob.glob("**/*.pyc", recursive=True))
    
    removed = 0
    for pyc_file in pyc_files:
        try:
            if os.path.isdir(pyc_file):
                import shutil
                shutil.rmtree(pyc_file)
            else:
                os.remove(pyc_file)
            removed += 1
        except Exception as e:
            print(f"   Could not remove {pyc_file}: {e}")
    
    print(f"   Removed {removed} cache files")

def direct_model_selection():
    """Direct model selection bypassing the model manager if needed."""
    print("\n🎯 Direct Model Selection")
    print("="*50)
    
    try:
        import ollama
        client = ollama.Client()
        response = client.list()
        models = response.models
        
        print("Available models:")
        for i, model in enumerate(models, 1):
            size_gb = model.size / (1024**3)
            print(f"  {i:2d}. {model.model:<40} ({size_gb:.1f} GB)")
        
        choice = input(f"\nSelect model (1-{len(models)}): ").strip()
        
        if choice.isdigit():
            choice_num = int(choice)
            if 1 <= choice_num <= len(models):
                selected = models[choice_num - 1].model
                print(f"✅ Selected: {selected}")
                
                # Update the interactive debug script directly
                script_path = "hipporag2_interactive_debug.py"
                if os.path.exists(script_path):
                    with open(script_path, 'r') as f:
                        content = f.read()
                    
                    # Replace the default model
                    content = content.replace(
                        "'ollama_model': 'qwen3:30b-a3b-thinking-2507-q4_K_M'",
                        f"'ollama_model': '{selected}'"
                    )
                    
                    with open(script_path, 'w') as f:
                        f.write(content)
                    
                    print(f"✅ Updated {script_path} with selected model")
                else:
                    print(f"⚠️  Could not find {script_path} to update")
                
                return selected
        
        print("❌ Invalid selection")
        return None
        
    except Exception as e:
        print(f"❌ Direct selection failed: {e}")
        return None

def main():
    print("🔧 MODEL SELECTION FIX UTILITY")
    print("="*60)
    
    print("\nThis utility helps resolve model selection issues by:")
    print("1. Clearing Python cache files")
    print("2. Providing direct model selection")
    print("3. Updating your script with the selected model")
    
    # Step 1: Clear cache
    clear_python_cache()
    
    # Step 2: Test current state
    print("\n🧪 Testing current state:")
    try:
        # Force reload of the module
        if 'ollama_model_manager' in sys.modules:
            del sys.modules['ollama_model_manager']
        
        from ollama_model_manager import OllamaModelManager, OLLAMA_AVAILABLE
        print(f"   OLLAMA_AVAILABLE: {OLLAMA_AVAILABLE}")
        
        mgr = OllamaModelManager()
        print(f"   Manager available: {mgr.available}")
        print(f"   Server available: {mgr.is_ollama_available()}")
        
        if mgr.available and mgr.is_ollama_available():
            print("   ✅ Model manager should work now")
        else:
            print("   ❌ Still having issues, using direct selection")
            direct_model_selection()
            
    except Exception as e:
        print(f"   ❌ Module still has issues: {e}")
        print("   Using direct selection instead:")
        direct_model_selection()
    
    print("\n🎉 Fix complete! Try running hipporag2_interactive_debug.py again.")

if __name__ == "__main__":
    main()