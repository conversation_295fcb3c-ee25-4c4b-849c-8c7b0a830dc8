#!/usr/bin/env python3
"""
Analyze Edge Candidates for MMC Query

Investigates why the top edge candidates for "how to create scenarios in MMC?"
are not finding MMC-related content, despite MMC entities existing in the graph.
"""

import numpy as np
from collections import Counter
from sklearn.metrics.pairwise import cosine_similarity
import warnings

from hipporag2_pipeline import HippoRAG2Pipeline

def analyze_mmc_edge_candidates():
    """Analyze what types of edges are being found for MMC queries."""
    print("🔍 ANALYZING MMC EDGE CANDIDATES")
    print("="*60)
    
    # Initialize pipeline
    pipeline = HippoRAG2Pipeline()
    data = pipeline.load_existing_data()
    pipeline.setup_models()
    pipeline.data = data
    pipeline.initialize_hipporag2()
    
    retriever = pipeline.hipporag2_retriever
    
    # Test query
    query = "how to create scenarios in MMC?"
    print(f"Query: '{query}'")
    
    # Get query embedding
    query_emb = pipeline.sentence_encoder.encode([query], query_type="edge")
    
    # Compute similarities with all edges
    with warnings.catch_warnings():
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        raw_scores = retriever.edge_embeddings @ query_emb[0].T
        
    scores = raw_scores  # Use raw scores for analysis
    
    # Get top 100 candidates for analysis
    top_indices = np.argsort(scores)[::-1][:100]
    
    print(f"\n📊 ANALYZING TOP 100 EDGE CANDIDATES:")
    print(f"Score range: {scores[top_indices[0]]:.4f} to {scores[top_indices[-1]]:.4f}")
    
    # Analyze edge types and entities
    entity_types = Counter()
    relation_types = Counter()
    mmc_related_edges = []
    scenario_related_edges = []
    
    print(f"\nTOP 20 EDGE CANDIDATES:")
    for i, idx in enumerate(top_indices[:20]):
        if idx < len(retriever.edge_list):
            edge = retriever.edge_list[idx]
            score = scores[idx]
            
            try:
                source_data = retriever.KG.nodes[edge[0]]
                target_data = retriever.KG.nodes[edge[1]]
                edge_data = retriever.KG.edges[edge]
                
                source_name = source_data.get('name', edge[0])
                target_name = target_data.get('name', edge[1])
                relation = edge_data.get('relation', 'connected_to')
                
                source_type = source_data.get('type', 'unknown')
                target_type = target_data.get('type', 'unknown')
                
                entity_types[f"{source_type}->{target_type}"] += 1
                relation_types[relation] += 1
                
                # Check for MMC relevance
                full_description = f"{source_name} {relation} {target_name}"
                is_mmc = any(term in full_description.lower() for term in ['mmc', 'market maker cockpit', 'cockpit'])
                is_scenario = any(term in full_description.lower() for term in ['scenario', 'scenarios'])
                
                print(f"   {i+1:2d}. Score: {score:.4f} | {source_type}->{target_type}")
                print(f"       {source_name[:40]}... {relation} {target_name[:40]}...")
                
                if is_mmc:
                    mmc_related_edges.append((idx, score, full_description))
                    print(f"       🎯 MMC-RELATED!")
                    
                if is_scenario:
                    scenario_related_edges.append((idx, score, full_description))
                    print(f"       📋 SCENARIO-RELATED!")
                    
            except Exception as e:
                print(f"   {i+1:2d}. Score: {score:.4f} | Error: {e}")
    
    print(f"\n📈 EDGE TYPE ANALYSIS (top 10):")
    for edge_type, count in entity_types.most_common(10):
        print(f"   {edge_type}: {count}")
    
    print(f"\n🔗 RELATION TYPE ANALYSIS (top 10):")
    for relation, count in relation_types.most_common(10):
        print(f"   {relation}: {count}")
    
    print(f"\n🎯 MMC-RELATED EDGES FOUND: {len(mmc_related_edges)}")
    for i, (idx, score, description) in enumerate(mmc_related_edges[:5]):
        print(f"   {i+1}. Score: {score:.4f} | Rank: {list(top_indices).index(idx)+1}")
        print(f"      {description}")
    
    print(f"\n📋 SCENARIO-RELATED EDGES FOUND: {len(scenario_related_edges)}")
    for i, (idx, score, description) in enumerate(scenario_related_edges[:5]):
        print(f"   {i+1}. Score: {score:.4f} | Rank: {list(top_indices).index(idx)+1}")
        print(f"      {description}")
    
    return mmc_related_edges, scenario_related_edges

def analyze_mmc_entities_directly():
    """Analyze MMC entities directly in the graph."""
    print(f"\n🔍 ANALYZING MMC ENTITIES IN GRAPH")
    print("="*40)
    
    pipeline = HippoRAG2Pipeline()
    data = pipeline.load_existing_data()
    
    graph = data["KG"]
    
    # Find all MMC entities
    mmc_entities = []
    scenario_entities = []
    
    for node_id in graph.nodes():
        node_data = graph.nodes[node_id]
        node_name = str(node_data.get('name', '')).lower()
        node_type = node_data.get('type', 'unknown')
        
        if any(term in node_name for term in ['mmc', 'market maker cockpit']):
            mmc_entities.append((node_id, node_data.get('name', node_id), node_type))
            
        if 'scenario' in node_name:
            scenario_entities.append((node_id, node_data.get('name', node_id), node_type))
    
    print(f"📊 ENTITY ANALYSIS:")
    print(f"   MMC entities found: {len(mmc_entities)}")
    print(f"   Scenario entities found: {len(scenario_entities)}")
    
    print(f"\n🎯 TOP MMC ENTITIES:")
    for i, (node_id, name, node_type) in enumerate(mmc_entities[:10]):
        print(f"   {i+1:2d}. {node_type}: {name}")
        
        # Check connections
        neighbors = list(graph.neighbors(node_id))
        text_connections = [n for n in neighbors if graph.nodes[n].get('type') in ['passage', 'text']]
        print(f"       Connections: {len(neighbors)} total, {len(text_connections)} to text")
    
    print(f"\n📋 TOP SCENARIO ENTITIES:")
    for i, (node_id, name, node_type) in enumerate(scenario_entities[:10]):
        print(f"   {i+1:2d}. {node_type}: {name}")
        
        # Check connections
        neighbors = list(graph.neighbors(node_id))
        text_connections = [n for n in neighbors if graph.nodes[n].get('type') in ['passage', 'text']]
        print(f"       Connections: {len(neighbors)} total, {len(text_connections)} to text")
    
    return mmc_entities, scenario_entities

def test_direct_mmc_queries():
    """Test different MMC-related queries to see which work better."""
    print(f"\n🧪 TESTING DIFFERENT MMC QUERIES")
    print("="*50)
    
    pipeline = HippoRAG2Pipeline()
    data = pipeline.load_existing_data()
    pipeline.setup_models()
    pipeline.data = data
    pipeline.initialize_hipporag2()
    
    test_queries = [
        "MMC Market Maker Cockpit",
        "scenarios in market maker cockpit",
        "how to configure MMC",
        "market maker cockpit setup",
        "MMC configuration guide"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Query: '{query}'")
        
        try:
            # Get top edge candidates
            query_emb = pipeline.sentence_encoder.encode([query], query_type="edge")
            
            with warnings.catch_warnings():
                warnings.filterwarnings('ignore', category=RuntimeWarning)
                raw_scores = pipeline.hipporag2_retriever.edge_embeddings @ query_emb[0].T
            
            top_idx = np.argmax(raw_scores)
            top_score = raw_scores[top_idx]
            
            if top_idx < len(pipeline.hipporag2_retriever.edge_list):
                edge = pipeline.hipporag2_retriever.edge_list[top_idx]
                source_name = pipeline.data["KG"].nodes[edge[0]].get('name', edge[0])
                target_name = pipeline.data["KG"].nodes[edge[1]].get('name', edge[1])
                relation = pipeline.data["KG"].edges[edge].get('relation', 'connected_to')
                
                print(f"   Top result: {top_score:.4f}")
                print(f"   Edge: {source_name[:50]}... {relation} {target_name[:50]}...")
                
                # Check if MMC-related
                full_desc = f"{source_name} {relation} {target_name}".lower()
                if any(term in full_desc for term in ['mmc', 'market maker cockpit']):
                    print(f"   ✅ MMC-relevant top result!")
                else:
                    print(f"   ❌ Not MMC-relevant")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")

def main():
    """Main analysis function."""
    print("🔬 MMC EDGE CANDIDATE ANALYSIS")
    print("="*80)
    
    try:
        # Analyze edge candidates
        mmc_edges, scenario_edges = analyze_mmc_edge_candidates()
        
        # Analyze entities directly
        mmc_entities, scenario_entities = analyze_mmc_entities_directly()
        
        # Test different queries
        test_direct_mmc_queries()
        
        print(f"\n{'='*80}")
        print(f"🏁 ANALYSIS SUMMARY")
        print(f"{'='*80}")
        print(f"   MMC-related edges in top 100: {len(mmc_edges)}")
        print(f"   Scenario-related edges in top 100: {len(scenario_edges)}")
        print(f"   Total MMC entities in graph: {len(mmc_entities)}")
        print(f"   Total scenario entities in graph: {len(scenario_entities)}")
        
        if len(mmc_edges) == 0:
            print(f"\n⚠️  ISSUE: No MMC-related edges found in top candidates")
            print(f"   This explains why the query isn't returning MMC content")
            print(f"   The query embedding may not be matching MMC-related edges effectively")
        
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)