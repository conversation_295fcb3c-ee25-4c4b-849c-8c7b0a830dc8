#!/usr/bin/env python3
"""
NVIDIA/NV-Embed-v2 Embedding Regeneration Pipeline
This script regenerates all embeddings using nvidia/NV-Embed-v2 for better financial domain performance.

Tasks:
1. Load and test nvidia/NV-Embed-v2 model
2. Regenerate node embeddings with new model
3. Regenerate edge embeddings with new model  
4. Regenerate text embeddings with new model
5. Rebuild FAISS indexes
6. Update complete_data.pkl
7. Run validation tests
"""

import os
import sys
import time
import pickle
import numpy as np
import pandas as pd
import faiss
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Any
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from setup_embedding_model import setup_embedding_model
from atlas_rag.vectorstore.embedding_model import SentenceEmbedding

class NVIDIAEmbeddingRegenerator:
    """Regenerate all embeddings using nvidia/NV-Embed-v2 model."""
    
    def __init__(self, data_dir: str = "import/pdf_dataset"):
        self.data_dir = Path(data_dir)
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.sentence_encoder = None
        self.batch_size = 32  # Conservative batch size for nvidia/NV-Embed-v2
        
        logger.info(f"🚀 NVIDIA/NV-Embed-v2 Embedding Regeneration Pipeline")
        logger.info(f"   Data Directory: {self.data_dir}")
        logger.info(f"   Timestamp: {self.timestamp}")
        
        # Validate data directory
        if not self.data_dir.exists():
            raise FileNotFoundError(f"Data directory not found: {self.data_dir}")
    
    def setup_nvidia_model(self) -> bool:
        """Setup and test nvidia/NV-Embed-v2 model."""
        logger.info("🔧 Setting up nvidia/NV-Embed-v2 model...")
        
        try:
            self.sentence_encoder = setup_embedding_model()
            logger.info("✅ nvidia/NV-Embed-v2 model loaded successfully")
            
            # Test encoding with financial content
            test_texts = [
                "Risk reversal option trading strategy in financial markets",
                "Market maker cockpit for FX trading scenarios", 
                "Bridge trading platform configuration and setup"
            ]
            
            logger.info("🧪 Testing nvidia/NV-Embed-v2 with financial content...")
            test_embeddings = self.sentence_encoder.encode(test_texts, batch_size=len(test_texts))
            
            logger.info(f"✅ Test successful - Embeddings shape: {test_embeddings.shape}")
            logger.info(f"   Model dimension: {test_embeddings.shape[1]}")
            logger.info(f"   Embedding norm range: [{np.linalg.norm(test_embeddings, axis=1).min():.3f}, "
                       f"{np.linalg.norm(test_embeddings, axis=1).max():.3f}]")
            
            # Test financial domain similarity
            from sklearn.metrics.pairwise import cosine_similarity
            similarities = cosine_similarity(test_embeddings)
            logger.info(f"   Financial similarity test: Risk-reversal vs MMC = {similarities[0,1]:.3f}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ nvidia/NV-Embed-v2 model setup failed: {e}")
            return False
    
    def regenerate_node_embeddings(self) -> bool:
        """Regenerate node embeddings using nvidia/NV-Embed-v2."""
        logger.info("📊 Regenerating node embeddings with nvidia/NV-Embed-v2...")
        
        try:
            # Load node data
            nodes_csv_path = self.data_dir / "triples_csv" / "triple_nodes__from_json_without_emb.csv"
            if not nodes_csv_path.exists():
                logger.error(f"Node CSV not found: {nodes_csv_path}")
                return False
                
            nodes_df = pd.read_csv(nodes_csv_path)
            logger.info(f"   Loaded {len(nodes_df)} nodes from CSV")
            
            # Extract text content for embedding
            node_texts = []
            for _, row in nodes_df.iterrows():
                # Use name as primary text, fallback to id
                text = str(row.get('name', row.get('id', '')))
                if len(text.strip()) < 3:  # Ensure meaningful text
                    text = f"Entity: {row.get('id', 'unknown')}"
                node_texts.append(text)
            
            logger.info(f"   Prepared {len(node_texts)} texts for embedding")
            logger.info(f"   Sample texts: {node_texts[:3]}")
            
            # Generate embeddings in batches
            logger.info(f"   Generating embeddings (batch_size={self.batch_size})...")
            start_time = time.time()
            
            node_embeddings = self.sentence_encoder.encode(
                node_texts, 
                batch_size=self.batch_size,
                show_progress_bar=True,
                convert_to_numpy=True
            )
            
            generation_time = time.time() - start_time
            logger.info(f"✅ Node embeddings generated in {generation_time:.1f}s")
            logger.info(f"   Final shape: {node_embeddings.shape}")
            
            # Save embeddings
            output_path = self.data_dir / "vector_index" / "triple_nodes__from_json_with_emb.npy"
            backup_path = self.data_dir / "vector_index" / f"triple_nodes__from_json_with_emb_backup_{self.timestamp}.npy"
            
            # Create backup if original exists
            if output_path.exists():
                np.save(backup_path, np.load(output_path))
                logger.info(f"   Backup created: {backup_path}")
            
            # Save new embeddings
            np.save(output_path, node_embeddings)
            logger.info(f"   Saved new node embeddings: {output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Node embedding regeneration failed: {e}")
            return False
    
    def regenerate_edge_embeddings(self) -> bool:
        """Regenerate edge embeddings using nvidia/NV-Embed-v2."""
        logger.info("🔗 Regenerating edge embeddings with nvidia/NV-Embed-v2...")
        
        try:
            # Load edge data
            edges_csv_path = self.data_dir / "triples_csv" / "triple_edges__from_json_without_emb.csv"
            if not edges_csv_path.exists():
                logger.error(f"Edge CSV not found: {edges_csv_path}")
                return False
                
            edges_df = pd.read_csv(edges_csv_path)
            logger.info(f"   Loaded {len(edges_df)} edges from CSV")
            
            # Extract edge text content for embedding
            edge_texts = []
            for _, row in edges_df.iterrows():
                # Create edge text as "head relation tail"
                head = str(row.get('head', ''))
                relation = str(row.get('relation', ''))
                tail = str(row.get('tail', ''))
                edge_text = f"{head} {relation} {tail}"
                edge_texts.append(edge_text)
            
            logger.info(f"   Prepared {len(edge_texts)} edge texts for embedding")
            logger.info(f"   Sample edge texts: {edge_texts[:3]}")
            
            # Generate embeddings in batches
            logger.info(f"   Generating edge embeddings (batch_size={self.batch_size})...")
            start_time = time.time()
            
            edge_embeddings = self.sentence_encoder.encode(
                edge_texts,
                batch_size=self.batch_size,
                show_progress_bar=True,
                convert_to_numpy=True
            )
            
            generation_time = time.time() - start_time
            logger.info(f"✅ Edge embeddings generated in {generation_time:.1f}s")
            logger.info(f"   Final shape: {edge_embeddings.shape}")
            
            # Save embeddings
            output_path = self.data_dir / "vector_index" / "triple_edges__from_json_with_concept_with_emb.npy"
            backup_path = self.data_dir / "vector_index" / f"triple_edges__from_json_with_concept_with_emb_backup_{self.timestamp}.npy"
            
            # Create backup if original exists
            if output_path.exists():
                np.save(backup_path, np.load(output_path))
                logger.info(f"   Backup created: {backup_path}")
            
            # Save new embeddings
            np.save(output_path, edge_embeddings)
            logger.info(f"   Saved new edge embeddings: {output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Edge embedding regeneration failed: {e}")
            return False
    
    def regenerate_text_embeddings(self) -> bool:
        """Regenerate text embeddings using nvidia/NV-Embed-v2."""
        logger.info("📝 Regenerating text embeddings with nvidia/NV-Embed-v2...")
        
        try:
            # Load text data
            text_csv_path = self.data_dir / "triples_csv" / "text_nodes__from_json.csv"
            if not text_csv_path.exists():
                logger.error(f"Text CSV not found: {text_csv_path}")
                return False
                
            text_df = pd.read_csv(text_csv_path)
            logger.info(f"   Loaded {len(text_df)} text nodes from CSV")
            
            # Extract text content for embedding
            text_contents = []
            for _, row in text_df.iterrows():
                # Use text content, fallback to name
                text = str(row.get('text', row.get('name', '')))
                if len(text.strip()) < 10:  # Ensure meaningful text for passages
                    text = f"Text passage: {row.get('id', 'unknown')}"
                text_contents.append(text)
            
            logger.info(f"   Prepared {len(text_contents)} texts for embedding")
            logger.info(f"   Sample text lengths: {[len(t) for t in text_contents[:3]]}")
            
            # Generate embeddings in batches
            logger.info(f"   Generating text embeddings (batch_size={self.batch_size})...")
            start_time = time.time()
            
            text_embeddings = self.sentence_encoder.encode(
                text_contents,
                batch_size=self.batch_size,
                show_progress_bar=True,
                convert_to_numpy=True
            )
            
            generation_time = time.time() - start_time
            logger.info(f"✅ Text embeddings generated in {generation_time:.1f}s")
            logger.info(f"   Final shape: {text_embeddings.shape}")
            
            # Save embeddings
            output_path = self.data_dir / "vector_index" / "text_nodes__from_json_with_emb.npy"
            backup_path = self.data_dir / "vector_index" / f"text_nodes__from_json_with_emb_backup_{self.timestamp}.npy"
            
            # Create backup if original exists
            if output_path.exists():
                np.save(backup_path, np.load(output_path))
                logger.info(f"   Backup created: {backup_path}")
            
            # Save new embeddings
            np.save(output_path, text_embeddings)
            logger.info(f"   Saved new text embeddings: {output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Text embedding regeneration failed: {e}")
            return False
    
    def rebuild_faiss_indexes(self) -> bool:
        """Rebuild FAISS indexes with new embeddings."""
        logger.info("🔍 Rebuilding FAISS indexes with new embeddings...")
        
        try:
            vector_dir = self.data_dir / "vector_index"
            
            # Define embedding files and their corresponding index files
            embedding_files = [
                ("triple_nodes__from_json_with_emb.npy", "triple_nodes__from_json_with_emb_non_norm.index"),
                ("triple_edges__from_json_with_concept_with_emb.npy", "triple_edges__from_json_with_concept_with_emb_non_norm.index"),
                ("text_nodes__from_json_with_emb.npy", "text_nodes__from_json_with_emb_non_norm.index")
            ]
            
            for emb_file, idx_file in embedding_files:
                emb_path = vector_dir / emb_file
                idx_path = vector_dir / idx_file
                
                if not emb_path.exists():
                    logger.warning(f"   Embedding file not found: {emb_path}")
                    continue
                
                logger.info(f"   Building FAISS index for {emb_file}...")
                
                # Load embeddings
                embeddings = np.load(emb_path)
                logger.info(f"     Loaded embeddings: {embeddings.shape}")
                
                # Create FAISS index (L2 distance, non-normalized)
                dimension = embeddings.shape[1]
                index = faiss.IndexFlatL2(dimension)
                
                # Add embeddings to index
                index.add(embeddings.astype('float32'))
                logger.info(f"     Added {index.ntotal} vectors to index")
                
                # Backup existing index if it exists
                if idx_path.exists():
                    backup_idx_path = vector_dir / f"{idx_file}_backup_{self.timestamp}"
                    os.rename(idx_path, backup_idx_path)
                    logger.info(f"     Backup created: {backup_idx_path}")
                
                # Save new index
                faiss.write_index(index, str(idx_path))
                logger.info(f"     ✅ FAISS index saved: {idx_path}")
            
            logger.info("✅ All FAISS indexes rebuilt successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ FAISS index rebuilding failed: {e}")
            return False
    
    def update_complete_data_pkl(self) -> bool:
        """Update complete_data.pkl with new embeddings and model info."""
        logger.info("📦 Updating complete_data.pkl with new embeddings...")
        
        try:
            complete_data_path = self.data_dir / "complete_data.pkl"
            
            if not complete_data_path.exists():
                logger.error(f"complete_data.pkl not found: {complete_data_path}")
                return False
            
            # Backup existing complete_data.pkl
            backup_path = self.data_dir / f"complete_data_backup_{self.timestamp}.pkl"
            import shutil
            shutil.copy2(complete_data_path, backup_path)
            logger.info(f"   Backup created: {backup_path}")
            
            # Load existing data
            with open(complete_data_path, 'rb') as f:
                data = pickle.load(f)
            logger.info("   Loaded existing complete_data.pkl")
            
            # Update embeddings with new ones
            vector_dir = self.data_dir / "vector_index"
            
            # Update node embeddings
            node_emb_path = vector_dir / "triple_nodes__from_json_with_emb.npy"
            if node_emb_path.exists():
                data['node_embeddings'] = np.load(node_emb_path)
                logger.info(f"   Updated node embeddings: {data['node_embeddings'].shape}")
            
            # Update edge embeddings
            edge_emb_path = vector_dir / "triple_edges__from_json_with_concept_with_emb.npy"
            if edge_emb_path.exists():
                data['edge_embeddings'] = np.load(edge_emb_path)
                logger.info(f"   Updated edge embeddings: {data['edge_embeddings'].shape}")
            
            # Update text embeddings
            text_emb_path = vector_dir / "text_nodes__from_json_with_emb.npy"
            if text_emb_path.exists():
                data['text_embeddings'] = np.load(text_emb_path)
                logger.info(f"   Updated text embeddings: {data['text_embeddings'].shape}")
            
            # Update FAISS indexes
            edge_idx_path = vector_dir / "triple_edges__from_json_with_concept_with_emb_non_norm.index"
            if edge_idx_path.exists():
                data['edge_faiss_index'] = faiss.read_index(str(edge_idx_path))
                logger.info(f"   Updated edge FAISS index: {data['edge_faiss_index'].ntotal} vectors")
            
            # Add metadata about the regeneration
            data['embedding_model'] = 'nvidia/NV-Embed-v2'
            data['regeneration_timestamp'] = self.timestamp
            data['regeneration_info'] = {
                'model': 'nvidia/NV-Embed-v2',
                'timestamp': self.timestamp,
                'batch_size': self.batch_size,
                'embedding_dimension': data['edge_embeddings'].shape[1] if 'edge_embeddings' in data else 'unknown'
            }
            
            # Save updated complete_data.pkl
            with open(complete_data_path, 'wb') as f:
                pickle.dump(data, f, protocol=pickle.HIGHEST_PROTOCOL)
            
            logger.info("✅ complete_data.pkl updated successfully")
            logger.info(f"   New embedding model: nvidia/NV-Embed-v2")
            logger.info(f"   Regeneration timestamp: {self.timestamp}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ complete_data.pkl update failed: {e}")
            return False
    
    def run_validation_tests(self) -> bool:
        """Run validation tests with financial queries."""
        logger.info("🧪 Running validation tests with financial queries...")
        
        try:
            # Test queries for financial domain
            test_queries = [
                "risk reversal option trading strategy",
                "market maker cockpit scenarios", 
                "bridge trading platform configuration",
                "FX currency trading setup",
                "financial market scenarios"
            ]
            
            logger.info("   Testing query embeddings...")
            
            for i, query in enumerate(test_queries):
                query_emb = self.sentence_encoder.encode([query])
                logger.info(f"   Query {i+1}: '{query}' -> embedding shape: {query_emb.shape}")
            
            # Test similarity between financial queries
            all_query_embs = self.sentence_encoder.encode(test_queries)
            from sklearn.metrics.pairwise import cosine_similarity
            similarities = cosine_similarity(all_query_embs)
            
            logger.info("   Financial query similarity matrix:")
            for i, query1 in enumerate(test_queries):
                for j, query2 in enumerate(test_queries):
                    if i < j:  # Only show upper triangle
                        sim = similarities[i, j]
                        logger.info(f"     '{query1[:30]}...' vs '{query2[:30]}...' = {sim:.3f}")
            
            # Load and test edge embeddings
            edge_emb_path = self.data_dir / "vector_index" / "triple_edges__from_json_with_concept_with_emb.npy"
            if edge_emb_path.exists():
                edge_embeddings = np.load(edge_emb_path)
                logger.info(f"   Loaded edge embeddings for validation: {edge_embeddings.shape}")
                
                # Test query vs edge similarity
                query_emb = self.sentence_encoder.encode(["risk reversal trading strategy"])
                similarities = cosine_similarity(query_emb, edge_embeddings)
                top_similarities = np.sort(similarities[0])[-10:]  # Top 10 similarities
                logger.info(f"   Top 10 similarities for 'risk reversal': {top_similarities}")
                logger.info(f"   Mean similarity: {similarities.mean():.3f}, Max: {similarities.max():.3f}")
            
            logger.info("✅ Validation tests completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Validation tests failed: {e}")
            return False
    
    def run_complete_regeneration(self) -> bool:
        """Run the complete embedding regeneration pipeline."""
        logger.info("🚀 Starting complete NVIDIA/NV-Embed-v2 embedding regeneration...")
        logger.info("=" * 80)
        
        start_time = time.time()
        
        # Step 1: Setup nvidia/NV-Embed-v2 model
        if not self.setup_nvidia_model():
            logger.error("❌ NVIDIA model setup failed - aborting regeneration")
            return False
        
        # Step 2: Regenerate node embeddings
        if not self.regenerate_node_embeddings():
            logger.error("❌ Node embedding regeneration failed - aborting")
            return False
        
        # Step 3: Regenerate edge embeddings
        if not self.regenerate_edge_embeddings():
            logger.error("❌ Edge embedding regeneration failed - aborting")
            return False
        
        # Step 4: Regenerate text embeddings
        if not self.regenerate_text_embeddings():
            logger.error("❌ Text embedding regeneration failed - aborting")
            return False
        
        # Step 5: Rebuild FAISS indexes
        if not self.rebuild_faiss_indexes():
            logger.error("❌ FAISS index rebuilding failed - aborting")
            return False
        
        # Step 6: Update complete_data.pkl
        if not self.update_complete_data_pkl():
            logger.error("❌ complete_data.pkl update failed - aborting")
            return False
        
        # Step 7: Run validation tests
        if not self.run_validation_tests():
            logger.error("❌ Validation tests failed - but regeneration completed")
        
        total_time = time.time() - start_time
        
        logger.info("🎉 NVIDIA/NV-Embed-v2 EMBEDDING REGENERATION COMPLETED!")
        logger.info("=" * 80)
        logger.info(f"Total regeneration time: {total_time/60:.1f} minutes")
        logger.info(f"Timestamp: {self.timestamp}")
        logger.info("✅ All embeddings regenerated with nvidia/NV-Embed-v2")
        logger.info("✅ FAISS indexes rebuilt")
        logger.info("✅ complete_data.pkl updated")
        logger.info("✅ Ready for improved financial domain retrieval!")
        
        return True

def main():
    """Main entry point for embedding regeneration."""
    
    import argparse
    
    parser = argparse.ArgumentParser(description="NVIDIA/NV-Embed-v2 Embedding Regeneration")
    parser.add_argument(
        "--data-dir",
        default="import/pdf_dataset",
        help="Data directory (default: import/pdf_dataset)"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=32,
        help="Embedding batch size (default: 32)"
    )
    
    args = parser.parse_args()
    
    try:
        # Create regenerator
        regenerator = NVIDIAEmbeddingRegenerator(data_dir=args.data_dir)
        regenerator.batch_size = args.batch_size
        
        # Run complete regeneration
        success = regenerator.run_complete_regeneration()
        
        if success:
            logger.info("🎉 Regeneration completed successfully!")
            sys.exit(0)
        else:
            logger.error("❌ Regeneration failed!")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()