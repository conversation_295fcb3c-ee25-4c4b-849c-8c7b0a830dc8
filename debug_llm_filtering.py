#!/usr/bin/env python3
"""
Debug LLM Filtering in HippoRAG2

This script tests the filter_triples_with_entity_event method directly to understand
why it's failing and causing HippoRAG2 to fall back to basic similarity search.
"""

import os
import sys
import json
import traceback
from pathlib import Path

# Set environment variable to avoid tokenizer warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Add the project root to Python path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from atlas_rag.llm_generator import LLMGenerator
from openai import OpenAI

def setup_ollama_llm():
    """Setup the same Ollama LLM used in HippoRAG2."""
    print("🤖 Setting up Ollama LLM Generator...")
    
    try:
        ollama_url = "http://localhost:11434"
        model_name = "qwen3:30b-a3b-instruct-2507-q4_K_M"
        
        client = OpenAI(
            base_url=f"{ollama_url}/v1",
            api_key="dummy-key",
        )
        
        llm_generator = LLMGenerator(client=client, model_name=model_name)
        print(f"✅ LLM Generator created with {model_name}")
        return llm_generator
    except Exception as e:
        print(f"❌ Error creating LLM Generator: {e}")
        return None

def test_llm_filtering():
    """Test LLM filtering with sample query and triples."""
    print("\n🔍 TESTING LLM TRIPLE FILTERING")
    print("=" * 50)
    
    # Setup LLM
    llm_generator = setup_ollama_llm()
    if not llm_generator:
        return False
    
    # Test query (same as user's)
    query = "what is a risk reversal option, and how to create one in Bridge?"
    print(f"🎯 Query: '{query}'")
    
    # Sample triples (simplified format that HippoRAG2 would generate)
    sample_triples = {
        "fact": [
            ["Bridge", "has_feature", "option_trading"],
            ["risk_reversal", "is_type_of", "option_strategy"],
            ["option_strategy", "involves", "buying_and_selling"],
            ["Bridge", "supports", "trading_platform"],
            ["FIX_protocol", "used_by", "Bridge"],
            ["currency_trading", "available_in", "Bridge"]
        ]
    }
    
    triples_json = json.dumps(sample_triples, ensure_ascii=False)
    print(f"📋 Sample triples JSON:\n{triples_json}")
    
    # Test the filtering method
    print(f"\n🔄 Calling filter_triples_with_entity_event()...")
    
    try:
        result = llm_generator.filter_triples_with_entity_event(query, triples_json)
        print(f"✅ LLM filtering successful!")
        print(f"📤 Raw result: {result}")
        
        # Try to parse the result
        try:
            parsed_result = json.loads(result)
            print(f"✅ Result is valid JSON: {parsed_result}")
            
            if 'fact' in parsed_result:
                filtered_facts = parsed_result['fact']
                print(f"📊 Filtered facts count: {len(filtered_facts)}")
                print(f"📋 Filtered facts: {filtered_facts}")
                
                if len(filtered_facts) == 0:
                    print("⚠️  LLM returned 0 filtered facts - this causes fallback!")
                    return False
                else:
                    print("🎉 LLM filtering working correctly!")
                    return True
            else:
                print("❌ Result JSON missing 'fact' key")
                return False
                
        except json.JSONDecodeError as json_error:
            print(f"❌ Result is not valid JSON: {json_error}")
            print(f"Raw result: {result}")
            return False
        
    except Exception as e:
        print(f"❌ LLM filtering failed with exception: {e}")
        print(f"Exception type: {type(e)}")
        print(f"Full traceback:")
        traceback.print_exc()
        return False

def test_llm_basic_functionality():
    """Test basic LLM functionality to ensure it's working."""
    print("\n🧪 TESTING BASIC LLM FUNCTIONALITY")
    print("=" * 40)
    
    llm_generator = setup_ollama_llm()
    if not llm_generator:
        return False
    
    # Test basic generation
    try:
        test_messages = [
            {"role": "user", "content": "What is 2+2? Reply with just the number."}
        ]
        
        response = llm_generator.generate_response(test_messages, max_new_tokens=10)
        print(f"✅ Basic generation test: '{response}'")
        
        if "4" in response:
            print("✅ LLM is responding correctly")
            return True
        else:
            print("⚠️  LLM response unexpected")
            return False
            
    except Exception as e:
        print(f"❌ Basic LLM test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main debug function."""
    print("🔍 DEBUGGING HIPPORAG2 LLM FILTERING FAILURE")
    print("=" * 55)
    print("Investigating why LLM filtering returns 0 results")
    print("and causes fallback to basic similarity search")
    print()
    
    # Test 1: Basic LLM functionality
    print("STEP 1: Testing basic LLM functionality")
    if not test_llm_basic_functionality():
        print("\n❌ BASIC LLM FUNCTIONALITY FAILED")
        print("Issue: Ollama LLM is not responding properly")
        return False
    
    # Test 2: LLM filtering method
    print("\nSTEP 2: Testing LLM triple filtering method")
    if not test_llm_filtering():
        print("\n❌ LLM FILTERING FAILED")
        print("Issue: filter_triples_with_entity_event method not working")
        return False
    
    print("\n🎉 DEBUG SUCCESSFUL!")
    print("✅ LLM filtering should be working")
    print("💡 If HippoRAG2 is still failing, check edge embeddings or FAISS index")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)