#!/usr/bin/env python3
"""
Comprehensive MMC debugging report
"""

import sys
import os
import csv
from pathlib import Path

def generate_mmc_debug_report():
    """Generate comprehensive MMC debugging report"""
    
    print("=== MMC Content Debug Report ===\n")
    
    # Load text mappings
    text_hash_to_numeric = {}
    text_numeric_to_content = {}
    
    with open("import/pdf_dataset/triples_csv/text_nodes__from_json_with_numeric_id.csv", 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)
        for row in reader:
            if len(row) >= 4:
                try:
                    text_hash = row[0]
                    content = row[1]
                    numeric_id = int(row[2])
                    
                    text_hash_to_numeric[text_hash] = numeric_id
                    text_numeric_to_content[numeric_id] = content
                except (ValueError, IndexError):
                    continue
    
    print(f"Total text nodes loaded: {len(text_numeric_to_content)}")
    
    # Find all MMC-related content
    mmc_text_nodes = []
    scenario_text_nodes = []
    
    for numeric_id, content in text_numeric_to_content.items():
        content_lower = content.lower()
        
        if "mmc" in content_lower or "market maker cockpit" in content_lower:
            mmc_text_nodes.append((numeric_id, content))
            
        if "scenario" in content_lower:
            scenario_text_nodes.append((numeric_id, content))
    
    print(f"Text nodes containing MMC content: {len(mmc_text_nodes)}")
    print(f"Text nodes containing scenario content: {len(scenario_text_nodes)}")
    
    # Analyze main MMC text node
    mmc_hash = "1fb3759bd9af49535dc71864a9dfaa0382b097be4ae24abdd55ef9dd5838b149"
    main_mmc_id = text_hash_to_numeric[mmc_hash]
    main_mmc_content = text_numeric_to_content[main_mmc_id]
    
    print(f"\n=== Main MMC Text Node Analysis ===")
    print(f"Numeric ID: {main_mmc_id}")
    print(f"Content length: {len(main_mmc_content)} characters")
    print(f"Contains 'scenario': {'scenario' in main_mmc_content.lower()}")
    print(f"Contains 'create': {'create' in main_mmc_content.lower()}")
    print(f"Contains 'MMC': {'MMC' in main_mmc_content}")
    print(f"Contains 'Market Maker Cockpit': {'Market Maker Cockpit' in main_mmc_content}")
    
    # Show relevant excerpts
    content_lower = main_mmc_content.lower()
    
    # Find scenario mentions
    if "scenario" in content_lower:
        start_idx = content_lower.find("scenario")
        excerpt = main_mmc_content[max(0, start_idx-50):start_idx+200]
        print(f"\nScenario excerpt from main MMC text:")
        print(f"...{excerpt}...")
    
    # Show other MMC text nodes
    print(f"\n=== Other MMC Text Nodes ===")
    for i, (numeric_id, content) in enumerate(mmc_text_nodes[:5]):
        if numeric_id != main_mmc_id:
            print(f"\nMMC text node {i+1}: ID={numeric_id}")
            print(f"Length: {len(content)} chars")
            print(f"Preview: {content[:150]}...")
            
            # Check if this contains scenario creation info
            if "scenario" in content.lower() and "create" in content.lower():
                print(f"🎯 Contains scenario + create!")
    
    # Show scenario text nodes
    print(f"\n=== Scenario Text Nodes ===")
    for i, (numeric_id, content) in enumerate(scenario_text_nodes[:5]):
        print(f"\nScenario text node {i+1}: ID={numeric_id}")
        print(f"Length: {len(content)} chars")
        print(f"Preview: {content[:150]}...")
        
        # Check relevance to MMC
        if "mmc" in content.lower() or "market maker cockpit" in content.lower():
            print(f"✓ Contains MMC references!")
        
        if "create" in content.lower():
            print(f"✓ Contains 'create'!")
    
    # Analyze the specific query "how to create scenarios in MMC?"
    print(f"\n=== Query Analysis: 'how to create scenarios in MMC?' ===")
    
    query_terms = ["how", "create", "scenario", "mmc", "market maker cockpit"]
    
    best_matches = []
    
    for numeric_id, content in text_numeric_to_content.items():
        content_lower = content.lower()
        
        # Count term matches
        term_matches = sum(1 for term in query_terms if term in content_lower)
        
        if term_matches >= 2:  # At least 2 terms match
            best_matches.append((numeric_id, content, term_matches))
    
    # Sort by number of matches
    best_matches.sort(key=lambda x: x[2], reverse=True)
    
    print(f"Text nodes matching query terms: {len(best_matches)}")
    print(f"\nTop matches by term count:")
    
    for i, (numeric_id, content, matches) in enumerate(best_matches[:10]):
        print(f"\nMatch {i+1}: ID={numeric_id}, Terms={matches}")
        print(f"Preview: {content[:200]}...")
        
        if numeric_id == main_mmc_id:
            print(f"🎯 This is the main MMC text node!")
    
    # Check specific content about scenario creation
    print(f"\n=== Scenario Creation Content Search ===")
    
    creation_keywords = ["create scenario", "add scenario", "define scenario", "new scenario", "scenario configuration"]
    
    creation_matches = []
    for numeric_id, content in text_numeric_to_content.items():
        content_lower = content.lower()
        
        for keyword in creation_keywords:
            if keyword in content_lower:
                creation_matches.append((numeric_id, content, keyword))
    
    if creation_matches:
        print(f"Found {len(creation_matches)} explicit scenario creation mentions:")
        for numeric_id, content, keyword in creation_matches:
            print(f"\nID={numeric_id}, Keyword='{keyword}'")
            
            # Find the context around the keyword
            start_idx = content.lower().find(keyword)
            context = content[max(0, start_idx-100):start_idx+300]
            print(f"Context: ...{context}...")
    else:
        print("No explicit scenario creation instructions found.")
    
    # Summary and recommendations
    print(f"\n=== Summary & Recommendations ===")
    
    print(f"✓ MMC content exists in {len(mmc_text_nodes)} text nodes")
    print(f"✓ Scenario content exists in {len(scenario_text_nodes)} text nodes")
    print(f"✓ Main MMC text node (ID={main_mmc_id}) contains scenario references")
    
    if best_matches and best_matches[0][0] != main_mmc_id:
        print(f"⚠️  Main MMC node is not the top match for query terms")
        print(f"    Top match is ID={best_matches[0][0]} with {best_matches[0][2]} term matches")
    
    print(f"\nRecommendations:")
    print(f"1. The main MMC text node (ID=133) ranks low (191/1207) in similarity search")
    print(f"2. This suggests the embedding model doesn't capture semantic similarity well")
    print(f"3. Consider using a different embedding model or approach")
    print(f"4. Alternative: Use entity-based retrieval to find 'Pricing and Risk Management Scenarios' entity")
    print(f"5. Then traverse graph connections to find connected text passages")

if __name__ == "__main__":
    generate_mmc_debug_report()