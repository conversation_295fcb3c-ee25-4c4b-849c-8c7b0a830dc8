#!/usr/bin/env python3
"""
Search for MMC scenario creation content in the current dataset.
Since text node 133 doesn't exist, we need to find where the actual MMC scenario content is located.
"""

import json
import logging
import sys
from pathlib import Path
from hipporag2_pipeline import HippoRAG2Pipeline

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def search_mmc_scenario_content():
    """Search for MMC scenario creation content in the text passages."""
    print("🔍 SEARCHING FOR MMC SCENARIO CONTENT")
    print("=" * 60)
    
    try:
        # Load data using pipeline
        pipeline = HippoRAG2Pipeline(data_directory="import/pdf_dataset")
        data = pipeline.load_existing_data()
        
        text_dict = data['text_dict']
        KG = data['KG']
        
        print(f"✅ Loaded {len(text_dict)} text passages")
        
        # Search for MMC + scenario content
        mmc_scenario_passages = []
        scenario_creation_passages = []
        mmc_passages = []
        scenario_passages = []
        
        for text_id, content in text_dict.items():
            content_lower = content.lower()
            
            # Check for MMC + scenario together
            if ('mmc' in content_lower or 'market maker cockpit' in content_lower) and 'scenario' in content_lower:
                mmc_scenario_passages.append((text_id, content))
            
            # Check for scenario creation content
            if 'scenario' in content_lower and ('create' in content_lower or 'creation' in content_lower or 'configure' in content_lower):
                scenario_creation_passages.append((text_id, content))
            
            # Separate MMC and scenario content
            if 'mmc' in content_lower or 'market maker cockpit' in content_lower:
                mmc_passages.append((text_id, content))
            
            if 'scenario' in content_lower:
                scenario_passages.append((text_id, content))
        
        print(f"\n📊 SEARCH RESULTS:")
        print(f"MMC + Scenario passages: {len(mmc_scenario_passages)}")
        print(f"Scenario creation passages: {len(scenario_creation_passages)}")
        print(f"MMC passages: {len(mmc_passages)}")
        print(f"Scenario passages: {len(scenario_passages)}")
        
        # Show MMC + scenario passages (most relevant)
        if mmc_scenario_passages:
            print(f"\n🎯 MMC + SCENARIO PASSAGES:")
            print("-" * 40)
            for i, (text_id, content) in enumerate(mmc_scenario_passages[:5]):
                print(f"\n{i+1}. Text ID: {text_id}")
                print(f"Content (first 300 chars): {content[:300]}...")
                
                # Check graph connectivity for this text node
                if text_id in KG.nodes:
                    neighbors = list(KG.neighbors(text_id))
                    print(f"Connected entities: {len(neighbors)}")
                    if neighbors:
                        # Show a few connected entities
                        for j, neighbor in enumerate(neighbors[:5]):
                            if neighbor in KG.nodes:
                                entity_name = KG.nodes[neighbor].get('id', 'N/A')
                                print(f"  - {entity_name}")
                        if len(neighbors) > 5:
                            print(f"  ... and {len(neighbors) - 5} more")
        
        # Show scenario creation passages
        if scenario_creation_passages:
            print(f"\n🛠️ SCENARIO CREATION PASSAGES:")
            print("-" * 40)
            for i, (text_id, content) in enumerate(scenario_creation_passages[:3]):
                print(f"\n{i+1}. Text ID: {text_id}")
                print(f"Content (first 300 chars): {content[:300]}...")
        
        # Show top MMC passages if no combined content found
        if not mmc_scenario_passages and mmc_passages:
            print(f"\n📈 TOP MMC PASSAGES (no scenario mention):")
            print("-" * 40)
            for i, (text_id, content) in enumerate(mmc_passages[:3]):
                print(f"\n{i+1}. Text ID: {text_id}")
                print(f"Content (first 300 chars): {content[:300]}...")
        
        return mmc_scenario_passages, scenario_creation_passages, mmc_passages, scenario_passages
        
    except Exception as e:
        print(f"❌ Error searching for content: {e}")
        return [], [], [], []

def analyze_entity_text_connections():
    """Analyze how MMC and scenario entities connect to text passages."""
    print(f"\n🔗 ANALYZING ENTITY-TEXT CONNECTIONS")
    print("=" * 60)
    
    try:
        # Load data
        pipeline = HippoRAG2Pipeline(data_directory="import/pdf_dataset")
        data = pipeline.load_existing_data()
        
        KG = data['KG']
        node_list = data['node_list']
        text_dict = data['text_dict']
        
        # Find key MMC and scenario entities
        mmc_entity = None
        scenario_entity = None
        
        for node_id in node_list:
            if node_id in KG.nodes:
                entity_name = KG.nodes[node_id].get('id', '').lower()
                
                if entity_name == 'market maker cockpit (mmc)':
                    mmc_entity = node_id
                    print(f"✅ Found MMC entity: {node_id}")
                
                if entity_name == 'pricing and risk management scenarios':
                    scenario_entity = node_id
                    print(f"✅ Found scenario entity: {node_id}")
        
        # Analyze connections for key entities
        for entity_name, entity_id in [("MMC", mmc_entity), ("Scenario", scenario_entity)]:
            if entity_id:
                print(f"\n🔍 ANALYZING {entity_name} ENTITY CONNECTIONS:")
                print("-" * 30)
                
                neighbors = list(KG.neighbors(entity_id))
                text_neighbors = [n for n in neighbors if n in text_dict]
                
                print(f"Total neighbors: {len(neighbors)}")
                print(f"Text neighbors: {len(text_neighbors)}")
                
                # Show connected text passages
                if text_neighbors:
                    print(f"Connected text passages:")
                    for i, text_id in enumerate(text_neighbors[:5]):
                        content = text_dict[text_id]
                        content_preview = content[:150].replace('\n', ' ')
                        print(f"  {i+1}. Text {text_id}: {content_preview}...")
                    
                    if len(text_neighbors) > 5:
                        print(f"  ... and {len(text_neighbors) - 5} more text passages")
        
    except Exception as e:
        print(f"❌ Error analyzing connections: {e}")

def main():
    print("🚀 SEARCHING FOR MMC SCENARIO CONTENT")
    print("=" * 60)
    
    # Search for relevant content
    mmc_scenario_passages, scenario_creation_passages, mmc_passages, scenario_passages = search_mmc_scenario_content()
    
    # Analyze entity connections
    analyze_entity_text_connections()
    
    # Summary
    print(f"\n📊 FINAL SUMMARY:")
    print("=" * 30)
    print(f"MMC + Scenario passages found: {len(mmc_scenario_passages)}")
    print(f"Scenario creation passages found: {len(scenario_creation_passages)}")
    print(f"Total MMC passages: {len(mmc_passages)}")
    print(f"Total scenario passages: {len(scenario_passages)}")
    
    if mmc_scenario_passages:
        print(f"\n✅ Found {len(mmc_scenario_passages)} passages combining MMC and scenario content")
        print("These are the most relevant for the query 'how to create scenarios in MMC?'")
    elif scenario_creation_passages:
        print(f"\n⚠️  Found {len(scenario_creation_passages)} scenario creation passages but no MMC-specific ones")
    else:
        print(f"\n❌ No specific scenario creation content found")
        print("The retrieval algorithm may need to combine separate MMC and scenario information")

if __name__ == "__main__":
    main()