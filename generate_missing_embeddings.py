#!/usr/bin/env python3
"""
Generate Missing Embeddings for HippoRAG2

This script generates the critical missing embeddings identified in the analysis:
1. 10,623 entity-text edges (breaking retrieval connectivity)
2. 1,207 text nodes (can't participate in similarity search)

The goal is to enable proper graph traversal from entities to text passages.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import os
from datetime import datetime
import shutil
from tqdm import tqdm
from collections import defaultdict

from hipporag2_pipeline import HippoRAG2Pipeline

class MissingEmbeddingGenerator:
    def __init__(self, data_directory="import/pdf_dataset"):
        self.data_directory = Path(data_directory)
        self.pipeline = None
        self.data = None
        self.sentence_encoder = None
        self.missing_edges = []
        self.missing_nodes = []
        
    def setup(self):
        """Setup the pipeline and load data."""
        print("🔧 Setting up HippoRAG2 Pipeline...")
        
        self.pipeline = HippoRAG2Pipeline(data_directory=str(self.data_directory))
        self.data = self.pipeline.load_existing_data()
        self.pipeline.setup_models()
        
        # Get the sentence encoder for generating embeddings
        self.sentence_encoder = self.pipeline.sentence_encoder
        
        print("✅ Setup complete")
    
    def identify_missing_embeddings(self):
        """Identify specific missing edges and nodes that need embeddings."""
        print("🔍 Identifying missing embeddings...")
        
        graph = self.data["KG"]
        embedded_edge_set = set(self.data["edge_list"])
        all_edge_set = set(graph.edges())
        missing_edge_set = all_edge_set - embedded_edge_set
        
        print(f"Total missing edges: {len(missing_edge_set):,}")
        
        # Focus on entity-text edges (the critical ones)
        entity_text_edges = []
        other_missing_edges = []
        
        for edge in missing_edge_set:
            source, target = edge
            source_type = graph.nodes[source].get('type', 'unknown')
            target_type = graph.nodes[target].get('type', 'unknown')
            
            if ((source_type == 'entity' and target_type == 'text') or 
                (source_type == 'text' and target_type == 'entity')):
                entity_text_edges.append(edge)
            else:
                other_missing_edges.append(edge)
        
        self.missing_edges = entity_text_edges
        print(f"Critical entity-text edges: {len(entity_text_edges):,}")
        print(f"Other missing edges: {len(other_missing_edges):,}")
        
        # Identify missing nodes (text nodes without embeddings)
        # Based on analysis, these are the 1,207 text nodes
        text_nodes = []
        for node_id in graph.nodes():
            node_data = graph.nodes[node_id]
            node_type = node_data.get('type', 'unknown')
            if node_type == 'text':
                text_nodes.append(node_id)
        
        self.missing_nodes = text_nodes
        print(f"Text nodes needing embeddings: {len(text_nodes):,}")
        
        return len(entity_text_edges), len(text_nodes)
    
    def generate_edge_embeddings(self):
        """Generate embeddings for missing entity-text edges."""
        print(f"\n🔗 Generating embeddings for {len(self.missing_edges):,} entity-text edges...")
        
        if not self.missing_edges:
            print("No missing edges to process")
            return np.array([])
        
        graph = self.data["KG"]
        edge_embeddings = []
        
        # Process edges in batches for memory efficiency
        batch_size = 100
        batches = [self.missing_edges[i:i + batch_size] for i in range(0, len(self.missing_edges), batch_size)]
        
        for batch_idx, batch in enumerate(tqdm(batches, desc="Processing edge batches")):
            batch_texts = []
            
            for edge in batch:
                source, target = edge
                
                # Get node information
                source_data = graph.nodes[source]
                target_data = graph.nodes[target]
                
                # Create text representation of the edge
                source_name = source_data.get('name', str(source))
                target_name = target_data.get('name', str(target))
                relation = graph.edges[edge].get('relation', 'connected_to')
                
                # For entity-text edges, we want to capture the semantic relationship
                edge_text = f"{source_name} {relation} {target_name}"
                batch_texts.append(edge_text)
            
            # Generate embeddings for this batch
            try:
                batch_embeddings = self.sentence_encoder.encode(batch_texts, query_type="edge")
                edge_embeddings.extend(batch_embeddings)
                
            except Exception as e:
                print(f"Error processing batch {batch_idx}: {e}")
                # Use zero embeddings as fallback
                fallback_dim = 384  # Standard dimension
                fallback_embeddings = np.zeros((len(batch), fallback_dim), dtype=np.float32)
                edge_embeddings.extend(fallback_embeddings)
        
        edge_embeddings_array = np.array(edge_embeddings, dtype=np.float32)
        print(f"✅ Generated edge embeddings: {edge_embeddings_array.shape}")
        
        return edge_embeddings_array
    
    def generate_node_embeddings(self):
        """Generate embeddings for missing text nodes."""
        print(f"\n📄 Generating embeddings for {len(self.missing_nodes):,} text nodes...")
        
        if not self.missing_nodes:
            print("No missing nodes to process")
            return np.array([])
        
        graph = self.data["KG"]
        node_embeddings = []
        
        # Get text content for nodes
        node_texts = []
        valid_nodes = []
        
        for node_id in self.missing_nodes:
            # Get text content from text_dict
            text_content = self.data["text_dict"].get(node_id, "")
            
            if not text_content:
                # Try to get from node attributes
                node_data = graph.nodes[node_id]
                text_content = node_data.get('text', '') or node_data.get('name', str(node_id))
            
            if text_content and len(text_content.strip()) > 0:
                node_texts.append(text_content)
                valid_nodes.append(node_id)
            else:
                print(f"Warning: No text content for node {node_id}")
        
        print(f"Processing {len(valid_nodes):,} nodes with valid text content")
        
        # Process nodes in batches
        batch_size = 50  # Smaller batches for potentially long text
        batches = [node_texts[i:i + batch_size] for i in range(0, len(node_texts), batch_size)]
        
        for batch_idx, batch in enumerate(tqdm(batches, desc="Processing node batches")):
            try:
                # Use passage type for text nodes
                batch_embeddings = self.sentence_encoder.encode(batch, query_type="passage")
                node_embeddings.extend(batch_embeddings)
                
            except Exception as e:
                print(f"Error processing node batch {batch_idx}: {e}")
                # Use zero embeddings as fallback
                fallback_dim = 384
                fallback_embeddings = np.zeros((len(batch), fallback_dim), dtype=np.float32)
                node_embeddings.extend(fallback_embeddings)
        
        node_embeddings_array = np.array(node_embeddings, dtype=np.float32)
        print(f"✅ Generated node embeddings: {node_embeddings_array.shape}")
        
        # Update missing_nodes to only include valid ones
        self.missing_nodes = valid_nodes
        
        return node_embeddings_array
    
    def save_missing_embeddings(self, edge_embeddings, node_embeddings):
        """Save the generated embeddings to disk."""
        print(f"\n💾 Saving generated embeddings...")
        
        vector_dir = self.data_directory / "vector_index"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save edge embeddings
        if len(edge_embeddings) > 0:
            edge_path = vector_dir / f"missing_entity_text_edges_embeddings_{timestamp}.npy"
            np.save(edge_path, edge_embeddings)
            print(f"✅ Saved edge embeddings: {edge_path}")
            
            # Also save edge mapping
            edge_mapping_path = vector_dir / f"missing_entity_text_edges_mapping_{timestamp}.txt"
            with open(edge_mapping_path, 'w') as f:
                for i, edge in enumerate(self.missing_edges):
                    source, target = edge
                    f.write(f"{i}: {source} -> {target}\n")
            print(f"✅ Saved edge mapping: {edge_mapping_path}")
        
        # Save node embeddings
        if len(node_embeddings) > 0:
            node_path = vector_dir / f"missing_text_nodes_embeddings_{timestamp}.npy"
            np.save(node_path, node_embeddings)
            print(f"✅ Saved node embeddings: {node_path}")
            
            # Also save node mapping
            node_mapping_path = vector_dir / f"missing_text_nodes_mapping_{timestamp}.txt"
            with open(node_mapping_path, 'w') as f:
                for i, node_id in enumerate(self.missing_nodes):
                    f.write(f"{i}: {node_id}\n")
            print(f"✅ Saved node mapping: {node_mapping_path}")
    
    def run_analysis_only(self):
        """Run analysis without integration - safer first step."""
        print("🚀 Starting Missing Embeddings Generation (Analysis Mode)")
        print("="*60)
        
        try:
            # Setup
            self.setup()
            
            # Identify what's missing
            edge_count, node_count = self.identify_missing_embeddings()
            
            if edge_count == 0 and node_count == 0:
                print("✅ No missing embeddings found!")
                return True
            
            print(f"\n🎯 Processing Plan:")
            print(f"   Entity-text edges: {edge_count:,}")
            print(f"   Text nodes: {node_count:,}")
            print(f"   Estimated time: ~{(edge_count + node_count) // 100} minutes")
            
            # Generate missing embeddings
            edge_embeddings = self.generate_edge_embeddings()
            node_embeddings = self.generate_node_embeddings()
            
            # Save generated embeddings (but don't integrate yet)
            self.save_missing_embeddings(edge_embeddings, node_embeddings)
            
            print(f"\n🎉 SUCCESS: Generated missing embeddings!")
            print(f"   New edge embeddings: {len(edge_embeddings):,}")
            print(f"   New node embeddings: {len(node_embeddings):,}")
            print(f"   Saved to disk for manual integration")
            print(f"\n⚠️  Next step: Review generated embeddings before integration")
            
            return True
            
        except Exception as e:
            print(f"❌ Embedding generation failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Main function."""
    try:
        generator = MissingEmbeddingGenerator()
        success = generator.run_analysis_only()
        return success
        
    except Exception as e:
        print(f"❌ Main execution failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)