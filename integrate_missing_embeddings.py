#!/usr/bin/env python3
"""
Integrate Missing Embeddings into HippoRAG2 System

This script safely integrates the generated embeddings:
1. 10,623 entity-text edge embeddings 
2. 1,207 text node embeddings

The integration must update both embeddings arrays and edge/node lists.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import shutil
from datetime import datetime
import pickle

from hipporag2_pipeline import HippoRAG2Pipeline

class EmbeddingIntegrator:
    def __init__(self, data_directory="import/pdf_dataset"):
        self.data_directory = Path(data_directory)
        self.vector_dir = self.data_directory / "vector_index"
        self.pipeline = None
        self.data = None
        
        # Paths to generated embeddings (use latest timestamp)
        self.edge_embeddings_file = None
        self.edge_mapping_file = None  
        self.node_embeddings_file = None
        self.node_mapping_file = None
        
    def find_generated_embeddings(self):
        """Find the most recent generated embedding files."""
        print("🔍 Finding generated embedding files...")
        
        # Find edge embedding files
        edge_files = list(self.vector_dir.glob("missing_entity_text_edges_embeddings_*.npy"))
        if edge_files:
            self.edge_embeddings_file = sorted(edge_files)[-1]  # Most recent
            timestamp = self.edge_embeddings_file.stem.split('_')[-1]
            self.edge_mapping_file = self.vector_dir / f"missing_entity_text_edges_mapping_{timestamp}.txt"
            print(f"   Found edge embeddings: {self.edge_embeddings_file.name}")
        else:
            print("   ❌ No edge embedding files found")
            
        # Find node embedding files  
        node_files = list(self.vector_dir.glob("missing_text_nodes_embeddings_*.npy"))
        if node_files:
            self.node_embeddings_file = sorted(node_files)[-1]  # Most recent
            timestamp = self.node_embeddings_file.stem.split('_')[-1]
            self.node_mapping_file = self.vector_dir / f"missing_text_nodes_mapping_{timestamp}.txt"
            print(f"   Found node embeddings: {self.node_embeddings_file.name}")
        else:
            print("   ❌ No node embedding files found")
            
        return bool(self.edge_embeddings_file) or bool(self.node_embeddings_file)
    
    def load_generated_embeddings(self):
        """Load the generated embeddings and their mappings."""
        print("📂 Loading generated embeddings...")
        
        # Load edge embeddings and mapping
        new_edge_embeddings = np.array([])
        new_edge_list = []
        
        if self.edge_embeddings_file and self.edge_embeddings_file.exists():
            new_edge_embeddings = np.load(self.edge_embeddings_file)
            print(f"   Edge embeddings: {new_edge_embeddings.shape}")
            
            # Load edge mapping
            if self.edge_mapping_file and self.edge_mapping_file.exists():
                with open(self.edge_mapping_file, 'r') as f:
                    for line in f:
                        parts = line.strip().split(': ')
                        if len(parts) == 2:
                            edge_str = parts[1]
                            source, target = edge_str.split(' -> ')
                            new_edge_list.append((source, target))
                            
                print(f"   Edge mappings: {len(new_edge_list)}")
        
        # Load node embeddings and mapping
        new_node_embeddings = np.array([])
        new_node_list = []
        
        if self.node_embeddings_file and self.node_embeddings_file.exists():
            new_node_embeddings = np.load(self.node_embeddings_file)
            print(f"   Node embeddings: {new_node_embeddings.shape}")
            
            # Load node mapping
            if self.node_mapping_file and self.node_mapping_file.exists():
                with open(self.node_mapping_file, 'r') as f:
                    for line in f:
                        parts = line.strip().split(': ')
                        if len(parts) == 2:
                            node_id = parts[1]
                            new_node_list.append(node_id)
                            
                print(f"   Node mappings: {len(new_node_list)}")
        
        return new_edge_embeddings, new_edge_list, new_node_embeddings, new_node_list
    
    def create_backups(self):
        """Create backups of current embedding files."""
        print("📦 Creating backups of current embeddings...")
        
        backup_dir = self.vector_dir / "backup_before_integration"
        backup_dir.mkdir(exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        files_to_backup = [
            "triple_edges__from_json_with_concept_with_emb.npy",
            "triple_nodes__from_json_with_emb.npy",
            "text_nodes__from_json_with_emb.npy"
        ]
        
        for filename in files_to_backup:
            src = self.vector_dir / filename
            if src.exists():
                dst = backup_dir / f"{src.stem}_backup_{timestamp}.npy"
                shutil.copy2(src, dst)
                print(f"   Backed up: {filename}")
    
    def integrate_edge_embeddings(self, new_edge_embeddings, new_edge_list):
        """Integrate new edge embeddings with existing ones."""
        if len(new_edge_embeddings) == 0:
            print("⚠️  No new edge embeddings to integrate")
            return False
            
        print(f"🔗 Integrating {len(new_edge_embeddings):,} new edge embeddings...")
        
        # Load existing edge embeddings
        existing_edge_path = self.vector_dir / "triple_edges__from_json_with_concept_with_emb.npy"
        existing_embeddings = np.load(existing_edge_path)
        print(f"   Existing edge embeddings: {existing_embeddings.shape}")
        
        # Combine embeddings
        combined_embeddings = np.vstack([existing_embeddings, new_edge_embeddings])
        print(f"   Combined edge embeddings: {combined_embeddings.shape}")
        
        # Save combined embeddings
        np.save(existing_edge_path, combined_embeddings)
        print(f"   ✅ Updated edge embeddings saved")
        
        # Update edge list - this is tricky because we need to maintain consistency
        # The existing edge_list corresponds to the first N embeddings
        # The new edges will be appended after
        print(f"   📝 New edges will be available at indices {len(existing_embeddings):,} to {len(combined_embeddings)-1:,}")
        
        # Save edge list mapping for reference
        edge_list_path = self.vector_dir / f"extended_edge_list_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(edge_list_path, 'w') as f:
            f.write(f"# Extended edge list after integration\n")
            f.write(f"# Existing edges: 0 to {len(existing_embeddings)-1}\n")
            f.write(f"# New entity-text edges: {len(existing_embeddings)} to {len(combined_embeddings)-1}\n")
            for i, edge in enumerate(new_edge_list):
                f.write(f"{len(existing_embeddings) + i}: {edge[0]} -> {edge[1]}\n")
        
        print(f"   ✅ Edge list mapping saved: {edge_list_path.name}")
        return True
    
    def integrate_node_embeddings(self, new_node_embeddings, new_node_list):
        """Integrate new node embeddings with existing ones."""
        if len(new_node_embeddings) == 0:
            print("⚠️  No new node embeddings to integrate")
            return False
            
        print(f"📄 Integrating {len(new_node_embeddings):,} new node embeddings...")
        
        # Load existing node embeddings
        existing_node_path = self.vector_dir / "triple_nodes__from_json_with_emb.npy"
        existing_embeddings = np.load(existing_node_path)
        print(f"   Existing node embeddings: {existing_embeddings.shape}")
        
        # Combine embeddings
        combined_embeddings = np.vstack([existing_embeddings, new_node_embeddings])
        print(f"   Combined node embeddings: {combined_embeddings.shape}")
        
        # Save combined embeddings
        np.save(existing_node_path, combined_embeddings)
        print(f"   ✅ Updated node embeddings saved")
        
        # Save node list mapping for reference
        node_list_path = self.vector_dir / f"extended_node_list_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(node_list_path, 'w') as f:
            f.write(f"# Extended node list after integration\n")
            f.write(f"# Existing nodes: 0 to {len(existing_embeddings)-1}\n")
            f.write(f"# New text nodes: {len(existing_embeddings)} to {len(combined_embeddings)-1}\n")
            for i, node_id in enumerate(new_node_list):
                f.write(f"{len(existing_embeddings) + i}: {node_id}\n")
        
        print(f"   ✅ Node list mapping saved: {node_list_path.name}")
        return True
    
    def update_hipporag2_data_structures(self, new_edge_list):
        """Update HippoRAG2 data structures to include new edges."""
        print("🔧 Updating HippoRAG2 data structures...")
        
        # Load the pipeline to get current data
        pipeline = HippoRAG2Pipeline(data_directory=str(self.data_directory))
        data = pipeline.load_existing_data()
        
        # Current edge list
        current_edge_list = data["edge_list"]
        print(f"   Current edge list: {len(current_edge_list):,} edges")
        
        # Add new edges to the edge list
        extended_edge_list = current_edge_list + new_edge_list
        print(f"   Extended edge list: {len(extended_edge_list):,} edges")
        
        # Note: We can't easily update the in-memory data structure without modifying
        # the HippoRAG2Pipeline code. For now, we'll document what needs to be done.
        
        update_notes_path = self.vector_dir / f"integration_notes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(update_notes_path, 'w') as f:
            f.write("# HippoRAG2 Integration Notes\n\n")
            f.write(f"After integration:\n")
            f.write(f"- Total edge embeddings: {len(current_edge_list) + len(new_edge_list):,}\n")
            f.write(f"- Original edges: 0 to {len(current_edge_list)-1:,}\n")
            f.write(f"- New entity-text edges: {len(current_edge_list):,} to {len(current_edge_list) + len(new_edge_list)-1:,}\n\n")
            f.write("TO COMPLETE INTEGRATION:\n")
            f.write("1. Modify HippoRAG2Pipeline to include new edges in edge_list\n")
            f.write("2. Ensure new edges are used in graph traversal\n")
            f.write("3. Test retrieval with extended edge connectivity\n")
        
        print(f"   ✅ Integration notes saved: {update_notes_path.name}")
    
    def run_integration(self):
        """Run the complete integration process."""
        print("🚀 Starting Embedding Integration")
        print("="*60)
        
        try:
            # Find generated files
            if not self.find_generated_embeddings():
                print("❌ No generated embedding files found")
                return False
            
            # Load generated embeddings
            new_edge_embeddings, new_edge_list, new_node_embeddings, new_node_list = self.load_generated_embeddings()
            
            if len(new_edge_embeddings) == 0 and len(new_node_embeddings) == 0:
                print("❌ No embeddings to integrate")
                return False
            
            # Create backups
            self.create_backups()
            
            # Integrate embeddings
            edge_success = self.integrate_edge_embeddings(new_edge_embeddings, new_edge_list)
            node_success = self.integrate_node_embeddings(new_node_embeddings, new_node_list)
            
            if edge_success:
                # Update data structures
                self.update_hipporag2_data_structures(new_edge_list)
            
            print(f"\n🎉 INTEGRATION COMPLETE!")
            print(f"   Edge embeddings integrated: {'✅' if edge_success else '❌'}")
            print(f"   Node embeddings integrated: {'✅' if node_success else '❌'}")
            
            if edge_success:
                print(f"\n⚠️  IMPORTANT: Manual steps needed to complete integration:")
                print(f"   1. Update HippoRAG2Pipeline to use extended edge list")
                print(f"   2. Test retrieval with new entity-text connectivity")
                print(f"   3. Verify MMC queries now return relevant results")
            
            return edge_success or node_success
            
        except Exception as e:
            print(f"❌ Integration failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Main function."""
    try:
        integrator = EmbeddingIntegrator()
        success = integrator.run_integration()
        return success
        
    except Exception as e:
        print(f"❌ Main execution failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)