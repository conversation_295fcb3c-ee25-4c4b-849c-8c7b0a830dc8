#!/usr/bin/env python3
"""
Robust Concept Generation Script with Checkpoint System
Designed to handle AFC rate limits and provide safe resume capability.
"""

from atlas_rag.kg_construction.triple_extraction import KnowledgeGraphExtractor
from atlas_rag.kg_construction.triple_config import ProcessingConfig
from setup_llm_generator_direct import setup_gemini_llm_generator_direct
from configparser import ConfigParser
import os
import json
import csv
import time
from datetime import datetime

class ConceptGenerationCheckpoint:
    """Manages checkpoint system for concept generation progress."""
    
    def __init__(self, dataset_name="360t_guide_direct_api_v2"):
        self.dataset_name = dataset_name
        self.checkpoint_file = f"import/{dataset_name}/concept_checkpoint.json"
        self.concept_file = f"import/{dataset_name}/concepts/concept_shard_0.csv"
        self.backup_file = f"import/{dataset_name}/concepts/concept_shard_0.csv.backup"
        
    def load_checkpoint(self):
        """Load existing checkpoint data."""
        if os.path.exists(self.checkpoint_file):
            with open(self.checkpoint_file, 'r') as f:
                return json.load(f)
        return {
            "completed_batches": 0,
            "total_batches": 0,
            "last_update": None,
            "concepts_generated": 0,
            "start_time": None
        }
    
    def save_checkpoint(self, checkpoint_data):
        """Save checkpoint data."""
        checkpoint_data["last_update"] = datetime.now().isoformat()
        with open(self.checkpoint_file, 'w') as f:
            json.dump(checkpoint_data, f, indent=2)
    
    def backup_concepts(self):
        """Create backup of existing concepts."""
        if os.path.exists(self.concept_file):
            import shutil
            shutil.copy2(self.concept_file, self.backup_file)
            print(f"✅ Backup created: {self.backup_file}")
    
    def count_existing_concepts(self):
        """Count concepts in existing CSV file."""
        if not os.path.exists(self.concept_file):
            return 0
        
        count = 0
        try:
            with open(self.concept_file, 'r') as f:
                reader = csv.reader(f)
                next(reader)  # Skip header
                for row in reader:
                    if len(row) >= 3:  # Valid concept row
                        count += 1
        except Exception as e:
            print(f"⚠️ Error counting concepts: {e}")
            return 0
        
        return count

def create_afc_aware_config(dataset_name="360t_guide_direct_api_v2"):
    """
    Create processing configuration optimized for AFC rate limits.
    AFC (Automatic Feedback Control) limits us to 10 remote calls.
    Uses larger batches (64 concepts) to minimize total API calls needed.
    """
    
    config = ConfigParser()
    config.read('config.ini')
    model_name = config['settings']['LLM_MODEL']
    
    print(f"🔧 Creating AFC-optimized processing configuration for {dataset_name}")
    print(f"Model: {model_name}")
    print(f"AFC-optimized settings: Large batches to minimize API calls within 'max remote calls: 10' limit")
    
    # AFC-optimized configuration - larger batches for efficiency
    processing_config = ProcessingConfig(
        model_path=model_name,
        data_directory="example_data",
        filename_pattern="",
        output_directory=f"import/{dataset_name}",
        
        # Optimized batches to minimize total API calls
        batch_size_triple=16,       
        batch_size_concept=10,      # Medium batches: 10 concepts per batch (reduces 274→55 batches)
        
        # Processing control
        total_shards_triple=1,
        current_shard_triple=0,
        total_shards_concept=1,
        current_shard_concept=0,
        
        # Optimized performance settings
        max_new_tokens=8192,        # Full context for quality concept generation
        max_workers=1,              # Sequential processing (safer for AFC)
        
        # Processing options
        use_8bit=False,
        debug_mode=False,
        resume_from=0,
        record=True,                # Enable detailed logging
        remove_doc_spaces=True
    )
    
    print("✅ AFC-optimized processing configuration created:")
    print(f"  - Batch size (concepts): {processing_config.batch_size_concept} (medium batches for balance)")
    print(f"  - Max tokens: {processing_config.max_new_tokens} (full context for quality)")
    print(f"  - Max workers: {processing_config.max_workers} (sequential)")
    print(f"  - Reduces 274 batches to ~55 batches (11 sessions within AFC 10-call limit)")
    
    return processing_config

def run_robust_concept_generation():
    """
    Run concept generation with checkpoint system and AFC awareness.
    """
    
    dataset_name = "360t_guide_direct_api_v2"
    checkpoint_manager = ConceptGenerationCheckpoint(dataset_name)
    
    print("🚀 AutoSchemaKG Robust Concept Generation")
    print(f"Dataset: {dataset_name}")
    print(f"AFC-optimized with checkpoint resume system")
    print("=" * 60)
    
    # Load checkpoint
    checkpoint = checkpoint_manager.load_checkpoint()
    existing_concepts = checkpoint_manager.count_existing_concepts()
    
    if existing_concepts > 0:
        print(f"📊 Found {existing_concepts} existing concepts")
        print(f"📋 Checkpoint: {checkpoint['completed_batches']} batches completed")
    else:
        print("🆕 Starting fresh concept generation")
    
    # Create backup
    checkpoint_manager.backup_concepts()
    
    # Setup direct Gemini API with enhanced error handling
    print("\\n🔌 Setting up AFC-aware Gemini API connection...")
    try:
        llm_generator = setup_gemini_llm_generator_direct()
        print("✅ Direct Gemini API connection established")
        print("⚠️ AFC rate limiting: Max 10 remote calls per session")
    except Exception as e:
        print(f"❌ Failed to setup Gemini API: {str(e)}")
        return False
    
    # Create AFC-aware configuration
    processing_config = create_afc_aware_config(dataset_name)
    
    # Initialize knowledge graph extractor
    print("\\n🏗️ Initializing knowledge graph extractor...")
    kg_extractor = KnowledgeGraphExtractor(
        model=llm_generator,
        config=processing_config
    )
    print("✅ Knowledge graph extractor initialized")
    
    # Start time tracking
    if not checkpoint.get("start_time"):
        checkpoint["start_time"] = datetime.now().isoformat()
    
    try:
        print("\\n🎯 Starting concept generation with AFC optimization...")
        print("⚡ Large batches (64 concepts each) minimize API calls for AFC efficiency")
        
        # Convert JSON to CSV if not done
        print("\\n1️⃣ Processing extraction JSON to CSV...")
        kg_extractor.convert_json_to_csv()
        print("✅ JSON to CSV conversion completed")
        
        # Generate concepts with checkpoint tracking
        print("\\n2️⃣ Generating concepts with checkpoint system...")
        start_time = time.time()
        
        # This will create moderate batches due to batch_size_concept=10
        kg_extractor.generate_concept_csv_temp(language='en')
        
        elapsed_time = time.time() - start_time
        print(f"✅ Concept generation completed in {elapsed_time:.1f} seconds")
        
        # Create final concept CSV files
        print("\\n3️⃣ Creating final concept CSV files...")
        kg_extractor.create_concept_csv()
        print("✅ Concept CSV files created")
        
        # Update final checkpoint
        final_concepts = checkpoint_manager.count_existing_concepts()
        checkpoint.update({
            "completed_batches": "all",
            "concepts_generated": final_concepts,
            "status": "completed",
            "end_time": datetime.now().isoformat()
        })
        checkpoint_manager.save_checkpoint(checkpoint)
        
        print("\\n🎉 Robust concept generation completed successfully!")
        print(f"\\n📊 Final Results:")
        print(f"  - Total concepts generated: {final_concepts}")
        print(f"  - Processing time: {elapsed_time:.1f} seconds")
        print(f"  - Average per concept: {elapsed_time/max(final_concepts,1):.1f} seconds")
        
        # Show sample concepts
        if os.path.exists(checkpoint_manager.concept_file):
            print(f"\\n📝 Sample concepts generated:")
            with open(checkpoint_manager.concept_file, 'r') as f:
                reader = csv.reader(f)
                next(reader)  # Skip header
                for i, row in enumerate(reader):
                    if i >= 3:  # Show first 3 concepts
                        break
                    if len(row) >= 2:
                        print(f"  • {row[1]}")
        
        return True
        
    except KeyboardInterrupt:
        print("\\n⏸️ Process interrupted by user")
        print("💾 Progress saved in checkpoint - can resume later")
        return False
        
    except Exception as e:
        print(f"\\n❌ Concept generation failed: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        
        # Save error in checkpoint for debugging
        checkpoint.update({
            "last_error": str(e),
            "error_type": type(e).__name__,
            "error_time": datetime.now().isoformat()
        })
        checkpoint_manager.save_checkpoint(checkpoint)
        
        print("\\n🔧 Troubleshooting suggestions:")
        print("1. Check if AFC rate limit was exceeded (max 10 calls)")
        print("2. Wait a few minutes before retrying")
        print("3. Reduce batch_size_concept further (currently 2)")
        print("4. Check network connectivity and API key")
        
        return False

def show_checkpoint_status(dataset_name="360t_guide_direct_api_v2"):
    """Display current checkpoint status."""
    checkpoint_manager = ConceptGenerationCheckpoint(dataset_name)
    checkpoint = checkpoint_manager.load_checkpoint()
    concepts_count = checkpoint_manager.count_existing_concepts()
    
    print("\\n📋 Checkpoint Status:")
    print(f"  - Concepts generated: {concepts_count}")
    print(f"  - Completed batches: {checkpoint.get('completed_batches', 0)}")
    print(f"  - Last update: {checkpoint.get('last_update', 'Never')}")
    print(f"  - Status: {checkpoint.get('status', 'Not started')}")

if __name__ == "__main__":
    print("🎯 AutoSchemaKG Robust Concept Generation")
    print("Designed for AFC rate limit compatibility with checkpoint resume")
    print()
    
    # Show current status
    show_checkpoint_status()
    
    print("\\n" + "=" * 60)
    success = run_robust_concept_generation()
    
    if success:
        print("\\n✅ Concept generation completed successfully!")
        print("\\n🎯 Next steps:")
        print("1. Re-generate GraphML with concepts included")
        print("2. Import complete knowledge graph into Neo4j")
        print("3. Verify concept quality and coverage")
    else:
        print("\\n⚠️ Concept generation incomplete")
        print("Run the script again to resume from checkpoint")
        show_checkpoint_status()