#!/usr/bin/env python3
"""
HippoRAG2 Embeddings Diagnostic Tool

This script diagnoses the mathematical warnings and retrieval quality issues in HippoRAG2:
1. Validates embedding arrays for mathematical issues (zeros, NaNs, infinities)
2. Tests query embedding generation
3. Simulates problematic matrix operations
4. Analyzes data content for domain relevance

Issues to diagnose:
- RuntimeWarning: divide by zero encountered in matmul
- RuntimeWarning: overflow encountered in matmul  
- RuntimeWarning: invalid value encountered in matmul
- Poor retrieval quality (irrelevant results)
"""

import os
import sys
import numpy as np
import pandas as pd
import warnings
from pathlib import Path
import json
from hipporag2_pipeline import HippoRAG2Pipeline

# Capture warnings to analyze them
warnings.filterwarnings('error', category=RuntimeWarning)

class HippoRAG2Diagnostics:
    def __init__(self, data_directory="import/pdf_dataset"):
        self.data_directory = Path(data_directory)
        self.pipeline = None
        self.data = None
        self.issues_found = []
        
    def setup_pipeline(self):
        """Initialize the HippoRAG2 pipeline for testing."""
        print("🔧 Setting up HippoRAG2 pipeline...")
        try:
            self.pipeline = HippoRAG2Pipeline(data_directory=str(self.data_directory))
            self.data = self.pipeline.load_existing_data()
            self.pipeline.setup_models()
            print("✅ Pipeline setup completed")
            return True
        except Exception as e:
            print(f"❌ Pipeline setup failed: {e}")
            return False

    def validate_embedding_arrays(self):
        """Validate all embedding arrays for mathematical issues."""
        print("\n" + "="*60)
        print("🔍 PHASE 1: EMBEDDING ARRAYS VALIDATION")
        print("="*60)
        
        arrays_to_check = {
            "node_embeddings": self.data.get("node_embeddings"),
            "edge_embeddings": self.data.get("edge_embeddings"), 
            "text_embeddings": self.data.get("text_embeddings")
        }
        
        for name, array in arrays_to_check.items():
            if array is None or len(array) == 0:
                print(f"⚠️  {name}: EMPTY OR MISSING")
                self.issues_found.append(f"{name}_missing")
                continue
                
            print(f"\n📊 Analyzing {name}:")
            print(f"   Shape: {array.shape}")
            print(f"   Data type: {array.dtype}")
            
            # Check for problematic values
            issues = self._check_array_health(array, name)
            
            # Check embedding norms
            self._check_embedding_norms(array, name)
            
            # Statistical analysis
            self._statistical_analysis(array, name)
            
        return len(self.issues_found) == 0

    def _check_array_health(self, array, name):
        """Check for NaN, Inf, zeros, and extreme values."""
        issues = []
        
        # Check for NaN values
        nan_count = np.isnan(array).sum()
        if nan_count > 0:
            print(f"   ❌ NaN values: {nan_count:,} ({nan_count/array.size*100:.2f}%)")
            issues.append(f"{name}_nan")
            self.issues_found.append(f"{name}_has_nan_values")
        else:
            print(f"   ✅ No NaN values")
            
        # Check for infinite values
        inf_count = np.isinf(array).sum()
        if inf_count > 0:
            print(f"   ❌ Infinite values: {inf_count:,} ({inf_count/array.size*100:.2f}%)")
            issues.append(f"{name}_inf")
            self.issues_found.append(f"{name}_has_infinite_values")
        else:
            print(f"   ✅ No infinite values")
            
        # Check for zero vectors (entire embedding is zero)
        zero_vectors = np.all(array == 0, axis=1).sum()
        if zero_vectors > 0:
            print(f"   ⚠️  Zero vectors: {zero_vectors:,} ({zero_vectors/len(array)*100:.2f}%)")
            issues.append(f"{name}_zero_vectors")
            self.issues_found.append(f"{name}_has_zero_vectors")
        else:
            print(f"   ✅ No zero vectors")
            
        # Check for extreme values
        abs_array = np.abs(array)
        max_val = np.max(abs_array)
        min_val = np.min(abs_array)
        
        print(f"   📈 Value range: [{min_val:.6f}, {max_val:.6f}]")
        
        if max_val > 1e6:
            print(f"   ⚠️  Very large values detected (max: {max_val:.2e})")
            issues.append(f"{name}_extreme_large")
            self.issues_found.append(f"{name}_has_extreme_large_values")
            
        if min_val < 1e-6 and min_val > 0:
            print(f"   ⚠️  Very small values detected (min: {min_val:.2e})")
            issues.append(f"{name}_extreme_small")
            
        return issues

    def _check_embedding_norms(self, array, name):
        """Check L2 norms of embeddings."""
        norms = np.linalg.norm(array, axis=1)
        
        print(f"   📐 L2 Norms:")
        print(f"      Mean: {np.mean(norms):.6f}")
        print(f"      Std:  {np.std(norms):.6f}")
        print(f"      Min:  {np.min(norms):.6f}")
        print(f"      Max:  {np.max(norms):.6f}")
        
        # Check for zero norms (problematic for normalization)
        zero_norm_count = (norms == 0).sum()
        if zero_norm_count > 0:
            print(f"   ❌ Zero norm vectors: {zero_norm_count:,}")
            self.issues_found.append(f"{name}_zero_norms")
        
        # Check for very small norms
        small_norm_count = (norms < 1e-6).sum()
        if small_norm_count > 0:
            print(f"   ⚠️  Very small norms (<1e-6): {small_norm_count:,}")

    def _statistical_analysis(self, array, name):
        """Perform statistical analysis on embeddings."""
        print(f"   📊 Statistics:")
        print(f"      Mean: {np.mean(array):.6f}")
        print(f"      Std:  {np.std(array):.6f}")
        print(f"      Skew: {self._calculate_skewness(array):.6f}")
        
        # Check for unusual distributions
        if abs(np.mean(array)) > 0.1:
            print(f"   ⚠️  Large mean - embeddings may not be centered")
            
        if np.std(array) > 10 or np.std(array) < 0.01:
            print(f"   ⚠️  Unusual standard deviation")

    def _calculate_skewness(self, array):
        """Calculate skewness of the array."""
        mean = np.mean(array)
        std = np.std(array)
        if std == 0:
            return 0
        return np.mean(((array - mean) / std) ** 3)

    def test_query_processing(self):
        """Test query embedding generation and identify issues."""
        print("\n" + "="*60)
        print("🔍 PHASE 2: QUERY PROCESSING VALIDATION")
        print("="*60)
        
        test_queries = [
            "What are the step of creating a new scenario in MMC?",  # Original failing query
            "How to connect to 360T?",  # Should work with current data
            "Network connectivity options",  # Should work with current data
            "Simple test query"  # Basic test
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n🤔 Testing Query {i}: '{query}'")
            
            try:
                # Test different query types
                query_types = ["edge", "entity", "passage"]
                
                for query_type in query_types:
                    try:
                        print(f"   Testing {query_type} embedding...")
                        query_emb = self.pipeline.sentence_encoder.encode([query], query_type=query_type)
                        
                        print(f"      Shape: {query_emb.shape}")
                        print(f"      Data type: {query_emb.dtype}")
                        
                        # Check query embedding health
                        if np.isnan(query_emb).any():
                            print(f"      ❌ Query embedding contains NaN!")
                            self.issues_found.append(f"query_embedding_nan_{query_type}")
                            
                        if np.isinf(query_emb).any():
                            print(f"      ❌ Query embedding contains Inf!")
                            self.issues_found.append(f"query_embedding_inf_{query_type}")
                            
                        norm = np.linalg.norm(query_emb)
                        print(f"      L2 norm: {norm:.6f}")
                        
                        if norm == 0:
                            print(f"      ❌ Query embedding has zero norm!")
                            self.issues_found.append(f"query_embedding_zero_norm_{query_type}")
                        else:
                            print(f"      ✅ Query embedding looks healthy")
                            
                    except Exception as e:
                        print(f"      ❌ Error generating {query_type} embedding: {e}")
                        self.issues_found.append(f"query_embedding_error_{query_type}")
                        
            except Exception as e:
                print(f"   ❌ Error processing query: {e}")
                self.issues_found.append(f"query_processing_error")

    def test_matrix_operations(self):
        """Test the problematic matrix multiplication operations."""
        print("\n" + "="*60)
        print("🔍 PHASE 3: MATRIX OPERATIONS TESTING")
        print("="*60)
        
        test_query = "How to connect to 360T?"
        
        print(f"🧪 Testing matrix operations with query: '{test_query}'")
        
        # Test edge embedding operation (line 124)
        print(f"\n📊 Testing edge embedding operation...")
        try:
            query_emb = self.pipeline.sentence_encoder.encode([test_query], query_type="edge")
            print(f"   Query embedding shape: {query_emb.shape}")
            print(f"   Edge embeddings shape: {self.data['edge_embeddings'].shape}")
            
            # The problematic operation
            with warnings.catch_warnings(record=True) as w:
                warnings.simplefilter("always")
                
                result = self.data['edge_embeddings'] @ query_emb[0].T
                
                if w:
                    print(f"   ❌ Warnings during matrix multiplication:")
                    for warning in w:
                        print(f"      - {warning.message}")
                        self.issues_found.append(f"edge_matmul_warning: {warning.message}")
                else:
                    print(f"   ✅ No warnings during matrix multiplication")
                    
                print(f"   Result shape: {result.shape}")
                print(f"   Result range: [{np.min(result):.6f}, {np.max(result):.6f}]")
                
                # Check result for problematic values
                if np.isnan(result).any():
                    print(f"   ❌ Result contains NaN values!")
                    self.issues_found.append("edge_matmul_result_nan")
                if np.isinf(result).any():
                    print(f"   ❌ Result contains infinite values!")
                    self.issues_found.append("edge_matmul_result_inf")
                    
        except Exception as e:
            print(f"   ❌ Error during edge matrix operation: {e}")
            self.issues_found.append(f"edge_matmul_error: {e}")

        # Test text embedding operation (line 180)
        print(f"\n📊 Testing text embedding operation...")
        try:
            query_emb = self.pipeline.sentence_encoder.encode([test_query], query_type="passage")
            print(f"   Query embedding shape: {query_emb.shape}")
            print(f"   Text embeddings shape: {self.data['text_embeddings'].shape}")
            
            # The problematic operation
            with warnings.catch_warnings(record=True) as w:
                warnings.simplefilter("always")
                
                result = self.data['text_embeddings'] @ query_emb[0].T
                
                if w:
                    print(f"   ❌ Warnings during matrix multiplication:")
                    for warning in w:
                        print(f"      - {warning.message}")
                        self.issues_found.append(f"text_matmul_warning: {warning.message}")
                else:
                    print(f"   ✅ No warnings during matrix multiplication")
                    
                print(f"   Result shape: {result.shape}")
                print(f"   Result range: [{np.min(result):.6f}, {np.max(result):.6f}]")
                
                # Check result for problematic values
                if np.isnan(result).any():
                    print(f"   ❌ Result contains NaN values!")
                    self.issues_found.append("text_matmul_result_nan")
                if np.isinf(result).any():
                    print(f"   ❌ Result contains infinite values!")
                    self.issues_found.append("text_matmul_result_inf")
                    
        except Exception as e:
            print(f"   ❌ Error during text matrix operation: {e}")
            self.issues_found.append(f"text_matmul_error: {e}")

    def analyze_content_domain(self):
        """Analyze the indexed content for domain relevance."""
        print("\n" + "="*60)
        print("🔍 PHASE 4: CONTENT DOMAIN ANALYSIS") 
        print("="*60)
        
        print(f"📊 Knowledge Graph Structure:")
        graph = self.data["KG"]
        print(f"   Nodes: {len(graph.nodes):,}")
        print(f"   Edges: {len(graph.edges):,}")
        
        # Sample some node content to understand the domain
        print(f"\n🔍 Sampling node content (first 10 nodes):")
        node_sample = list(graph.nodes())[:10]
        
        for i, node_id in enumerate(node_sample, 1):
            node_data = graph.nodes[node_id]
            node_name = node_data.get('name', 'No name')
            node_type = node_data.get('type', 'No type')
            print(f"   {i}. [{node_type}] {node_name}")
            
        # Check text dictionary content
        print(f"\n📚 Text Dictionary Sample (first 5 entries):")
        text_dict = self.data.get("text_dict", {})
        sample_keys = list(text_dict.keys())[:5]
        
        for i, key in enumerate(sample_keys, 1):
            text_content = text_dict[key][:200] + "..." if len(text_dict[key]) > 200 else text_dict[key]
            print(f"   {i}. [{key}] {text_content}")
            
        # Look for MMC-related content
        print(f"\n🔍 Searching for MMC-related content...")
        mmc_terms = ["MMC", "Market Management Console", "scenario", "Market Maker", "management console"]
        
        mmc_found = False
        for term in mmc_terms:
            # Search in node names
            node_matches = 0
            for node_id in graph.nodes():
                node_name = graph.nodes[node_id].get('name', '').lower()
                if term.lower() in node_name:
                    node_matches += 1
                    
            # Search in text content
            text_matches = 0
            for text_content in text_dict.values():
                if term.lower() in text_content.lower():
                    text_matches += 1
                    
            if node_matches > 0 or text_matches > 0:
                print(f"   ✅ '{term}': {node_matches} node matches, {text_matches} text matches")
                mmc_found = True
            else:
                print(f"   ❌ '{term}': No matches found")
                
        if not mmc_found:
            print(f"\n⚠️  No MMC-related content found in knowledge graph!")
            print(f"   This explains why MMC queries return irrelevant results.")
            self.issues_found.append("no_mmc_content")
            
        # Identify the actual domain
        print(f"\n🏷️  Identifying actual domain...")
        domain_keywords = {
            "trading": ["trading", "trade", "market", "FIX", "order", "execution"],
            "360T": ["360T", "360t", "supersonic", "bridge", "ECN"],
            "financial": ["currency", "FX", "swap", "settlement", "notional"],
            "network": ["connectivity", "network", "connection", "TCP", "SSL", "protocol"]
        }
        
        for domain, keywords in domain_keywords.items():
            matches = 0
            for keyword in keywords:
                for text_content in text_dict.values():
                    if keyword.lower() in text_content.lower():
                        matches += 1
                        break  # Count each keyword only once
                        
            if matches > 0:
                print(f"   📊 {domain.upper()}: {matches}/{len(keywords)} keyword matches")

    def generate_diagnostic_report(self):
        """Generate a comprehensive diagnostic report."""
        print("\n" + "="*60)
        print("📋 DIAGNOSTIC REPORT SUMMARY")  
        print("="*60)
        
        if not self.issues_found:
            print("🎉 No critical issues detected!")
            return True
            
        print(f"❌ Total issues found: {len(self.issues_found)}")
        
        # Categorize issues
        categories = {
            "Mathematical Issues": [issue for issue in self.issues_found if any(x in issue for x in ["nan", "inf", "zero", "matmul", "norm"])],
            "Data Quality Issues": [issue for issue in self.issues_found if any(x in issue for x in ["missing", "extreme", "embedding"])],
            "Content Issues": [issue for issue in self.issues_found if any(x in issue for x in ["mmc", "content", "domain"])],
            "Query Processing Issues": [issue for issue in self.issues_found if "query" in issue]
        }
        
        for category, issues in categories.items():
            if issues:
                print(f"\n🔴 {category}:")
                for issue in issues:
                    print(f"   - {issue}")
                    
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        
        if any("nan" in issue or "inf" in issue for issue in self.issues_found):
            print("   1. Clean embedding arrays: Remove or fix NaN/Inf values")
            
        if any("zero" in issue for issue in self.issues_found):
            print("   2. Handle zero embeddings: Regenerate or exclude zero vectors")
            
        if any("matmul" in issue for issue in self.issues_found):
            print("   3. Add robust error handling to matrix operations")
            
        if "no_mmc_content" in self.issues_found:
            print("   4. Index MMC-related documents or adjust query expectations")
            
        return False

def main():
    """Main diagnostic function."""
    print("🚀 HippoRAG2 Embeddings Diagnostic Tool")
    print("="*60)
    
    try:
        diagnostics = HippoRAG2Diagnostics()
        
        # Setup pipeline
        if not diagnostics.setup_pipeline():
            return False
            
        # Run all diagnostic phases
        diagnostics.validate_embedding_arrays()
        diagnostics.test_query_processing()  
        diagnostics.test_matrix_operations()
        diagnostics.analyze_content_domain()
        
        # Generate report
        success = diagnostics.generate_diagnostic_report()
        
        return success
        
    except Exception as e:
        print(f"\n❌ Diagnostic failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)