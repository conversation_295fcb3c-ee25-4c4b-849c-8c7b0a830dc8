#!/usr/bin/env python3
"""
Analyze Missing Embeddings in HippoRAG2

This script investigates the three critical embedding issues:
1. 49,008 text edges without embeddings  
2. 1,207 nodes without embeddings
3. Why high-scoring entity matches don't reach final results

The goal is to understand the graph structure and identify what's missing.
"""

import networkx as nx
import numpy as np
import pandas as pd
from pathlib import Path
from collections import defaultdict, Counter
from hipporag2_pipeline import HippoRAG2Pipeline

class EmbeddingAnalyzer:
    def __init__(self, data_directory="import/pdf_dataset"):
        self.data_directory = Path(data_directory)
        self.pipeline = None
        self.data = None
        self.analysis_results = {}
        
    def load_data(self):
        """Load the HippoRAG2 data for analysis."""
        print("🔍 Loading HippoRAG2 data for analysis...")
        
        self.pipeline = HippoRAG2Pipeline(data_directory=str(self.data_directory))
        self.data = self.pipeline.load_existing_data()
        
        print(f"✅ Data loaded:")
        print(f"   Graph: {len(self.data['KG'].nodes)} nodes, {len(self.data['KG'].edges)} edges")
        print(f"   Node embeddings: {self.data['node_embeddings'].shape}")
        print(f"   Edge embeddings: {self.data['edge_embeddings'].shape}")
    
    def analyze_edge_types(self):
        """Analyze different types of edges in the knowledge graph."""
        print(f"\n📊 ANALYZING EDGE TYPES")
        print("="*50)
        
        graph = self.data["KG"]
        total_edges = len(graph.edges)
        embedded_edges = len(self.data["edge_list"])  # Edges with embeddings
        missing_edges = total_edges - embedded_edges
        
        print(f"Total edges in graph: {total_edges:,}")
        print(f"Edges with embeddings: {embedded_edges:,}")
        print(f"Edges without embeddings: {missing_edges:,}")
        
        # Analyze edge attributes to understand types
        edge_types = Counter()
        edge_relations = Counter()
        
        for edge in graph.edges(data=True):
            source, target, edge_data = edge
            
            # Check edge type
            edge_type = edge_data.get('type', 'unknown')
            edge_types[edge_type] += 1
            
            # Check relation type
            relation = edge_data.get('relation', 'unknown')
            edge_relations[relation] += 1
        
        print(f"\n📋 Edge Types:")
        for edge_type, count in edge_types.most_common():
            print(f"   {edge_type}: {count:,}")
            
        print(f"\n📋 Top Relations:")
        for relation, count in edge_relations.most_common(10):
            print(f"   '{relation}': {count:,}")
        
        # Identify which edges have embeddings vs which don't
        embedded_edge_set = set(self.data["edge_list"])
        all_edge_set = set(graph.edges())
        missing_edge_set = all_edge_set - embedded_edge_set
        
        print(f"\n🔍 Missing Edge Analysis:")
        print(f"   Missing edges: {len(missing_edge_set):,}")
        
        # Analyze properties of missing edges
        missing_types = Counter()
        missing_relations = Counter()
        
        for edge in missing_edge_set:
            edge_data = graph.edges[edge]
            edge_type = edge_data.get('type', 'unknown')
            relation = edge_data.get('relation', 'unknown')
            
            missing_types[edge_type] += 1
            missing_relations[relation] += 1
        
        print(f"\n❌ Missing Edge Types:")
        for edge_type, count in missing_types.most_common():
            print(f"   {edge_type}: {count:,}")
            
        print(f"\n❌ Missing Edge Relations:")
        for relation, count in missing_relations.most_common(10):
            print(f"   '{relation}': {count:,}")
        
        # Store results
        self.analysis_results['edges'] = {
            'total': total_edges,
            'embedded': embedded_edges,
            'missing': missing_edges,
            'missing_types': dict(missing_types),
            'missing_relations': dict(missing_relations),
            'missing_edge_set': missing_edge_set
        }
    
    def analyze_node_types(self):
        """Analyze different types of nodes and missing node embeddings."""
        print(f"\n📊 ANALYZING NODE TYPES")
        print("="*50)
        
        graph = self.data["KG"]
        total_nodes = len(graph.nodes)
        embedded_nodes = len(self.data["node_list"])  # Should match embedding count
        embedding_count = self.data["node_embeddings"].shape[0]
        
        print(f"Total nodes in graph: {total_nodes:,}")
        print(f"Nodes in node_list: {embedded_nodes:,}")
        print(f"Node embeddings available: {embedding_count:,}")
        
        missing_nodes = total_nodes - embedding_count
        print(f"Nodes without embeddings: {missing_nodes:,}")
        
        # Analyze node types
        node_types = Counter()
        for node_id in graph.nodes():
            node_data = graph.nodes[node_id]
            node_type = node_data.get('type', 'unknown')
            node_types[node_type] += 1
            
        print(f"\n📋 Node Types:")
        for node_type, count in node_types.most_common():
            print(f"   {node_type}: {count:,}")
        
        # If we have missing nodes, try to identify them
        if missing_nodes > 0:
            # Get node_list as set for comparison
            embedded_node_set = set(self.data["node_list"])
            all_node_set = set(graph.nodes())
            missing_node_set = all_node_set - embedded_node_set
            
            print(f"\n❌ Missing Node Analysis:")
            print(f"   Actually missing nodes: {len(missing_node_set):,}")
            
            missing_node_types = Counter()
            for node_id in missing_node_set:
                node_data = graph.nodes[node_id]
                node_type = node_data.get('type', 'unknown')
                missing_node_types[node_type] += 1
                
            print(f"\n❌ Missing Node Types:")
            for node_type, count in missing_node_types.most_common():
                print(f"   {node_type}: {count:,}")
                
            # Sample some missing nodes
            print(f"\n🔍 Sample Missing Nodes:")
            for i, node_id in enumerate(list(missing_node_set)[:5], 1):
                node_data = graph.nodes[node_id]
                node_name = node_data.get('name', str(node_id))
                node_type = node_data.get('type', 'unknown')
                print(f"   {i}. [{node_type}] {node_name}")
        
        # Store results
        self.analysis_results['nodes'] = {
            'total': total_nodes,
            'embedded': embedding_count,
            'missing': missing_nodes,
            'node_types': dict(node_types)
        }
    
    def analyze_entity_to_text_connectivity(self):
        """Analyze how entities connect to text passages through the graph."""
        print(f"\n📊 ANALYZING ENTITY-TO-TEXT CONNECTIVITY")
        print("="*50)
        
        graph = self.data["KG"]
        
        # Find entity nodes and text nodes
        entity_nodes = []
        text_nodes = []
        
        for node_id in graph.nodes():
            node_data = graph.nodes[node_id]
            node_type = node_data.get('type', 'unknown')
            
            if node_type == 'entity':
                entity_nodes.append(node_id)
            elif node_type in ['passage', 'text']:
                text_nodes.append(node_id)
        
        print(f"Entity nodes: {len(entity_nodes):,}")
        print(f"Text nodes: {len(text_nodes):,}")
        
        # Analyze connectivity between entities and text
        entity_to_text_edges = 0
        text_to_entity_edges = 0
        
        for edge in graph.edges(data=True):
            source, target, edge_data = edge
            
            source_type = graph.nodes[source].get('type', 'unknown')
            target_type = graph.nodes[target].get('type', 'unknown')
            
            if source_type == 'entity' and target_type in ['passage', 'text']:
                entity_to_text_edges += 1
            elif source_type in ['passage', 'text'] and target_type == 'entity':
                text_to_entity_edges += 1
        
        print(f"\nEntity-to-text edges: {entity_to_text_edges:,}")
        print(f"Text-to-entity edges: {text_to_entity_edges:,}")
        
        total_entity_text_edges = entity_to_text_edges + text_to_entity_edges
        print(f"Total entity-text connections: {total_entity_text_edges:,}")
        
        # Check which of these edges have embeddings
        missing_edge_set = self.analysis_results['edges']['missing_edge_set']
        
        entity_text_missing = 0
        for edge in missing_edge_set:
            source, target = edge
            source_type = graph.nodes[source].get('type', 'unknown')
            target_type = graph.nodes[target].get('type', 'unknown')
            
            if ((source_type == 'entity' and target_type in ['passage', 'text']) or
                (source_type in ['passage', 'text'] and target_type == 'entity')):
                entity_text_missing += 1
        
        print(f"\n❌ Entity-text edges without embeddings: {entity_text_missing:,}")
        print(f"   This is likely the core issue breaking retrieval!")
        
        # Store results
        self.analysis_results['connectivity'] = {
            'entity_nodes': len(entity_nodes),
            'text_nodes': len(text_nodes),
            'entity_to_text_edges': entity_to_text_edges,
            'text_to_entity_edges': text_to_entity_edges,
            'total_entity_text_edges': total_entity_text_edges,
            'entity_text_missing': entity_text_missing
        }
    
    def analyze_mmc_connectivity(self):
        """Specifically analyze MMC entity connectivity to text passages."""
        print(f"\n📊 ANALYZING MMC CONNECTIVITY")
        print("="*50)
        
        graph = self.data["KG"]
        
        # Find MMC-related entities
        mmc_entities = []
        for node_id in graph.nodes():
            node_data = graph.nodes[node_id]
            node_name = node_data.get('name', '').lower()
            node_type = node_data.get('type', 'unknown')
            
            if node_type == 'entity' and 'mmc' in node_name:
                mmc_entities.append((node_id, node_data.get('name', node_id)))
        
        print(f"Found {len(mmc_entities)} MMC entities:")
        for i, (node_id, name) in enumerate(mmc_entities[:10], 1):
            print(f"   {i}. {name}")
        
        if len(mmc_entities) > 10:
            print(f"   ... and {len(mmc_entities) - 10} more")
        
        # Check connectivity for top MMC entities
        if mmc_entities:
            print(f"\n🔍 Checking connectivity for top MMC entities:")
            
            for i, (entity_id, entity_name) in enumerate(mmc_entities[:3], 1):
                print(f"\n   {i}. '{entity_name}':")
                
                # Find connected nodes
                connected_nodes = list(graph.neighbors(entity_id))
                print(f"      Connected to {len(connected_nodes)} nodes")
                
                # Check types of connected nodes
                connected_types = Counter()
                text_connections = []
                
                for neighbor in connected_nodes:
                    neighbor_type = graph.nodes[neighbor].get('type', 'unknown')
                    connected_types[neighbor_type] += 1
                    
                    if neighbor_type in ['passage', 'text']:
                        neighbor_text = self.data["text_dict"].get(neighbor, "No text")[:100]
                        text_connections.append((neighbor, neighbor_text))
                
                print(f"      Connected node types: {dict(connected_types)}")
                
                if text_connections:
                    print(f"      Text connections:")
                    for j, (text_id, text_preview) in enumerate(text_connections[:2], 1):
                        print(f"         {j}. {text_preview}...")
                else:
                    print(f"      ❌ No text connections found!")
                
                # Check if edges to text have embeddings
                missing_edge_set = self.analysis_results['edges']['missing_edge_set']
                
                edges_to_text = []
                missing_edges_to_text = []
                
                for neighbor in connected_nodes:
                    neighbor_type = graph.nodes[neighbor].get('type', 'unknown')
                    if neighbor_type in ['passage', 'text']:
                        edge = (entity_id, neighbor) if (entity_id, neighbor) in graph.edges else (neighbor, entity_id)
                        if edge in graph.edges:
                            edges_to_text.append(edge)
                            if edge in missing_edge_set:
                                missing_edges_to_text.append(edge)
                
                print(f"      Edges to text: {len(edges_to_text)}")
                print(f"      Missing embeddings: {len(missing_edges_to_text)}")
                
                if missing_edges_to_text:
                    print(f"      ❌ This explains why MMC queries fail!")
    
    def generate_summary_report(self):
        """Generate a comprehensive summary of the embedding issues."""
        print(f"\n" + "="*60)
        print(f"📋 COMPREHENSIVE EMBEDDING ANALYSIS SUMMARY")
        print("="*60)
        
        edges = self.analysis_results['edges']
        nodes = self.analysis_results['nodes'] 
        connectivity = self.analysis_results['connectivity']
        
        print(f"🔴 **CRITICAL ISSUES IDENTIFIED:**")
        print(f"\n1. **Missing Text Edge Embeddings:** {edges['missing']:,}")
        print(f"   - This breaks entity-to-passage connectivity")
        print(f"   - Prevents high-scoring entities from reaching their text")
        print(f"   - Core cause of retrieval quality problems")
        
        print(f"\n2. **Missing Node Embeddings:** {nodes['missing']:,}")
        print(f"   - These nodes can't participate in similarity search")
        print(f"   - May include important text passages")
        
        print(f"\n3. **Entity-Text Connectivity Issues:**")
        print(f"   - Entity nodes: {connectivity['entity_nodes']:,}")
        print(f"   - Text nodes: {connectivity['text_nodes']:,}")
        print(f"   - Entity-text edges: {connectivity['total_entity_text_edges']:,}")
        print(f"   - Missing embeddings: {connectivity['entity_text_missing']:,}")
        
        print(f"\n💡 **ROOT CAUSE:**")
        print(f"The HippoRAG2 algorithm relies on graph traversal from entities to text.")
        print(f"Without embeddings on entity-text edges, this traversal fails, causing")
        print(f"the system to fall back to generic passage similarity search.")
        
        print(f"\n🎯 **SOLUTION PRIORITY:**")
        print(f"1. Generate embeddings for {connectivity['entity_text_missing']:,} entity-text edges")
        print(f"2. Generate embeddings for {nodes['missing']:,} missing nodes")
        print(f"3. Test MMC query connectivity specifically")
        
        print(f"\n📊 **EXPECTED IMPACT:**")
        print(f"Fixing these issues should enable:")
        print(f"- MMC queries to reach MMC documentation")
        print(f"- High-scoring entity matches (1.0000) to propagate to text")
        print(f"- Proper graph-based retrieval instead of generic fallback")

def main():
    """Main analysis function."""
    print("🔍 Missing Embeddings Analysis for HippoRAG2")
    print("="*60)
    
    try:
        analyzer = EmbeddingAnalyzer()
        analyzer.load_data()
        
        # Run all analyses
        analyzer.analyze_edge_types()
        analyzer.analyze_node_types()
        analyzer.analyze_entity_to_text_connectivity()
        analyzer.analyze_mmc_connectivity()
        
        # Generate summary
        analyzer.generate_summary_report()
        
        print(f"\n✅ Analysis complete!")
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)