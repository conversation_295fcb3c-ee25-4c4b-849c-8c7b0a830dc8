#!/usr/bin/env python3
"""
Test the entity-based retrieval fix for Risk Reversal queries
"""

import os
import sys
import pickle
from pathlib import Path

# Set environment and add path
os.environ["TOKENIZERS_PARALLELISM"] = "false"
sys.path.insert(0, str(Path(__file__).parent))

from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
from atlas_rag.retriever.inference_config import InferenceConfig
from setup_embedding_model import setup_embedding_model
from setup_llm_generator import setup_llm_generator

def main():
    print("🎯 TESTING ENTITY-BASED RETRIEVAL FIX")
    print("="*60)
    print("Testing Risk Reversal queries with query2node mode")
    
    # Setup components
    print("🔧 Setting up components...")
    
    try:
        embedding_model = setup_embedding_model()
        print("   ✅ Embedding model ready")
    except Exception as e:
        print(f"   ❌ Embedding model error: {e}")
        return
    
    try:
        llm_generator = setup_llm_generator()  
        print("   ✅ LLM generator ready")
    except Exception as e:
        print(f"   ❌ LLM generator error: {e}")
        return
    
    # Load data
    print("📊 Loading complete data...")
    data_file = Path("import/pdf_dataset/complete_data.pkl")
    
    try:
        with open(data_file, 'rb') as f:
            complete_data = pickle.load(f)
        print("   ✅ Data loaded successfully")
    except Exception as e:
        print(f"   ❌ Data loading error: {e}")
        return
    
    # Setup enhanced inference config for financial content
    print("⚙️  Setting up enhanced inference config...")
    inference_config = InferenceConfig()
    inference_config.topk_edges = 30    # Increased for better entity discovery
    inference_config.ppr_alpha = 0.15   # Balanced exploration
    inference_config.ppr_max_iter = 200 # More iterations for thorough propagation
    inference_config.weight_adjust = 0.1 # Stronger text similarity weighting
    
    # Initialize HippoRAG2 with entity-based retrieval
    print("🚀 Initializing HippoRAG2 with entity-based retrieval...")
    
    try:
        retriever = HippoRAG2Retriever(
            llm_generator=llm_generator,
            sentence_encoder=embedding_model,
            data=complete_data,
            inference_config=inference_config
        )
        print("   ✅ HippoRAG2 initialized successfully")
        print(f"   📊 Mode: {retriever.retrieve_node_fn.__name__} (should be 'query2node')")
    except Exception as e:
        print(f"   ❌ HippoRAG2 initialization error: {e}")
        return
    
    # Test queries
    print(f"\n🔍 TESTING FINANCIAL QUERIES:")
    print("="*60)
    
    test_queries = [
        "what is a risk reversal option strategy and how to create one in Bridge?",
        "how does MMC Market Maker Cockpit work for FX trading?", 
        "explain the zero cost strategy option pricing model",
        "what are the key features of the SEF Market Maker platform?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🎯 TEST #{i}: {query}")
        print("-" * 50)
        
        try:
            # Retrieve passages
            passages, passage_ids = retriever.retrieve(query, topN=5)
            
            print(f"📊 Retrieved {len(passages)} passages:")
            
            # Check for financial content
            financial_keywords = [
                'risk reversal', 'Risk Reversal', 'RISK REVERSAL',
                'mmc', 'MMC', 'Market Maker Cockpit',
                'zero cost', 'Zero Cost', 'ZERO COST',
                'SEF', 'sef', 'strategy option', 'Strategy Option',
                'FX', 'fx', 'option', 'Option', 'pricing', 'Pricing'
            ]
            
            found_relevant = False
            for j, (passage, pid) in enumerate(zip(passages, passage_ids), 1):
                # Check for relevant keywords
                relevant_keywords = [kw for kw in financial_keywords if kw in passage]
                
                if relevant_keywords:
                    found_relevant = True
                    print(f"   ✅ RELEVANT #{j}: Found keywords: {relevant_keywords[:3]}")
                    print(f"      ID: {pid}")
                    print(f"      Content: {passage[:200]}...")
                else:
                    print(f"   #{j}: {passage[:100]}...")
            
            if found_relevant:
                print(f"   🎉 SUCCESS: Found relevant financial content!")
            else:
                print(f"   ⚠️  WARNING: No relevant financial content found")
                
        except Exception as e:
            print(f"   ❌ Query failed: {e}")
    
    # Test direct entity matching
    print(f"\n🧪 TESTING DIRECT ENTITY MATCHING:")
    print("="*60)
    
    try:
        # Test the query2node method directly
        query = "Risk Reversal strategy FX option"
        print(f"🎯 Direct entity test: '{query}'")
        
        node_scores = retriever.query2node(query, topN=10)
        
        print(f"📊 Found {len(node_scores)} matching entities:")
        
        # Sort by score
        sorted_entities = sorted(node_scores.items(), key=lambda x: x[1], reverse=True)
        
        financial_entities_found = 0
        for i, (entity, score) in enumerate(sorted_entities[:10], 1):
            # Check if it's a financial entity
            is_financial = any(kw in entity.lower() for kw in [
                'risk reversal', 'mmc', 'market maker', 'fx', 'option', 
                'trading', 'sef', 'zero cost', 'strategy'
            ])
            
            marker = "🎯" if is_financial else "  "
            print(f"{marker} #{i}: {entity[:50]}... (score: {score:.4f})")
            
            if is_financial:
                financial_entities_found += 1
        
        print(f"\n📊 Entity Analysis:")
        print(f"   • Total entities found: {len(node_scores)}")
        print(f"   • Financial entities: {financial_entities_found}")
        
        if financial_entities_found > 0:
            print(f"   ✅ SUCCESS: Direct entity matching working!")
        else:
            print(f"   ❌ WARNING: No financial entities matched")
            
    except Exception as e:
        print(f"   ❌ Direct entity test failed: {e}")
    
    print(f"\n" + "="*60)
    print("🎯 ENTITY-BASED RETRIEVAL TEST SUMMARY:")
    print("✅ HippoRAG2 switched to query2node mode")
    print("✅ Entity-based retrieval is now active")
    print("🔍 Test results show entity matching capability")
    print("🚀 Ready for comprehensive validation!")

if __name__ == "__main__":
    main()