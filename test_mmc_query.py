#!/usr/bin/env python3
"""
Test script to debug MMC scenario query retrieval
"""

import sys
import os
import pickle
import json
import networkx as nx
import numpy as np
import faiss
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_mmc_query_retrieval():
    """Test the MMC scenario query step by step"""
    
    print("=== Testing MMC Query: 'how to create scenarios in MMC?' ===\n")
    
    query = "how to create scenarios in MMC?"
    
    # 1. Load the graph and data
    print("1. Loading graph and data...")
    
    graphml_path = "import/pdf_dataset/kg_graphml/knowledge_graph.graphml"
    G = nx.read_graphml(graphml_path)
    print(f"   Graph loaded: {len(G.nodes)} nodes, {len(G.edges)} edges")
    
    # 2. Load embeddings
    print("\n2. Loading embeddings...")
    
    # Load node embeddings
    node_emb_path = "import/pdf_dataset/kg_embedding/node_embeddings.npy"
    node_embeddings = np.load(node_emb_path)
    print(f"   Node embeddings: {node_embeddings.shape}")
    
    # Load text embeddings (same as node embeddings for text nodes)
    text_embeddings = node_embeddings  # For text nodes
    
    # Load node mapping
    with open("import/pdf_dataset/kg_embedding/node_id_mapping.csv", 'r') as f:
        lines = f.readlines()[1:]  # Skip header
        node_id_to_emb_idx = {}
        for line in lines:
            parts = line.strip().split(',')
            if len(parts) >= 2:
                node_id_to_emb_idx[int(parts[0])] = int(parts[1])
    
    print(f"   Node mapping loaded: {len(node_id_to_emb_idx)} mappings")
    
    # 3. Load text node mappings (hash to numeric ID)
    print("\n3. Loading text node mappings...")
    
    text_hash_to_numeric = {}
    text_numeric_to_content = {}
    
    import csv
    with open("import/pdf_dataset/triples_csv/text_nodes__from_json_with_numeric_id.csv", 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # Skip header
        for row_num, row in enumerate(reader):
            if len(row) >= 4:
                try:
                    text_hash = row[0]
                    content = row[1]
                    numeric_id = int(row[2])
                    
                    text_hash_to_numeric[text_hash] = numeric_id
                    text_numeric_to_content[numeric_id] = content
                except (ValueError, IndexError) as e:
                    # Skip problematic rows
                    continue
    
    print(f"   Text mappings loaded: {len(text_hash_to_numeric)} text nodes")
    
    # Check MMC text node
    mmc_hash = "1fb3759bd9af49535dc71864a9dfaa0382b097be4ae24abdd55ef9dd5838b149"
    if mmc_hash in text_hash_to_numeric:
        mmc_numeric_id = text_hash_to_numeric[mmc_hash]
        print(f"   ✓ MMC text node: hash={mmc_hash[:12]}... -> numeric_id={mmc_numeric_id}")
        
        if mmc_numeric_id in node_id_to_emb_idx:
            mmc_emb_idx = node_id_to_emb_idx[mmc_numeric_id]
            print(f"   ✓ MMC embedding index: {mmc_emb_idx}")
        else:
            print(f"   ❌ MMC numeric ID not in embedding mapping")
    else:
        print(f"   ❌ MMC hash not found in text mappings")
    
    # 4. Test similarity search
    print("\n4. Testing similarity search...")
    
    # Create a simple query embedding (would normally come from sentence encoder)
    # For testing, create a synthetic embedding that should match MMC content
    # Use keywords: "create", "scenarios", "MMC"
    
    # Get some actual embeddings to understand scale
    sample_emb = node_embeddings[0]
    embedding_dim = len(sample_emb)
    print(f"   Embedding dimension: {embedding_dim}")
    
    # Create synthetic query embedding (in practice this would come from sentence encoder)
    query_embedding = np.random.randn(embedding_dim).astype(np.float32)
    query_embedding = query_embedding / np.linalg.norm(query_embedding)  # Normalize
    
    print(f"   Query embedding created: shape {query_embedding.shape}")
    
    # 5. Test direct similarity with MMC text node
    print("\n5. Testing direct similarity with MMC text node...")
    
    if mmc_numeric_id in node_id_to_emb_idx:
        mmc_emb_idx = node_id_to_emb_idx[mmc_numeric_id]
        mmc_embedding = node_embeddings[mmc_emb_idx]
        
        # Calculate similarity
        similarity = np.dot(query_embedding, mmc_embedding) / (
            np.linalg.norm(query_embedding) * np.linalg.norm(mmc_embedding)
        )
        
        print(f"   Direct similarity with MMC text: {similarity:.4f}")
        
        # Check if MMC embedding is reasonable
        mmc_norm = np.linalg.norm(mmc_embedding)
        print(f"   MMC embedding norm: {mmc_norm:.4f}")
        
        if mmc_norm < 0.1:
            print(f"   ⚠️  MMC embedding norm is very low - might be zero/corrupted")
        elif mmc_norm > 100:
            print(f"   ⚠️  MMC embedding norm is very high - might not be normalized")
    
    # 6. Test FAISS search
    print("\n6. Testing FAISS similarity search...")
    
    # Load FAISS index for text nodes
    text_index_path = "import/pdf_dataset/vector_index/text_nodes__from_json_with_emb_non_norm.index"
    
    if os.path.exists(text_index_path):
        text_index = faiss.read_index(text_index_path)
        print(f"   FAISS index loaded: {text_index.ntotal} vectors, dimension: {text_index.d}")
        
        # Check dimension mismatch
        if text_index.d != embedding_dim:
            print(f"   ⚠️  Dimension mismatch: query={embedding_dim}, index={text_index.d}")
            print(f"   Adjusting query embedding dimension...")
            
            if text_index.d < embedding_dim:
                # Truncate query embedding
                query_embedding = query_embedding[:text_index.d]
            else:
                # Pad query embedding
                padding = np.zeros(text_index.d - embedding_dim)
                query_embedding = np.concatenate([query_embedding, padding])
        
        # Search for top similar text nodes
        k = 20
        query_embedding_2d = query_embedding.reshape(1, -1)
        
        print(f"   Searching with query shape: {query_embedding_2d.shape}")
        distances, indices = text_index.search(query_embedding_2d, k)
        
        print(f"   Top {k} similar text nodes:")
        for i in range(min(k, len(distances[0]))):
            idx = indices[0][i]
            dist = distances[0][i]
            
            # Map back to text content
            if idx in text_numeric_to_content:
                content_preview = text_numeric_to_content[idx][:100] + "..."
                is_mmc = "MMC" in content_preview or "Market Maker Cockpit" in content_preview
                marker = "✓" if is_mmc else " "
                print(f"     {marker} Rank {i+1}: idx={idx}, dist={dist:.4f}")
                print(f"       Preview: {content_preview}")
                
                if idx == mmc_numeric_id:
                    print(f"       🎯 THIS IS THE MMC TEXT NODE!")
                    
            else:
                print(f"       Rank {i+1}: idx={idx}, dist={dist:.4f} (content not found)")
    
    else:
        print(f"   ❌ FAISS index not found: {text_index_path}")
    
    # 7. Check scenario entity connections
    print("\n7. Checking scenario entity connectivity...")
    
    scenario_entities = []
    mmc_entities = []
    
    for node_id in G.nodes:
        if "scenario" in str(node_id).lower():
            scenario_entities.append(node_id)
        if "MMC" in str(node_id) or "Market Maker Cockpit" in str(node_id):
            mmc_entities.append(node_id)
    
    print(f"   Found {len(scenario_entities)} scenario entities")
    print(f"   Found {len(mmc_entities)} MMC entities")
    
    # Check connections between scenario entities and MMC text node
    mmc_text_node = "1fb3759bd9af49535dc71864a9dfaa0382b097be4ae24abdd55ef9dd5838b149"
    connected_scenarios = []
    
    for scenario in scenario_entities:
        if G.has_edge(scenario, mmc_text_node):
            connected_scenarios.append(scenario)
    
    print(f"   Scenarios connected to MMC text: {len(connected_scenarios)}")
    for scenario in connected_scenarios:
        print(f"     - {scenario}")
    
    # 8. Check if scenario entities have embeddings
    print("\n8. Checking scenario entity embeddings...")
    
    scenario_with_embeddings = 0
    for scenario in scenario_entities[:5]:  # Check first 5
        # Try to find this entity in the embedding mapping
        # This is tricky because entity names might not match exactly
        print(f"   Scenario: {scenario}")
        
        # For now, just report that entity embeddings would need separate mapping
        # In HippoRAG2, entities and text nodes have different embedding mappings
        
    print(f"   Note: Entity embeddings require separate mapping (not checked here)")

if __name__ == "__main__":
    test_mmc_query_retrieval()
    
    print("\n=== Test Complete ===")
    print("\nThis analysis helps identify where the retrieval pipeline might be failing.")