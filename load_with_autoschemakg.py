#!/usr/bin/env python3
"""
Load data using AutoSchemaKG's create_embeddings_and_index() function.
This ensures proper data structure for HippoRAG2 retrieval.
"""

import os
import sys
from pathlib import Path
from sentence_transformers import SentenceTransformer
from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
from atlas_rag.vectorstore.create_graph_index import create_embeddings_and_index
from atlas_rag.retriever import HippoRAG2Retriever
from atlas_rag.llm_generator import LLMGenerator
from openai import OpenAI
from ollama_model_manager import OllamaModelManager

# Configuration - Change this to use a different Ollama model
DEFAULT_OLLAMA_MODEL = "qwen3:30b-a3b-thinking-2507-q4_K_M"  # Edit this to use your preferred model

def load_data_with_autoschemakg(dataset_dir: str = "import/360t_guide_direct_api_v2"):
    """Load data using AutoSchemaKG's proven method."""
    
    print("🚀 Loading Data with AutoSchemaKG's create_embeddings_and_index()")
    print("=" * 70)
    
    # Initialize embedding model (using all-mpnet-base-v2 for consistency)
    print("🔤 Initializing sentence encoder (all-mpnet-base-v2)...")
    encoder_model_name = "sentence-transformers/all-mpnet-base-v2"
    sentence_model = SentenceTransformer(encoder_model_name)
    sentence_encoder = SentenceEmbedding(sentence_model)
    print("✅ Sentence encoder initialized")
    
    # Set parameters for data loading
    keyword = ""  # Empty for the filename pattern
    working_directory = dataset_dir
    
    print(f"\n📁 Loading data from: {working_directory}")
    print("  Including: entities, events, concepts, and passages")
    
    try:
        # Use AutoSchemaKG's function to load data properly
        data = create_embeddings_and_index(
            sentence_encoder=sentence_encoder,
            model_name=encoder_model_name,
            working_directory=working_directory,
            keyword=keyword,
            include_concept=True,
            include_events=True,
            normalize_embeddings=True,
            text_batch_size=64,
            node_and_edge_batch_size=256
        )
        
        print("\n✅ Data loaded successfully!")
        
        # Display data structure information
        print("\n📊 Data Structure:")
        print(f"  - Knowledge Graph nodes: {len(data['KG'].nodes)}")
        print(f"  - Node list (entities/events/concepts): {len(data['node_list'])}")
        print(f"  - Edge list: {len(data['edge_list'])}")
        print(f"  - Text passages: {len(data['text_dict'])}")
        
        # Check text_dict content
        if data['text_dict']:
            # Show sample of text content
            print("\n📝 Sample text passages:")
            for i, (node_id, text) in enumerate(list(data['text_dict'].items())[:3]):
                preview = text[:200] + "..." if len(text) > 200 else text
                print(f"  {i+1}. Node {node_id[:8]}...: {preview}")
        
        # Check for passage nodes in graph
        passage_nodes = [n for n in data['KG'].nodes if data['KG'].nodes[n].get('type') == 'passage']
        print(f"\n📄 Passage nodes in graph: {len(passage_nodes)}")
        
        return data, sentence_encoder
        
    except FileNotFoundError as e:
        print(f"\n❌ Error: {str(e)}")
        print("💡 Make sure the GraphML file exists at:")
        print(f"   {working_directory}/kg_graphml/_graph.graphml")
        return None, None
    except Exception as e:
        print(f"\n❌ Error loading data: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None

def test_retrieval_with_loaded_data(data, sentence_encoder):
    """Test retrieval with properly loaded data."""
    
    print("\n🧪 Testing Retrieval with Loaded Data")
    print("=" * 70)
    
    # Initialize a simple LLM for testing (can be replaced with Ollama later)
    print("🤖 Setting up LLM (using OpenAI for testing)...")
    
    # For testing, we'll use a mock or simple client
    # In production, replace with Ollama client
    client = OpenAI(
        api_key="EMPTY",  # Mock for testing
        base_url="http://localhost:11434/v1"  # Ollama endpoint
    )
    
    llm_generator = LLMGenerator(
        client=client,
        model_name=DEFAULT_OLLAMA_MODEL
    )
    
    # Initialize HippoRAG2Retriever with loaded data
    print("🔍 Initializing HippoRAG2Retriever...")
    retriever = HippoRAG2Retriever(
        llm_generator=llm_generator,
        sentence_encoder=sentence_encoder,
        data=data
    )
    
    # Test queries
    test_queries = [
        "What is an OCO order?",
        "How to place risk reversal orders?",
        "What are the Market Maker Cockpit features?",
        "Explain the FIX protocol rules of engagement"
    ]
    
    print("\n🔎 Running test queries...")
    for query in test_queries:
        print(f"\n❓ Query: {query}")
        try:
            # Retrieve with topN=3
            passages, node_ids, scores = retriever.retrieve(query, topN=3)
            
            print(f"  📊 Retrieved {len(passages)} passages")
            
            # Show first result
            if passages:
                first_passage = passages[0]
                preview = first_passage[:300] + "..." if len(first_passage) > 300 else first_passage
                print(f"  📄 Top result (score {scores[0]:.4f}):")
                print(f"     {preview}")
                
                # Check if it's actual text or just entity name
                if len(first_passage) < 50 and not any(c in first_passage for c in ['.', '!', '?']):
                    print("  ⚠️  Warning: Result looks like entity name, not passage text")
                else:
                    print("  ✅ Result contains actual passage text")
                    
        except Exception as e:
            print(f"  ❌ Error during retrieval: {str(e)}")

def main():
    """Main function to load data and test retrieval."""
    
    print("🎯 AutoSchemaKG Data Loading and Testing")
    print("=" * 70)
    print("This script uses AutoSchemaKG's proven create_embeddings_and_index()")
    print("to properly load data with passage nodes for HippoRAG2 retrieval.")
    print()
    
    # Load data using AutoSchemaKG method
    data, sentence_encoder = load_data_with_autoschemakg("import/360t_guide_direct_api_v2")
    
    if data and sentence_encoder:
        # Test retrieval
        test_retrieval_with_loaded_data(data, sentence_encoder)
        
        print("\n🎉 Data loading complete!")
        print("📝 Next steps:")
        print("  1. Integrate with Ollama for answer generation")
        print("  2. Use this data structure in production retrieval")
    else:
        print("\n❌ Data loading failed!")
        print("💡 Check that GraphML file exists and has correct structure")

if __name__ == "__main__":
    main()