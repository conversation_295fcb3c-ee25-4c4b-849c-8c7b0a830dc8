#!/usr/bin/env python3
"""
Debug why edge-based retrieval isn't finding Risk Reversal content.
"""

import os
import sys
import pickle
import numpy as np
from pathlib import Path

# Set environment and add path
os.environ["TOKENIZERS_PARALLELISM"] = "false"
sys.path.insert(0, str(Path(__file__).parent))

from setup_embedding_model import setup_embedding_model

def main():
    print("🔍 DEBUG: Edge-based Risk Reversal Retrieval")
    print("="*50)
    
    # Setup embedding model
    print("🔧 Setting up embedding model...")
    embedding_model = setup_embedding_model()
    
    # Load data
    print("📊 Loading complete data...")
    data_file = Path("import/pdf_dataset/complete_data.pkl")
    with open(data_file, 'rb') as f:
        complete_data = pickle.load(f)
    
    # Get components
    kg = complete_data.get('KG')
    edge_list = complete_data.get('edge_list', [])
    edge_embeddings = complete_data.get('edge_embeddings', [])
    text_dict = complete_data.get('text_dict', {})
    
    # Risk Reversal text IDs
    risk_reversal_ids = [
        "cdcd632ca440b3caf201d917d6a8ac2f109905b716b570b0eb557b9e753591ff",
        "4eb22e1fee9bad1777d1e0d7493e5ae03450eb9402a4a3f08e638051a9dc2b87"
    ]
    
    print(f"📊 Data Overview:")
    print(f"   • KG nodes: {len(kg.nodes)}")
    print(f"   • KG edges: {len(kg.edges)}")
    print(f"   • Edge list: {len(edge_list)}")
    print(f"   • Edge embeddings: {len(edge_embeddings)}")
    
    # Find edges connected to Risk Reversal text nodes
    print(f"\n🔍 Risk Reversal Edge Analysis:")
    
    risk_reversal_edges = []
    for i, text_id in enumerate(risk_reversal_ids, 1):
        print(f"\n--- Risk Reversal ID #{i} ---")
        print(f"ID: {text_id}")
        
        if text_id in kg.nodes:
            # Find edges connected to this text node
            connected_edges = []
            
            # Check both incoming and outgoing edges
            for edge in kg.edges(data=True):
                source, target, edge_data = edge
                if source == text_id or target == text_id:
                    connected_edges.append(edge)
            
            print(f"🔗 Connected edges: {len(connected_edges)}")
            
            # Display some edges
            for j, (source, target, edge_data) in enumerate(connected_edges[:5], 1):
                relation = edge_data.get('relation', 'unknown')
                print(f"   #{j}: {source[:16]}... --{relation}--> {target[:16]}...")
                
                # Check if this edge has embeddings
                edge_tuple = (source, target)
                if edge_tuple in edge_list:
                    edge_idx = edge_list.index(edge_tuple)
                    print(f"        Edge index: {edge_idx} (has embedding: {edge_idx < len(edge_embeddings)})")
            
            risk_reversal_edges.extend(connected_edges)
        else:
            print(f"❌ Not found in KG")
    
    print(f"\n📊 Total Risk Reversal edges: {len(risk_reversal_edges)}")
    
    # Test edge embedding similarity
    if len(edge_embeddings) > 0 and len(risk_reversal_edges) > 0:
        print(f"\n🎯 Testing Edge Embedding Similarity:")
        
        query = "Risk Reversal strategy FX option"
        query_embedding = embedding_model.encode([query])[0]
        
        print(f"Query: '{query}'")
        print(f"Query embedding shape: {query_embedding.shape}")
        
        # Check edge embeddings related to Risk Reversal
        risk_reversal_edge_similarities = []
        
        for source, target, edge_data in risk_reversal_edges[:10]:  # Check first 10
            edge_tuple = (source, target)
            if edge_tuple in edge_list:
                edge_idx = edge_list.index(edge_tuple)
                if edge_idx < len(edge_embeddings):
                    edge_emb = edge_embeddings[edge_idx]
                    similarity = np.dot(query_embedding, edge_emb) / (np.linalg.norm(query_embedding) * np.linalg.norm(edge_emb))
                    
                    relation = edge_data.get('relation', 'unknown')
                    risk_reversal_edge_similarities.append((similarity, relation, source[:16], target[:16]))
        
        # Sort by similarity
        risk_reversal_edge_similarities.sort(reverse=True)
        
        print(f"\n🔍 Top Risk Reversal edge similarities:")
        for i, (sim, rel, src, tgt) in enumerate(risk_reversal_edge_similarities[:5], 1):
            print(f"   #{i}: {sim:.4f} - {src}... --{rel}--> {tgt}...")
    
    # Test overall edge similarity search
    print(f"\n🎯 Overall Edge Similarity Search:")
    
    if len(edge_embeddings) > 0:
        query = "Risk Reversal strategy FX option"
        query_embedding = embedding_model.encode([query])[0]
        
        # Calculate similarities with all edges
        edge_sims = []
        for i, edge_emb in enumerate(edge_embeddings[:1000]):  # Test first 1000 for speed
            similarity = np.dot(query_embedding, edge_emb) / (np.linalg.norm(query_embedding) * np.linalg.norm(edge_emb))
            edge_sims.append((similarity, i))
        
        # Sort by similarity
        edge_sims.sort(reverse=True)
        
        print(f"🔍 Top overall edge similarities:")
        for i, (sim, edge_idx) in enumerate(edge_sims[:10], 1):
            if edge_idx < len(edge_list):
                source, target = edge_list[edge_idx]
                edge_data = kg.edges.get((source, target), {})
                relation = edge_data.get('relation', 'unknown')
                
                # Check if connected to Risk Reversal
                is_risk_reversal = source in risk_reversal_ids or target in risk_reversal_ids
                marker = "🎯" if is_risk_reversal else "  "
                
                print(f"{marker} #{i}: {sim:.4f} - {source[:16]}... --{relation}--> {target[:16]}...")
                
                if is_risk_reversal:
                    print(f"      ✅ CONNECTED TO RISK REVERSAL!")
    
    print(f"\n{'='*50}")
    print("🎯 ANALYSIS SUMMARY:")
    print("• Risk Reversal nodes exist in KG ✅")
    print("• Need to check if edges have good embeddings")  
    print("• HippoRAG2 finds edges first, then does PageRank")
    print("• If edge embeddings don't match well, PageRank won't find Risk Reversal")

if __name__ == "__main__":
    main()