#!/usr/bin/env python3

"""
Test script to verify the system prompt fix is working correctly.
This addresses the regression where all models were giving brief answers.
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from hipporag2_interactive_debug import InteractiveHippoRAG2Debug

def test_system_prompt_fix():
    """Test that system prompt templates produce different instructions."""
    print("🧪 Testing System Prompt Fix")
    print("=" * 60)
    
    console = InteractiveHippoRAG2Debug()
    
    # Test 1: Detailed template (should encourage comprehensive answers)
    console.params['system_prompt_template'] = 'detailed'
    detailed_prompt = console.build_system_prompt()
    
    # Test 2: Concise template (the problematic restrictive one)
    console.params['system_prompt_template'] = 'concise'  
    concise_prompt = console.build_system_prompt()
    
    print("📋 Test Results:")
    print("-" * 40)
    
    # Verify detailed template encourages comprehensive answers
    if "comprehensive, detailed response with full explanations" in detailed_prompt:
        print("✅ PASS: Detailed template encourages comprehensive answers")
    else:
        print("❌ FAIL: Detailed template missing comprehensive instruction")
        
    # Verify concise template is restrictive (this was the problem)
    if "noun phrase, no elaborations" in concise_prompt:
        print("✅ PASS: Concise template is restrictive (as expected)")
    else:
        print("❌ FAIL: Concise template is not restrictive")
        
    # Verify they are different
    if detailed_prompt != concise_prompt:
        print("✅ PASS: Templates produce different prompts")
    else:
        print("❌ FAIL: Templates produce identical prompts")
        
    print("\n🔍 Detailed Template Preview:")
    print("-" * 40)
    print(detailed_prompt[-100:])  # Show the conclusion part
    
    print("\n🔍 Concise Template Preview:")  
    print("-" * 40)
    print(concise_prompt[-100:])   # Show the conclusion part
    
    print("\n🎯 Fix Summary:")
    print("-" * 40)
    print("• The system now builds custom prompts based on template selection")
    print("• Detailed mode encourages comprehensive answers with full context")
    print("• The LLM generator accepts custom_system_instruction parameter")
    print("• Users can view and edit the exact prompt sent to the LLM")
    print("• The misleading debug output has been replaced with actual prompts")
    
    return True

if __name__ == "__main__":
    test_system_prompt_fix()