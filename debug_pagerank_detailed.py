#!/usr/bin/env python3
"""
Advanced PageRank Propagation Debugging for HippoRAG2

This script provides comprehensive step-by-step tracing of the HippoRAG2 retrieval process
to identify exactly where PageRank propagation fails and why high-scoring entities/events
don't reach the final text results.

Key debugging capabilities:
1. Detailed query embedding analysis
2. Edge matching and filtering tracing
3. Personalization dictionary construction monitoring
4. NetworkX PageRank execution tracking
5. Final result filtering and ranking analysis
6. Event vs Entity connectivity analysis
"""

import numpy as np
import networkx as nx
import json
from datetime import datetime
from collections import Counter, defaultdict
from sklearn.metrics.pairwise import cosine_similarity
import warnings

from hipporag2_pipeline import HippoRAG2Pipeline

class AdvancedPageRankDebugger:
    """
    Advanced debugging tool for HippoRAG2 PageRank propagation analysis.
    """
    
    def __init__(self, data_directory="import/pdf_dataset"):
        self.data_directory = data_directory
        self.pipeline = None
        self.debug_log = []
        self.query_results = {}
        
    def log(self, message, level="INFO"):
        """Add timestamped log entry."""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {level}: {message}"
        self.debug_log.append(log_entry)
        print(log_entry)
    
    def initialize_pipeline(self):
        """Initialize HippoRAG2 pipeline with debugging capabilities."""
        self.log("🚀 Initializing HippoRAG2 Pipeline for detailed debugging")
        
        self.pipeline = HippoRAG2Pipeline(data_directory=self.data_directory)
        data = self.pipeline.load_existing_data()
        self.pipeline.setup_models()
        self.pipeline.data = data
        self.pipeline.initialize_hipporag2()
        
        self.log(f"✅ Pipeline initialized successfully")
        self.log(f"   Graph: {len(data['KG'].nodes):,} nodes, {len(data['KG'].edges):,} edges")
        self.log(f"   Edge embeddings: {data['edge_embeddings'].shape}")
        self.log(f"   Node embeddings: {data['node_embeddings'].shape}")
        self.log(f"   Text embeddings: {data['text_embeddings'].shape}")
        
        return data
    
    def analyze_query_embedding(self, query):
        """Analyze query embedding and similar terms."""
        self.log(f"\n🔍 STEP 1: QUERY EMBEDDING ANALYSIS")
        self.log(f"Query: '{query}'")
        
        # Get query embedding
        query_emb = self.pipeline.sentence_encoder.encode([query], query_type="edge")
        self.log(f"Query embedding shape: {query_emb.shape}")
        
        # Find most similar embeddings in edge embeddings
        edge_similarities = cosine_similarity(query_emb, self.pipeline.data["edge_embeddings"])[0]
        top_edge_indices = np.argsort(edge_similarities)[::-1][:10]
        
        self.log(f"Top 10 most similar edges:")
        for i, idx in enumerate(top_edge_indices):
            if idx < len(self.pipeline.data["edge_list"]):
                edge = self.pipeline.data["edge_list"][idx]
                similarity = edge_similarities[idx]
                
                # Get edge description
                try:
                    source_name = self.pipeline.data["KG"].nodes[edge[0]].get('name', edge[0])
                    target_name = self.pipeline.data["KG"].nodes[edge[1]].get('name', edge[1])
                    relation = self.pipeline.data["KG"].edges[edge].get('relation', 'connected_to')
                    edge_desc = f"{source_name} {relation} {target_name}"
                    
                    self.log(f"   {i+1:2d}. Sim: {similarity:.4f} | {edge_desc[:100]}")
                except Exception as e:
                    self.log(f"   {i+1:2d}. Sim: {similarity:.4f} | Edge {edge} (error: {e})")
        
        return query_emb
    
    def trace_edge_matching(self, query):
        """Trace the edge matching process in detail."""
        self.log(f"\n🔗 STEP 2: EDGE MATCHING & FILTERING TRACE")
        
        # Get the retriever for direct access
        retriever = self.pipeline.hipporag2_retriever
        
        # Step 2a: Initial edge similarity computation
        query_emb = self.pipeline.sentence_encoder.encode([query], query_type="edge")
        
        with warnings.catch_warnings():
            warnings.filterwarnings('ignore', category=RuntimeWarning)
            raw_scores = retriever.edge_embeddings @ query_emb[0].T
        
        scores = retriever.min_max_normalize(raw_scores) if hasattr(retriever, 'min_max_normalize') else raw_scores
        topN = retriever.inference_config.topk_edges
        
        self.log(f"Initial edge similarity computation:")
        self.log(f"   Raw scores range: {np.min(raw_scores):.4f} to {np.max(raw_scores):.4f}")
        self.log(f"   Normalized scores range: {np.min(scores):.4f} to {np.max(scores):.4f}")
        self.log(f"   Selecting top {topN} edges")
        
        # Step 2b: Top edge selection
        index_matrix = np.argsort(scores)[-topN:][::-1]
        
        self.log(f"Top {topN} edges before LLM filtering:")
        candidate_edges = []
        
        for i, idx in enumerate(index_matrix):
            if idx < len(retriever.edge_list):
                edge = retriever.edge_list[idx]
                similarity = scores[idx]
                
                try:
                    source_id = retriever.KG.nodes[edge[0]]['id']
                    target_id = retriever.KG.nodes[edge[1]]['id']
                    relation = retriever.KG.edges[edge]['relation']
                    edge_str = [source_id, relation, target_id]
                    
                    candidate_edges.append(edge_str)
                    self.log(f"   {i+1:2d}. Score: {similarity:.4f} | {source_id} {relation} {target_id}")
                    
                except Exception as e:
                    self.log(f"   {i+1:2d}. Score: {similarity:.4f} | Edge {edge} (error: {e})")
        
        # Step 2c: LLM filtering
        self.log(f"\nLLM filtering process:")
        
        before_filter_edge_json = {'fact': candidate_edges}
        self.log(f"   Sending {len(candidate_edges)} edges to LLM for filtering")
        
        try:
            filtered_facts_str = retriever.llm_generator.filter_triples_with_entity_event(
                query, json.dumps(before_filter_edge_json, ensure_ascii=False)
            )
            
            # Parse filtered results
            import json_repair
            filtered_facts = json_repair.loads(filtered_facts_str)['fact']
            
            self.log(f"   LLM returned {len(filtered_facts)} filtered edges:")
            for i, edge in enumerate(filtered_facts[:5]):  # Show first 5
                self.log(f"      {i+1}. {edge[0]} {edge[1]} {edge[2]}")
            
            if len(filtered_facts) > 5:
                self.log(f"      ... and {len(filtered_facts) - 5} more")
                
        except Exception as e:
            self.log(f"   ❌ LLM filtering failed: {e}")
            filtered_facts = []
        
        return candidate_edges, filtered_facts
    
    def trace_personalization_dict(self, query, filtered_facts):
        """Trace the construction of the personalization dictionary."""
        self.log(f"\n📊 STEP 3: PERSONALIZATION DICTIONARY CONSTRUCTION")
        
        retriever = self.pipeline.hipporag2_retriever
        
        # Step 3a: Node score extraction from filtered edges
        node_score_dict = {}
        
        if len(filtered_facts) == 0:
            self.log("   ⚠️  No filtered facts - falling back to text similarity only")
            text_dict = retriever.query2passage(query, weight_adjust=retriever.inference_config.weight_adjust)
            return {}, text_dict
        
        self.log(f"Extracting node scores from {len(filtered_facts)} filtered edges:")
        
        for i, edge in enumerate(filtered_facts):
            try:
                # Find the edge in the FAISS index
                edge_str = f'{edge[0]} {edge[1]} {edge[2]}'
                search_emb = retriever.sentence_encoder.encode([edge_str], query_type="search")
                D, I = retriever.edge_faiss_index.search(search_emb, 1)
                filtered_index = I[0][0]
                
                # Get the actual edge
                actual_edge = retriever.edge_list[filtered_index]
                head, tail = actual_edge[0], actual_edge[1]
                
                # Get similarity score from original computation
                with warnings.catch_warnings():
                    warnings.filterwarnings('ignore', category=RuntimeWarning)
                    query_emb = retriever.sentence_encoder.encode([query], query_type="edge")
                    raw_scores = retriever.edge_embeddings @ query_emb[0].T
                    scores = retriever.min_max_normalize(raw_scores) if hasattr(retriever, 'min_max_normalize') else raw_scores
                
                sim_score = scores[filtered_index]
                
                # Add scores for head and tail nodes
                for node in [head, tail]:
                    if node not in node_score_dict:
                        node_score_dict[node] = [sim_score]
                    else:
                        node_score_dict[node].append(sim_score)
                
                # Get node types for logging
                head_type = retriever.KG.nodes[head].get('type', 'unknown')
                tail_type = retriever.KG.nodes[tail].get('type', 'unknown')
                head_name = retriever.KG.nodes[head].get('name', head)[:50]
                tail_name = retriever.KG.nodes[tail].get('name', tail)[:50]
                
                self.log(f"   {i+1}. Score: {sim_score:.4f}")
                self.log(f"      Head: {head_type} '{head_name}'")
                self.log(f"      Tail: {tail_type} '{tail_name}'")
                
            except Exception as e:
                self.log(f"   ❌ Error processing edge {i+1}: {e}")
        
        # Step 3b: Average scores for nodes
        for node in node_score_dict:
            node_score_dict[node] = sum(node_score_dict[node]) / len(node_score_dict[node])
        
        self.log(f"\nFinal node scores (top 10):")
        sorted_nodes = sorted(node_score_dict.items(), key=lambda x: x[1], reverse=True)
        for i, (node, score) in enumerate(sorted_nodes[:10]):
            node_type = retriever.KG.nodes[node].get('type', 'unknown')
            node_name = retriever.KG.nodes[node].get('name', node)[:50]
            self.log(f"   {i+1:2d}. {score:.4f} | {node_type} '{node_name}'")
        
        # Step 3c: Text similarity scores
        text_dict = retriever.query2passage(query, weight_adjust=retriever.inference_config.weight_adjust)
        
        self.log(f"\nText similarity scores (top 5):")
        sorted_text = sorted(text_dict.items(), key=lambda x: x[1], reverse=True)
        for i, (text_id, score) in enumerate(sorted_text[:5]):
            text_preview = retriever.passage_dict.get(text_id, "No text")[:100]
            self.log(f"   {i+1}. {score:.4f} | {text_preview}...")
        
        return node_score_dict, text_dict
    
    def trace_pagerank_execution(self, node_dict, text_dict):
        """Trace the NetworkX PageRank execution in detail."""
        self.log(f"\n🧮 STEP 4: NETWORKX PAGERANK EXECUTION")
        
        retriever = self.pipeline.hipporag2_retriever
        
        # Step 4a: Construct personalization dictionary
        personalization_dict = {}
        personalization_dict.update(node_dict)
        personalization_dict.update(text_dict)
        
        self.log(f"Personalization dictionary stats:")
        self.log(f"   Total personalized nodes: {len(personalization_dict)}")
        self.log(f"   Node contributions: {len(node_dict)}")
        self.log(f"   Text contributions: {len(text_dict)}")
        
        # Analyze personalization by node type
        type_scores = defaultdict(list)
        for node_id, score in personalization_dict.items():
            node_type = retriever.KG.nodes[node_id].get('type', 'unknown')
            type_scores[node_type].append(score)
        
        self.log(f"   Personalization scores by node type:")
        for node_type, scores in type_scores.items():
            avg_score = np.mean(scores)
            max_score = np.max(scores)
            count = len(scores)
            self.log(f"      {node_type}: {count} nodes, avg={avg_score:.4f}, max={max_score:.4f}")
        
        # Step 4b: Run PageRank
        self.log(f"\nRunning PageRank with parameters:")
        self.log(f"   alpha (damping): {retriever.inference_config.ppr_alpha}")
        self.log(f"   max_iter: {retriever.inference_config.ppr_max_iter}")
        self.log(f"   tolerance: {retriever.inference_config.ppr_tol}")
        
        try:
            pr = nx.pagerank(
                retriever.KG, 
                personalization=personalization_dict,
                alpha=retriever.inference_config.ppr_alpha,
                max_iter=retriever.inference_config.ppr_max_iter,
                tol=retriever.inference_config.ppr_tol
            )
            
            self.log(f"✅ PageRank completed successfully")
            self.log(f"   Computed scores for {len(pr)} nodes")
            
        except Exception as e:
            self.log(f"❌ PageRank execution failed: {e}")
            return {}
        
        # Step 4c: Analyze PageRank results
        self.log(f"\nPageRank results analysis:")
        
        # Top overall scores
        sorted_pr = sorted(pr.items(), key=lambda x: x[1], reverse=True)
        self.log(f"   Top 10 PageRank scores (all nodes):")
        for i, (node, score) in enumerate(sorted_pr[:10]):
            node_type = retriever.KG.nodes[node].get('type', 'unknown')
            node_name = retriever.KG.nodes[node].get('name', node)[:50]
            self.log(f"      {i+1:2d}. {score:.6f} | {node_type} '{node_name}'")
        
        # Text node scores specifically
        text_scores = [(node, score) for node, score in pr.items() 
                      if node in retriever.text_id_list and score > 0.0]
        text_scores.sort(key=lambda x: x[1], reverse=True)
        
        self.log(f"   Top 10 text node PageRank scores:")
        for i, (node, score) in enumerate(text_scores[:10]):
            text_preview = retriever.passage_dict.get(node, "No text")[:100]
            self.log(f"      {i+1:2d}. {score:.6f} | {text_preview}...")
        
        return pr
    
    def trace_final_filtering(self, pr_scores, topN=5):
        """Trace the final result filtering and ranking."""
        self.log(f"\n🎯 STEP 5: FINAL RESULT FILTERING & RANKING")
        
        retriever = self.pipeline.hipporag2_retriever
        
        # Step 5a: Filter to text nodes only
        text_dict_score = {}
        for node in retriever.text_id_list:
            if pr_scores.get(node, 0.0) > 0.0:
                text_dict_score[node] = pr_scores[node]
        
        self.log(f"Text node filtering:")
        self.log(f"   Total text nodes in system: {len(retriever.text_id_list)}")
        self.log(f"   Text nodes with PageRank > 0: {len(text_dict_score)}")
        
        # Step 5b: Sort and select top N
        sorted_passages_ids = sorted(text_dict_score.items(), key=lambda x: x[1], reverse=True)
        final_results = sorted_passages_ids[:topN]
        
        self.log(f"\nFinal ranking (top {topN}):")
        for i, (passage_id, score) in enumerate(final_results):
            text_content = retriever.passage_dict.get(passage_id, "No content")
            text_preview = text_content[:150] + "..." if len(text_content) > 150 else text_content
            
            self.log(f"   {i+1}. Score: {score:.6f}")
            self.log(f"      Content: {text_preview}")
            
            # Analyze content type
            content_lower = text_content.lower()
            if any(term in content_lower for term in ['mmc', 'market maker cockpit', 'cockpit']):
                self.log(f"      🎯 MMC-relevant content!")
            elif any(term in content_lower for term in ['fix protocol', 'fix message']):
                self.log(f"      ⚠️  FIX protocol content")
            else:
                self.log(f"      ❓ Other content type")
        
        return final_results
    
    def analyze_connectivity_issues(self):
        """Analyze potential connectivity issues between entities/events and text."""
        self.log(f"\n🔍 CONNECTIVITY ANALYSIS")
        
        graph = self.pipeline.data["KG"]
        
        # Analyze node types
        node_types = Counter()
        for node in graph.nodes():
            node_type = graph.nodes[node].get('type', 'unknown')
            node_types[node_type] += 1
        
        self.log(f"Node type distribution:")
        for node_type, count in node_types.most_common():
            self.log(f"   {node_type}: {count:,}")
        
        # Analyze connectivity between different node types
        edge_types = Counter()
        entity_to_text = 0
        event_to_text = 0
        
        for edge in graph.edges():
            source, target = edge
            source_type = graph.nodes[source].get('type', 'unknown')
            target_type = graph.nodes[target].get('type', 'unknown')
            
            edge_type = f"{source_type} -> {target_type}"
            edge_types[edge_type] += 1
            
            if source_type == 'entity' and target_type in ['passage', 'text']:
                entity_to_text += 1
            elif source_type == 'event' and target_type in ['passage', 'text']:
                event_to_text += 1
        
        self.log(f"\nEdge type distribution (top 10):")
        for edge_type, count in edge_types.most_common(10):
            self.log(f"   {edge_type}: {count:,}")
        
        self.log(f"\nCritical connectivity:")
        self.log(f"   Entity → Text edges: {entity_to_text:,}")
        self.log(f"   Event → Text edges: {event_to_text:,}")
        
        if event_to_text == 0:
            self.log(f"   ⚠️  WARNING: No event-to-text edges found!")
            self.log(f"   This could be why events aren't reaching text results")
    
    def debug_full_query(self, query, topN=5):
        """Run complete debugging analysis for a query."""
        self.log(f"\n{'='*80}")
        self.log(f"🔬 COMPREHENSIVE PAGERANK DEBUG ANALYSIS")
        self.log(f"{'='*80}")
        self.log(f"Query: '{query}'")
        self.log(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Initialize pipeline
        data = self.initialize_pipeline()
        
        # Step 1: Query embedding analysis
        query_emb = self.analyze_query_embedding(query)
        
        # Step 2: Edge matching and filtering
        candidate_edges, filtered_facts = self.trace_edge_matching(query)
        
        # Step 3: Personalization dictionary construction
        node_dict, text_dict = self.trace_personalization_dict(query, filtered_facts)
        
        # Step 4: PageRank execution
        pr_scores = self.trace_pagerank_execution(node_dict, text_dict)
        
        # Step 5: Final filtering
        final_results = self.trace_final_filtering(pr_scores, topN)
        
        # Step 6: Connectivity analysis
        self.analyze_connectivity_issues()
        
        # Store results
        self.query_results[query] = {
            'candidate_edges': candidate_edges,
            'filtered_facts': filtered_facts,
            'node_dict': node_dict,
            'text_dict': text_dict,
            'pr_scores': pr_scores,
            'final_results': final_results
        }
        
        self.log(f"\n{'='*80}")
        self.log(f"🏁 DEBUG ANALYSIS COMPLETE")
        self.log(f"{'='*80}")
        
        return final_results
    
    def save_debug_log(self, filename=None):
        """Save the debug log to a file."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pagerank_debug_log_{timestamp}.txt"
        
        with open(filename, 'w') as f:
            f.write("\n".join(self.debug_log))
        
        print(f"📝 Debug log saved to: {filename}")
        return filename


def main():
    """Main debugging function."""
    debugger = AdvancedPageRankDebugger()
    
    # Test the problematic MMC query
    query = "how to create scenarios in MMC?"
    
    try:
        results = debugger.debug_full_query(query, topN=5)
        
        # Save debug log
        log_file = debugger.save_debug_log()
        
        print(f"\n🎯 DEBUGGING COMPLETE!")
        print(f"   Results found: {len(results)}")
        print(f"   Debug log: {log_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Debugging failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)