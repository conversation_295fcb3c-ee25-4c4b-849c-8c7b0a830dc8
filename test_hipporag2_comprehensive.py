#!/usr/bin/env python3
"""
Comprehensive HippoRAG2 Testing Suite

Tests the recovered HippoRAG2 pipeline with various query types and edge cases
to ensure the data consistency fixes are robust.
"""

import sys
import traceback
from hipporag2_pipeline import HippoRAG2Pipeline

def test_data_structure_consistency():
    """Test that data structures are consistent and indexing works."""
    print("🔍 Testing Data Structure Consistency...")
    
    try:
        pipeline = HippoRAG2Pipeline()
        pipeline.data = pipeline.load_existing_data()
        
        # Validate dimensions
        graph_nodes = len(pipeline.data['KG'].nodes)
        graph_edges = len(pipeline.data['KG'].edges)
        node_embeddings = pipeline.data['node_embeddings'].shape[0]
        edge_embeddings = pipeline.data['edge_embeddings'].shape[0] 
        text_embeddings = pipeline.data['text_embeddings'].shape[0]
        
        print(f"   Graph: {graph_nodes:,} nodes, {graph_edges:,} edges")
        print(f"   Embeddings: {node_embeddings:,} nodes, {edge_embeddings:,} edges, {text_embeddings:,} text")
        
        # Test indexing bounds
        node_list_len = len(pipeline.data.get('node_list', []))
        edge_list_len = len(pipeline.data.get('edge_list', []))
        
        print(f"   Lists: {node_list_len:,} nodes, {edge_list_len:,} edges")
        
        # Critical test: ensure edge_list length <= edge_embeddings length
        if edge_list_len > edge_embeddings:
            print(f"   ❌ CRITICAL: edge_list ({edge_list_len}) > edge_embeddings ({edge_embeddings})")
            return False
        else:
            print(f"   ✅ SAFE: edge_list ({edge_list_len}) <= edge_embeddings ({edge_embeddings})")
        
        # Test node indexing bounds
        if node_list_len > node_embeddings:
            print(f"   ⚠️  WARNING: node_list ({node_list_len}) > node_embeddings ({node_embeddings})")
        else:
            print(f"   ✅ SAFE: node_list ({node_list_len}) <= node_embeddings ({node_embeddings})")
        
        print("   ✅ Data structure consistency tests passed!")
        return True
        
    except Exception as e:
        print(f"   ❌ Data structure test failed: {e}")
        traceback.print_exc()
        return False

def test_query_types():
    """Test different types of queries to ensure robustness."""
    print("\\n🎯 Testing Various Query Types...")
    
    test_queries = [
        "What is FIX protocol?",
        "How to connect to 360T?", 
        "API version information",
        "Trading platform details",
        "Network connectivity options",
        "Testing and go-live process",
        "Message workflow and routing",
        "QuoteRequest specifications"
    ]
    
    try:
        pipeline = HippoRAG2Pipeline()
        pipeline.data = pipeline.load_existing_data()
        pipeline.setup_models()
        pipeline.initialize_hipporag2()
        
        success_count = 0
        
        for i, query in enumerate(test_queries, 1):
            print(f"   {i}. Testing: '{query}'")
            try:
                passages, passage_ids = pipeline.query_knowledge_graph(query, topN=2)
                if passages and len(passages) > 0:
                    print(f"      ✅ Retrieved {len(passages)} passages")
                    success_count += 1
                else:
                    print(f"      ⚠️  No passages retrieved")
            except Exception as e:
                print(f"      ❌ Query failed: {e}")
        
        print(f"\\n   📊 Query Results: {success_count}/{len(test_queries)} successful")
        
        if success_count >= len(test_queries) * 0.8:  # 80% success rate
            print("   ✅ Query testing passed!")
            return True
        else:
            print("   ❌ Query testing failed (< 80% success rate)")
            return False
            
    except Exception as e:
        print(f"   ❌ Query testing failed: {e}")
        traceback.print_exc()
        return False

def test_edge_case_scenarios():
    """Test edge cases that might cause indexing errors."""
    print("\\n⚡ Testing Edge Case Scenarios...")
    
    edge_cases = [
        ("Empty query", ""),
        ("Single word", "API"),
        ("Very long query", "This is a very long query that contains many words and should test the system's ability to handle extended input text without causing issues or errors in the retrieval process"),
        ("Special characters", "FIX 4.4 - what is @360T's API?"),
        ("Numbers only", "12345"),
        ("Non-English", "什么是FIX协议？")
    ]
    
    try:
        pipeline = HippoRAG2Pipeline()
        pipeline.data = pipeline.load_existing_data()
        pipeline.setup_models()
        pipeline.initialize_hipporag2()
        
        success_count = 0
        
        for i, (test_name, query) in enumerate(edge_cases, 1):
            print(f"   {i}. Testing {test_name}: '{query[:50]}{'...' if len(query) > 50 else ''}'")
            try:
                passages, passage_ids = pipeline.query_knowledge_graph(query, topN=1)
                print(f"      ✅ Handled successfully ({len(passages)} results)")
                success_count += 1
            except Exception as e:
                print(f"      ❌ Failed: {e}")
        
        print(f"\\n   📊 Edge Case Results: {success_count}/{len(edge_cases)} handled successfully")
        
        if success_count >= len(edge_cases) * 0.7:  # 70% success rate for edge cases
            print("   ✅ Edge case testing passed!")
            return True
        else:
            print("   ❌ Edge case testing failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Edge case testing failed: {e}")
        traceback.print_exc()
        return False

def test_performance_bounds():
    """Test performance under different topN values."""
    print("\\n⚡ Testing Performance Bounds...")
    
    test_query = "What are the main features of the FIX API?"
    topN_values = [1, 3, 5, 10, 20]
    
    try:
        pipeline = HippoRAG2Pipeline()
        pipeline.data = pipeline.load_existing_data()
        pipeline.setup_models()
        pipeline.initialize_hipporag2()
        
        success_count = 0
        
        for topN in topN_values:
            print(f"   Testing topN={topN}")
            try:
                import time
                start_time = time.time()
                passages, passage_ids = pipeline.query_knowledge_graph(test_query, topN=topN)
                end_time = time.time()
                
                if passages and len(passages) <= topN:
                    print(f"      ✅ Retrieved {len(passages)} passages in {end_time-start_time:.2f}s")
                    success_count += 1
                else:
                    print(f"      ⚠️  Retrieved {len(passages)} passages (expected ≤{topN})")
                    
            except Exception as e:
                print(f"      ❌ Failed with topN={topN}: {e}")
        
        print(f"\\n   📊 Performance Results: {success_count}/{len(topN_values)} successful")
        
        if success_count >= len(topN_values) * 0.8:
            print("   ✅ Performance testing passed!")
            return True
        else:
            print("   ❌ Performance testing failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Performance testing failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run comprehensive testing suite."""
    print("🧪 HippoRAG2 Comprehensive Testing Suite")
    print("=" * 50)
    
    tests = [
        ("Data Structure Consistency", test_data_structure_consistency),
        ("Query Types", test_query_types),
        ("Edge Case Scenarios", test_edge_case_scenarios), 
        ("Performance Bounds", test_performance_bounds)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\\n🔍 Running: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
            traceback.print_exc()
    
    print("\\n" + "=" * 50)
    print(f"📊 FINAL RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! HippoRAG2 pipeline is fully functional.")
        return True
    elif passed >= total * 0.75:
        print("✅ MOSTLY SUCCESSFUL! Minor issues detected but core functionality works.")
        return True
    else:
        print("❌ CRITICAL ISSUES DETECTED! Pipeline needs additional fixes.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)