{"timestamp": "2025-08-05T07:21:07.068872", "data_directory": "import/pdf_dataset", "findings": {"graph_structure": {"total_nodes": 63364, "total_edges": 160795, "node_types": {"entity": 35782, "event": 26375, "text": 1207}, "edge_types": {"entity -> entity": 37726, "entity -> text": 35782, "event -> event": 7834, "event -> entity": 53054, "event -> text": 26375, "entity -> event": 24}, "text_edges": 62157, "entity_text_edges": 62157}, "embedding_coverage": {"node_embeddings": {"file_exists": true, "shape": [64571, 384], "count": 64571, "dimensions": 384, "file_size_mb": 94.5865478515625}, "edge_embeddings": {"file_exists": true, "shape": [133033, 384], "count": 133033, "dimensions": 384, "file_size_mb": 194.8726806640625}, "text_embeddings": {"file_exists": true, "shape": [1207, 384], "count": 1207, "dimensions": 384, "file_size_mb": 1.7681884765625}}, "coverage_gaps": {"node_coverage_gap": {"missing_count": -1207, "missing_percentage": -1.9048671169749385}, "edge_coverage_gap": {"missing_count": 27762, "missing_percentage": 17.265462234522218}, "text_edge_analysis": {"total_text_edges": 62157, "entity_text_edges": 62157, "likely_missing_text_edges": 27762}}, "csv_source_analysis": {"triple_nodes": {"exists": false}, "triple_edges": {"exists": false}, "text_nodes": {"exists": false}, "text_edges": {"exists": false}}}, "root_causes": ["Incomplete embedding generation: 27,762 edges, -1,207 nodes missing"], "recommendations": ["Fix embedding generation pipeline to achieve 100% coverage", "Investigate memory/batch size constraints in embedding processing", "Implement proper error handling and retry mechanisms", "Add progress tracking and resumption for embedding generation", "Validate embedding completeness before using in retrieval", "Make HippoRAG2 robust to missing embeddings with fallback strategies"]}