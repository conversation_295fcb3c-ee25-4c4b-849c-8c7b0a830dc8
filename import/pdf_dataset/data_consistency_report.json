{"timestamp": "2025-08-04T12:34:35.070238", "data_directory": "import/pdf_dataset", "overall_status": "ERROR", "results": {"timestamps": {"timestamps": {"knowledge_graph.graphml": {"modified": "2025-08-04 12:07:00.720698", "created": "2025-08-04 12:07:00.720698", "size": 79708593}, "triple_nodes__from_json_without_emb.csv": {"modified": "2025-08-02 09:04:00.045386", "created": "2025-08-02 09:04:00.045386", "size": 3578165}, "triple_edges__from_json_without_emb.csv": {"modified": "2025-08-02 09:04:00.045192", "created": "2025-08-02 09:04:00.045192", "size": 11418571}, "triple_nodes__from_json_with_emb.npy": {"modified": "2025-08-03 17:12:53.898678", "created": "2025-08-03 17:12:53.898678", "size": 95473280}, "triple_edges__from_json_with_concept_with_emb.npy": {"modified": "2025-08-04 12:29:56.714289", "created": "2025-08-04 12:29:56.714289", "size": 171704960}}, "suspicious_gap": true, "time_span_hours": 51.432408082500004, "status": "WARNING"}, "dimensions": {"dimensions": {"csv_nodes": 62157, "csv_edges": 111787, "csv_text": 1207, "graphml_nodes": 63364, "graphml_edges": 160795, "node_embeddings": 62157, "node_embeddings_dim": 384, "edge_embeddings": 111787, "edge_embeddings_dim": 384, "text_embeddings": 1207, "text_embeddings_dim": 384}, "mismatches": ["GraphML nodes 63364 ≠ CSV nodes 62157"], "status": "ERROR"}, "integrity": {"integrity_results": {"node_embeddings": {"status": "OK", "shape": [62157, 384], "dtype": "float32", "has_nan": false, "has_inf": false, "all_zeros": false, "has_extreme_values": false, "mean": 0.0005885976715944707, "std": 0.05102761462330818}, "edge_embeddings": {"status": "OK", "shape": [111787, 384], "dtype": "float32", "has_nan": false, "has_inf": false, "all_zeros": false, "has_extreme_values": false, "mean": -1.0568673133093398e-05, "std": 0.05103102698922157}, "text_embeddings": {"status": "OK", "shape": [1207, 384], "dtype": "float32", "has_nan": false, "has_inf": false, "all_zeros": false, "has_extreme_values": false, "mean": -0.0023806297685950994, "std": 0.05097547918558121}}, "issues": [], "status": "OK"}, "faiss": {"index_results": {"triple_nodes__from_json_with_emb": {"status": "OK", "ntotal": 62157, "dimension": 384, "metric_type": 0}, "triple_edges__from_json_with_concept_with_emb": {"status": "OK", "ntotal": 111787, "dimension": 384, "metric_type": 0}, "text_nodes__from_json_with_emb": {"status": "OK", "ntotal": 1207, "dimension": 384, "metric_type": 0}}, "issues": [], "status": "OK"}}, "summary": {"total_checks": 4, "passed": 2, "warnings": 1, "errors": 1}}