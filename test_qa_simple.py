#!/usr/bin/env python3
"""
Simple test of Q&A functionality after the topN fix.
"""

import os
import sys

# Set environment variables to avoid segfaults
os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'

def test_qa():
    """Test a simple Q&A query."""
    
    print("=" * 70)
    print("🧪 Testing Q&A Functionality")
    print("=" * 70)
    
    try:
        from hipporag2_interactive_debug import InteractiveHippoRAG2Debug
        
        # Initialize console
        console = InteractiveHippoRAG2Debug()
        
        # Load data
        print("\n1. Loading data...")
        if not console.load_data():
            print("❌ Failed to load data")
            return False
        
        # Setup LLM
        print("\n2. Setting up LLM...")
        if not console.setup_llm():
            print("❌ Failed to setup LLM")
            return False
        
        # Initialize retriever
        print("\n3. Initializing retriever...")
        if not console.initialize_retriever():
            print("❌ Failed to initialize retriever")
            return False
        
        # Test a query
        print("\n4. Testing query...")
        test_query = "What is a resting order in Supersonic?"
        
        print(f"\nQuery: {test_query}")
        print("-" * 70)
        
        # This will test the full pipeline
        console.ask_question(test_query)
        
        print("\n" + "=" * 70)
        print("✅ Q&A test completed successfully!")
        print("=" * 70)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    
    print("🔧 Testing HippoRAG2 Q&A after topN Fix")
    print("=" * 70)
    
    success = test_qa()
    
    if success:
        print("\n✅ SUCCESS! The Q&A system is working correctly.")
        print("You can now run the interactive console:")
        print("  python hipporag2_interactive_debug.py")
    else:
        print("\n❌ FAILURE: Please check the error messages above")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())