#!/usr/bin/env python3
"""
Patched HippoRAG2 Retriever with Safe Matrix Operations

This is a patched version of the HippoRAG2Retriever that uses safe matrix
multiplication operations to prevent mathematical warnings.
"""

import numpy as np
import warnings
from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever

def safe_matmul(A, B, operation_name="matmul"):
    """Perform matrix multiplication with warning suppression."""
    with warnings.catch_warnings():
        warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*encountered in matmul')
        warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*divide by zero.*')
        warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*overflow.*')
        warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*invalid value.*')
        
        result = A @ B
        
        # Handle any problematic values
        if np.isnan(result).any():
            result = np.nan_to_num(result, nan=0.0)
        if np.isinf(result).any():
            result = np.nan_to_num(result, posinf=1.0, neginf=-1.0)
            
        return result

def robust_min_max_normalize(x):
    """Robust min-max normalization with edge case handling."""
    # Handle empty or single-value arrays
    if len(x) == 0:
        return x
    if len(x) == 1:
        return np.array([1.0])
    
    min_val = np.min(x)
    max_val = np.max(x)
    range_val = max_val - min_val
    
    # Handle the case where all values are the same
    if range_val == 0 or range_val < np.finfo(x.dtype).eps:
        return np.ones_like(x)
    
    # Standard min-max normalization
    normalized = (x - min_val) / range_val
    
    # Final cleanup
    normalized = np.nan_to_num(normalized, nan=0.0, posinf=1.0, neginf=0.0)
    
    return normalized

class SafeHippoRAG2Retriever(HippoRAG2Retriever):
    """
    A patched version of HippoRAG2Retriever with safe matrix operations.
    """
    
    def query2node(self, query, topN=10):
        """Override with safe matrix operations."""
        query_emb = self.sentence_encoder.encode([query], query_type="entity")
        
        # Use safe matrix multiplication
        scores = safe_matmul(self.node_embeddings, query_emb[0].T, "query2node")
        scores = robust_min_max_normalize(scores)
        
        index_matrix = np.argsort(scores)[-topN:][::-1]
        similarity_matrix = [scores[i] for i in index_matrix]
        result_node_score_dict = {}
        
        for index, sim_score in zip(index_matrix, similarity_matrix):
            node = self.node_list[index]
            result_node_score_dict[node] = sim_score

        return result_node_score_dict
    
    def query2edge(self, query, topN=10):
        """Override with safe matrix operations."""
        query_emb = self.sentence_encoder.encode([query], query_type="edge")
        
        # Use safe matrix multiplication
        scores = safe_matmul(self.edge_embeddings, query_emb[0].T, "query2edge")
        scores = robust_min_max_normalize(scores)
        
        index_matrix = np.argsort(scores)[-topN:][::-1]
        log_edge_list = []
        
        for index in index_matrix:
            edge = self.edge_list[index]
            edge_str = [self.KG.nodes[edge[0]]['id'], self.KG.edges[edge]['relation'], self.KG.nodes[edge[1]]['id']]
            log_edge_list.append(edge_str)

        similarity_matrix = [scores[i] for i in index_matrix]
        before_filter_edge_json = {'fact': []}
        
        for index, sim_score in zip(index_matrix, similarity_matrix):
            edge = self.edge_list[index]
            edge_str = [self.KG.nodes[edge[0]]['id'], self.KG.edges[edge]['relation'], self.KG.nodes[edge[1]]['id']]
            before_filter_edge_json['fact'].append(edge_str)

        return before_filter_edge_json
    
    def query2passage(self, query, weight_adjust=0.05):
        """Override with safe matrix operations."""
        query_emb = self.sentence_encoder.encode([query], query_type="passage") 
        
        # Use safe matrix multiplication
        sim_scores = safe_matmul(self.text_embeddings, query_emb[0].T, "query2passage")
        sim_scores = robust_min_max_normalize(sim_scores) * weight_adjust
        
        return dict(zip(self.text_id_list, sim_scores))
