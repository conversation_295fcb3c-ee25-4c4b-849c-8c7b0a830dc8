#!/usr/bin/env python3
"""
Test embedding models for financial domain performance
Compare nvidia/NV-Embed-v2 vs all-MiniLM-L6-v2 vs all-mpnet-base-v2
"""

import numpy as np
import time
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer
import torch

def test_embedding_model(model_name: str, device: str = None):
    """Test an embedding model with financial content."""
    print(f"\n🧪 Testing {model_name}")
    print("=" * 60)
    
    if device is None:
        device = "cuda" if torch.cuda.is_available() else "mps" if torch.backends.mps.is_available() else "cpu"
    
    try:
        start_time = time.time()
        
        # Load model
        model = SentenceTransformer(model_name, trust_remote_code=True, device=device)
        load_time = time.time() - start_time
        
        print(f"✅ Model loaded in {load_time:.1f}s")
        print(f"   Device: {device}")
        print(f"   Max sequence length: {model.max_seq_length}")
        print(f"   Embedding dimension: {model.get_sentence_embedding_dimension()}")
        
        # Financial test queries
        financial_queries = [
            "Risk reversal option trading strategy in financial markets",
            "Market maker cockpit for FX currency trading scenarios",
            "Bridge trading platform configuration and setup procedures",
            "Currency swap and forward contract pricing models",
            "Automated trading system risk management protocols"
        ]
        
        # General queries for comparison
        general_queries = [
            "The weather is sunny today",
            "Database connection configuration settings",
            "Machine learning model training procedures",
            "Software engineering best practices guide",
            "Network security protocol implementation"
        ]
        
        print(f"\n🔍 Testing financial domain queries...")
        
        # Test financial queries
        start_time = time.time()
        financial_embeddings = model.encode(financial_queries, show_progress_bar=False)
        financial_time = time.time() - start_time
        
        print(f"   Financial embeddings: {financial_embeddings.shape}")
        print(f"   Encoding time: {financial_time:.2f}s")
        
        # Test general queries
        start_time = time.time()
        general_embeddings = model.encode(general_queries, show_progress_bar=False)
        general_time = time.time() - start_time
        
        print(f"   General embeddings: {general_embeddings.shape}")
        print(f"   Encoding time: {general_time:.2f}s")
        
        # Compute financial similarity matrix
        financial_similarities = cosine_similarity(financial_embeddings)
        
        print(f"\n📊 Financial Domain Analysis:")
        print(f"   Mean financial similarity: {financial_similarities.mean():.3f}")
        print(f"   Max financial similarity: {financial_similarities.max():.3f}")
        print(f"   Min financial similarity: {financial_similarities.min():.3f}")
        
        # Specific financial pairs
        risk_reversal_vs_mmc = financial_similarities[0, 1]  # Risk reversal vs MMC
        bridge_vs_currency = financial_similarities[2, 3]    # Bridge vs Currency swap
        
        print(f"   Risk reversal vs MMC similarity: {risk_reversal_vs_mmc:.3f}")
        print(f"   Bridge vs Currency swap similarity: {bridge_vs_currency:.3f}")
        
        # Cross-domain similarity (financial vs general)
        cross_similarities = cosine_similarity(financial_embeddings, general_embeddings)
        print(f"   Mean cross-domain similarity: {cross_similarities.mean():.3f}")
        
        # Financial performance score
        financial_coherence = np.mean([s for i, row in enumerate(financial_similarities) 
                                     for j, s in enumerate(row) if i != j])
        cross_contamination = cross_similarities.mean()
        
        # Higher financial coherence is better, lower cross-contamination is better
        financial_score = financial_coherence - (cross_contamination * 0.5)
        
        print(f"\n🎯 Financial Performance Score: {financial_score:.3f}")
        print(f"   (Higher is better for financial domain)")
        
        return {
            'model_name': model_name,
            'load_time': load_time,
            'embedding_dim': model.get_sentence_embedding_dimension(),
            'financial_coherence': financial_coherence,
            'cross_contamination': cross_contamination,
            'financial_score': financial_score,
            'risk_mmc_similarity': risk_reversal_vs_mmc,
            'success': True
        }
        
    except Exception as e:
        print(f"❌ Failed to test {model_name}: {e}")
        return {
            'model_name': model_name,
            'error': str(e),
            'success': False
        }

def main():
    """Test multiple embedding models for financial domain."""
    print("🚀 Financial Domain Embedding Model Comparison")
    print("=" * 80)
    
    models_to_test = [
        "nvidia/NV-Embed-v2",
        "sentence-transformers/all-mpnet-base-v2", 
        "all-MiniLM-L6-v2"
    ]
    
    results = []
    
    for model_name in models_to_test:
        result = test_embedding_model(model_name)
        results.append(result)
        
        if result['success']:
            print(f"✅ {model_name} completed successfully")
        else:
            print(f"❌ {model_name} failed")
    
    # Summary comparison
    print(f"\n🏆 FINANCIAL DOMAIN EMBEDDING MODEL COMPARISON")
    print("=" * 80)
    
    successful_results = [r for r in results if r['success']]
    
    if successful_results:
        # Sort by financial score (higher is better)
        successful_results.sort(key=lambda x: x['financial_score'], reverse=True)
        
        print(f"{'Rank':<4} {'Model':<40} {'Dim':<6} {'Load(s)':<8} {'Fin Score':<10} {'Risk-MMC':<10}")
        print("-" * 80)
        
        for i, result in enumerate(successful_results, 1):
            print(f"{i:<4} {result['model_name']:<40} {result['embedding_dim']:<6} "
                  f"{result['load_time']:<8.1f} {result['financial_score']:<10.3f} "
                  f"{result['risk_mmc_similarity']:<10.3f}")
        
        # Recommendation
        best_model = successful_results[0]
        print(f"\n🎯 RECOMMENDATION:")
        print(f"   Best financial domain model: {best_model['model_name']}")
        print(f"   Financial performance score: {best_model['financial_score']:.3f}")
        print(f"   Risk-reversal vs MMC similarity: {best_model['risk_mmc_similarity']:.3f}")
        
        # Performance analysis
        if best_model['model_name'] == 'nvidia/NV-Embed-v2':
            print(f"   🏆 NVIDIA model provides superior financial domain performance!")
        elif best_model['model_name'] == 'sentence-transformers/all-mpnet-base-v2':
            print(f"   ⭐ MPNet provides good financial domain performance with faster loading")
        else:
            print(f"   ⚠️  Consider upgrading from MiniLM for better financial performance")
    
    else:
        print("❌ No models tested successfully")
    
    return successful_results

if __name__ == "__main__":
    results = main()