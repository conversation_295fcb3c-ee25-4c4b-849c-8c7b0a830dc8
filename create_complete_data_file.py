#!/usr/bin/env python3
"""
Create complete_data.pkl for Real HippoRAG2 Integration

This script creates the complete data structure that our real HippoRAG2 implementation needs.
Uses the existing working data loading from hipporag2_pipeline.py
"""

import pickle
from pathlib import Path
from hipporag2_pipeline import HippoRAG2<PERSON>ipeline

def create_complete_data_file():
    """Create complete_data.pkl with all working HippoRAG2 components."""
    
    print("🔧 Creating complete_data.pkl for real HippoRAG2 integration")
    print("=" * 60)
    
    # Initialize pipeline to load existing data
    pipeline = HippoRAG2Pipeline()
    
    # Load the complete data structure
    print("📂 Loading complete data structure...")
    data = pipeline.load_existing_data()
    
    # Save as complete_data.pkl
    output_path = Path("import/pdf_dataset/complete_data.pkl")
    
    print(f"💾 Saving complete data to: {output_path}")
    with open(output_path, 'wb') as f:
        pickle.dump(data, f)
    
    print("✅ complete_data.pkl created successfully!")
    print(f"   📊 Graph: {len(data['KG'].nodes)} nodes, {len(data['KG'].edges)} edges")
    print(f"   🔢 Node embeddings: {data.get('node_embeddings', []).shape if hasattr(data.get('node_embeddings', []), 'shape') else 'Not available'}")
    print(f"   🔗 Edge embeddings: {data.get('edge_embeddings', []).shape if hasattr(data.get('edge_embeddings', []), 'shape') else 'Not available'}")
    print(f"   📄 Text dictionary: {len(data.get('text_dict', {}))} entries")
    
    return True

if __name__ == "__main__":
    success = create_complete_data_file()
    exit(0 if success else 1)