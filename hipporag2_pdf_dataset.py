#!/usr/bin/env python3
"""
HippoRAG2 with pdf_dataset - The correct dataset with 1207 passages.
This script uses the full financial dataset that contains risk reversal, OCO, and other content.
"""

import os
import sys
from pathlib import Path
from sentence_transformers import SentenceTransformer
from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
from atlas_rag.vectorstore.create_graph_index import create_embeddings_and_index
from atlas_rag.retriever import HippoRAG2Retriever
from atlas_rag.llm_generator import LLMGenerator
from openai import OpenAI
from ollama_model_manager import OllamaModelManager
import time

# Configuration - Change this to use a different Ollama model
DEFAULT_OLLAMA_MODEL = "qwen3:30b-a3b-thinking-2507-q4_K_M"  # Edit this to use your preferred model

class HippoRAG2PDFDataset:
    """HippoRAG2 pipeline using the correct pdf_dataset with 1207 passages."""
    
    def __init__(self, dataset_dir: str = "import/pdf_dataset", 
                 use_ollama: bool = True,
                 ollama_model: str = DEFAULT_OLLAMA_MODEL):
        """Initialize the pipeline with the pdf_dataset."""
        
        self.dataset_dir = dataset_dir
        self.use_ollama = use_ollama
        self.ollama_model = ollama_model
        self.data = None
        self.sentence_encoder = None
        self.retriever = None
        self.llm_generator = None
        
    def load_data(self):
        """Load data using AutoSchemaKG's create_embeddings_and_index()."""
        
        print("📊 Loading pdf_dataset with 1207 passages...")
        print(f"   This is the CORRECT dataset with financial content")
        
        # Initialize embedding model
        encoder_model_name = "sentence-transformers/all-mpnet-base-v2"
        sentence_model = SentenceTransformer(encoder_model_name)
        self.sentence_encoder = SentenceEmbedding(sentence_model)
        
        # Check if GraphML exists
        graphml_path = Path(self.dataset_dir) / "kg_graphml" / "pdf_dataset_graph.graphml"
        if not graphml_path.exists():
            print(f"❌ GraphML not found at {graphml_path}")
            print("   Looking for alternative GraphML files...")
            
            # Try alternative names
            alt_names = ["_graph.graphml", "knowledge_graph.graphml", "pdf_dataset_graph_fixed.graphml"]
            for alt_name in alt_names:
                alt_path = Path(self.dataset_dir) / "kg_graphml" / alt_name
                if alt_path.exists():
                    print(f"   Found: {alt_path}")
                    # Use the found GraphML
                    keyword = alt_name.replace("_graph.graphml", "").replace(".graphml", "")
                    break
            else:
                keyword = "pdf_dataset"
        else:
            keyword = "pdf_dataset"
        
        print(f"   Using keyword: '{keyword}' for data loading")
        
        # Load data with AutoSchemaKG method
        self.data = create_embeddings_and_index(
            sentence_encoder=self.sentence_encoder,
            model_name=encoder_model_name,
            working_directory=self.dataset_dir,
            keyword=keyword,
            include_concept=True,
            include_events=True,
            normalize_embeddings=True,
            text_batch_size=64,
            node_and_edge_batch_size=256
        )
        
        print(f"✅ Loaded {len(self.data['node_list'])} nodes, {len(self.data['text_dict'])} passages")
        
        # Verify we have the right dataset
        if len(self.data['text_dict']) < 1000:
            print(f"⚠️  Warning: Only {len(self.data['text_dict'])} passages loaded.")
            print("   Expected ~1207 passages for pdf_dataset.")
            print("   You might be using the wrong dataset!")
        
    def setup_llm(self):
        """Setup LLM generator (Ollama or fallback)."""
        
        if self.use_ollama:
            print(f"🤖 Setting up Ollama with {self.ollama_model}...")
            
            # Ollama client configuration
            client = OpenAI(
                api_key="EMPTY",
                base_url="http://localhost:11434/v1"
            )
            
            self.llm_generator = LLMGenerator(
                client=client,
                model_name=self.ollama_model
            )
            
            # Test Ollama connection
            try:
                test_response = client.chat.completions.create(
                    model=self.ollama_model,
                    messages=[{"role": "user", "content": "test"}],
                    max_tokens=10,
                    temperature=0.1
                )
                print("✅ Ollama connection successful")
            except Exception as e:
                print(f"⚠️  Ollama connection failed: {str(e)}")
                print("   Make sure Ollama is running with the model loaded")
                print(f"   Run: ollama run {self.ollama_model}")
                self.use_ollama = False
        
        if not self.use_ollama:
            print("📝 Using mock LLM for testing")
            client = OpenAI(api_key="EMPTY", base_url="http://localhost:8000/v1")
            self.llm_generator = LLMGenerator(client=client, model_name="mock")
    
    def initialize_retriever(self):
        """Initialize HippoRAG2Retriever with loaded data."""
        
        print("🔍 Initializing HippoRAG2Retriever...")
        
        self.retriever = HippoRAG2Retriever(
            llm_generator=self.llm_generator,
            sentence_encoder=self.sentence_encoder,
            data=self.data
        )
        
        print("✅ Retriever initialized")
    
    def ask(self, question: str, topN: int = 5, verbose: bool = True):
        """Ask a question and get an answer using HippoRAG2 + LLM."""
        
        if verbose:
            print(f"\n❓ Question: {question}")
            print("-" * 70)
        
        # Retrieve relevant passages
        start_time = time.time()
        passages, node_ids, scores = self.retriever.retrieve(question, topN=topN)
        retrieval_time = time.time() - start_time
        
        if verbose:
            print(f"📊 Retrieved {len(passages)} passages in {retrieval_time:.2f}s")
            if scores:
                print(f"   Scores: {[f'{s:.4f}' for s in scores]}")
        
        # Show ALL retrieved passages (fixed display issue)
        if verbose and passages:
            print(f"\n📄 Retrieved Passages ({len(passages)} total):")
            for i, (passage, score) in enumerate(zip(passages, scores), 1):
                print(f"\n  --- Passage {i} (Score: {score:.4f}) ---")
                # Show more of each passage for context
                preview = passage[:500] + "..." if len(passage) > 500 else passage
                print(f"  {preview}")
                
                # Check if it looks like actual text or just entity name
                if len(passage) < 100 and not any(c in passage for c in ['.', '!', '?', ',']):
                    print(f"  ⚠️  Warning: This looks like an entity name, not passage text")
        
        # Generate answer with LLM
        if passages:
            context = "\n\n---\n\n".join(passages)
            
            if verbose:
                print(f"\n💬 Generating answer with {self.ollama_model}...")
                print(f"   Context length: {len(context)} chars")
            
            try:
                answer = self.llm_generator.generate_with_context(
                    question=question,
                    context=context,
                    max_new_tokens=512,
                    temperature=0.7
                )
                
                if verbose:
                    print(f"\n💡 Answer:")
                    print(f"   {answer}")
                
                return answer, passages, scores
                
            except Exception as e:
                error_msg = f"Error generating answer: {str(e)}"
                if verbose:
                    print(f"❌ {error_msg}")
                return error_msg, passages, scores
        else:
            if verbose:
                print("❌ No relevant passages found")
            return "No relevant information found.", [], []
    
    def search_content(self, keyword: str):
        """Search for specific content in the text passages."""
        
        print(f"\n🔍 Searching for '{keyword}' in passages...")
        matches = []
        
        for node_id, text in self.data['text_dict'].items():
            if keyword.lower() in text.lower():
                matches.append((node_id, text))
        
        print(f"Found {len(matches)} passages containing '{keyword}'")
        
        if matches:
            print("\nSample matches:")
            for i, (node_id, text) in enumerate(matches[:3], 1):
                # Find the keyword in context
                idx = text.lower().index(keyword.lower())
                start = max(0, idx - 100)
                end = min(len(text), idx + len(keyword) + 100)
                context = text[start:end]
                
                print(f"\n  {i}. Node {node_id[:8]}...")
                print(f"     ...{context}...")
        
        return matches
    
    def run_test_queries(self):
        """Run test queries to verify the dataset has the right content."""
        
        print("\n🧪 Running Test Queries on pdf_dataset")
        print("=" * 70)
        
        # First, verify content exists
        print("\n📋 Verifying dataset content...")
        self.search_content("risk reversal")
        self.search_content("OCO")
        self.search_content("Market Maker Cockpit")
        
        print("\n" + "=" * 70)
        
        # Now run actual queries
        test_queries = [
            "What is an OCO order and how does it work?",
            "How do I configure risk reversal in the trading system?",
            "Explain the Market Maker Cockpit features",
            "What are the FIX protocol specifications for market takers?",
            "How do Bank Baskets work in the 360T system?"
        ]
        
        for query in test_queries:
            self.ask(query, topN=5, verbose=True)
            print("\n" + "=" * 70)
            time.sleep(1)

def main():
    """Main function to run HippoRAG2 with the correct pdf_dataset."""
    
    print("🚀 HippoRAG2 with pdf_dataset (1207 passages)")
    print("=" * 70)
    print("This uses the CORRECT dataset with all financial content:")
    print("  - 1207 text passages (not just 12!)")
    print("  - Risk reversal, OCO, and other financial terms")
    print("  - Complete 360T documentation")
    print()
    
    # Initialize pipeline with pdf_dataset
    pipeline = HippoRAG2PDFDataset(
        dataset_dir="import/pdf_dataset",
        use_ollama=True,
        ollama_model="qwen3:30b-a3b-thinking-2507-q4_K_M"
    )
    
    # Load data
    print("Step 1: Loading pdf_dataset...")
    pipeline.load_data()
    
    # Setup LLM
    print("\nStep 2: Setting up LLM...")
    pipeline.setup_llm()
    
    # Initialize retriever
    print("\nStep 3: Initializing retriever...")
    pipeline.initialize_retriever()
    
    # Run test queries
    print("\nStep 4: Running test queries...")
    pipeline.run_test_queries()

if __name__ == "__main__":
    main()