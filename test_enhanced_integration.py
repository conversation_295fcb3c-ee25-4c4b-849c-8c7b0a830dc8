#!/usr/bin/env python3
"""
Test Enhanced HippoRAG2 Integration - Phase 3 Validation

This script validates the complete integration of the Enhanced HippoRAG2 Retriever
into the main pipeline, ensuring the MMC query issue is resolved and performance
improvements are maintained.
"""

import time
import sys
import os
from pathlib import Path

# Set environment variable to avoid tokenizer warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Add the project root to Python path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_pipeline_integration():
    """Test that enhanced retriever is properly integrated into main pipeline."""
    print("🧪 PHASE 3: Enhanced HippoRAG2 Integration Testing")
    print("="*60)
    
    print("\n📊 Step 1: Testing hipporag2_pipeline.py integration...")
    
    try:
        from hipporag2_pipeline import HippoRAG2Pipeline
        
        pipeline = HippoRAG2Pipeline(data_directory="import/pdf_dataset")
        print("   ✅ Pipeline imports successfully")
        
        # Check that enhanced retriever is being used
        import inspect
        source = inspect.getsource(pipeline.initialize_hipporag2)
        if "EnhancedHippoRAG2Retriever" in source:
            print("   ✅ Pipeline uses EnhancedHippoRAG2Retriever")
        else:
            print("   ❌ Pipeline still uses original HippoRAG2Retriever")
            return False
            
        print("   ✅ hipporag2_pipeline.py integration successful")
        
    except Exception as e:
        print(f"   ❌ hipporag2_pipeline.py integration failed: {e}")
        return False
    
    print("\n📊 Step 2: Testing run_fixed_hipporag2.py integration...")
    
    try:
        from run_fixed_hipporag2 import FixedHippoRAG2Pipeline
        
        fixed_pipeline = FixedHippoRAG2Pipeline()
        print("   ✅ Fixed pipeline imports successfully")
        
        # Check that enhanced retriever is being used
        source = inspect.getsource(fixed_pipeline.initialize_hipporag2)
        if "EnhancedHippoRAG2Retriever" in source:
            print("   ✅ Fixed pipeline uses EnhancedHippoRAG2Retriever")
        else:
            print("   ❌ Fixed pipeline still uses original HippoRAG2Retriever")
            return False
            
        print("   ✅ run_fixed_hipporag2.py integration successful")
        
    except Exception as e:
        print(f"   ❌ run_fixed_hipporag2.py integration failed: {e}")
        return False
    
    return True

def test_enhanced_retriever_functionality():
    """Test enhanced retriever functionality directly."""
    print("\n📊 Step 3: Testing Enhanced Retriever Direct Functionality...")
    
    try:
        from enhanced_hipporag2_retriever import EnhancedHippoRAG2Retriever
        
        print("   ✅ EnhancedHippoRAG2Retriever imports successfully")
        
        # Test domain entity indexing
        print("   🔍 Testing domain entity indexing...")
        
        # Mock test to verify key methods exist
        methods_to_check = [
            '_build_domain_entity_indexes',
            '_expand_query', 
            '_get_domain_starting_entities',
            '_score_text_content_relevance',
            'enhanced_retrieve_personalization_dict'
        ]
        
        for method_name in methods_to_check:
            if hasattr(EnhancedHippoRAG2Retriever, method_name):
                print(f"      ✅ Method {method_name} exists")
            else:
                print(f"      ❌ Method {method_name} missing")
                return False
        
        print("   ✅ Enhanced retriever functionality verified")
        return True
        
    except Exception as e:
        print(f"   ❌ Enhanced retriever testing failed: {e}")
        return False

def test_mmc_query_readiness():
    """Test that the system is ready for MMC query resolution."""
    print("\n📊 Step 4: Testing MMC Query Readiness...")
    
    try:
        # Test query expansion
        from enhanced_hipporag2_retriever import EnhancedHippoRAG2Retriever
        
        # Create mock instance to test query expansion
        class MockEnhancedRetriever(EnhancedHippoRAG2Retriever):
            def __init__(self):
                # Skip parent initialization for testing
                self.domain_entities = {'mmc': [], 'scenario': []}
        
        mock_retriever = MockEnhancedRetriever()
        
        # Test query expansion
        test_query = "how to create scenarios in MMC?"
        expanded_queries = mock_retriever._expand_query(test_query)
        
        print(f"   📝 Original query: {test_query}")
        print(f"   📝 Expanded queries: {len(expanded_queries)} variants")
        for i, eq in enumerate(expanded_queries, 1):
            print(f"      {i}. {eq}")
        
        # Check that expansion includes Market Maker Cockpit
        mmc_expanded = any('market maker cockpit' in eq.lower() for eq in expanded_queries)
        if mmc_expanded:
            print("   ✅ Query expansion includes 'Market Maker Cockpit'")
        else:
            print("   ❌ Query expansion missing 'Market Maker Cockpit'")
            return False
        
        # Test content scoring
        test_content_mmc = "The MMC Market Maker Cockpit allows users to create pricing scenarios for risk management."
        test_content_generic = "This document discusses FIX protocol implementation details."
        
        mmc_score = mock_retriever._score_text_content_relevance(test_content_mmc, test_query)
        generic_score = mock_retriever._score_text_content_relevance(test_content_generic, test_query)
        
        print(f"   📊 MMC content relevance score: {mmc_score}")
        print(f"   📊 Generic content relevance score: {generic_score}")
        
        if mmc_score > generic_score:
            print("   ✅ Content scoring correctly prioritizes MMC content")
        else:
            print("   ❌ Content scoring not prioritizing MMC content correctly")
            return False
        
        print("   ✅ MMC query enhancement features ready")
        return True
        
    except Exception as e:
        print(f"   ❌ MMC query readiness test failed: {e}")
        return False

def main():
    """Run complete integration testing."""
    print("🚀 ENHANCED HIPPORAG2 INTEGRATION TEST SUITE")
    print("   Validating Phase 3: Complete pipeline integration")
    print()
    
    start_time = time.time()
    
    # Run all tests
    tests = [
        ("Pipeline Integration", test_pipeline_integration),
        ("Enhanced Retriever Functionality", test_enhanced_retriever_functionality), 
        ("MMC Query Readiness", test_mmc_query_readiness)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        print("-" * 50)
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            print(f"💥 {test_name}: ERROR - {e}")
            results[test_name] = False
    
    # Summary
    elapsed_time = time.time() - start_time
    
    print("\n" + "="*60)
    print("📊 INTEGRATION TEST RESULTS")
    print("="*60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    print(f"⏱️  Total time: {elapsed_time:.2f} seconds")
    
    if passed == total:
        print("\n🎉 ALL INTEGRATION TESTS PASSED!")
        print("   Enhanced HippoRAG2 retriever successfully integrated")
        print("   Ready for live MMC query validation")
        return True
    else:
        print(f"\n❌ {total - passed} integration tests failed")
        print("   Integration incomplete - requires fixes before live testing")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)