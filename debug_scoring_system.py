#!/usr/bin/env python3
"""
Debug the scoring system to understand why scores are always 1.0000
"""

import os
import sys
import pickle
import numpy as np
from pathlib import Path

# Set environment and add path
os.environ["TOKENIZERS_PARALLELISM"] = "false"
sys.path.insert(0, str(Path(__file__).parent))

from setup_embedding_model import setup_embedding_model
from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever, min_max_normalize

def main():
    print("🔍 DEBUGGING SCORING SYSTEM")
    print("="*50)
    
    # Setup
    embedding_model = setup_embedding_model()
    
    # Load data
    data_file = Path("import/pdf_dataset/complete_data.pkl")
    with open(data_file, 'rb') as f:
        complete_data = pickle.load(f)
    
    print(f"📊 Data loaded:")
    print(f"   • Node embeddings: {len(complete_data.get('node_embeddings', []))}")
    print(f"   • Node list: {len(complete_data.get('node_list', []))}")
    
    # Check node embeddings structure
    node_embeddings = complete_data.get('node_embeddings', [])
    node_list = complete_data.get('node_list', [])
    
    if len(node_embeddings) > 0:
        print(f"\n🔍 Node Embeddings Analysis:")
        print(f"   • Type: {type(node_embeddings)}")
        print(f"   • Shape: {node_embeddings.shape if hasattr(node_embeddings, 'shape') else 'No shape'}")
        print(f"   • First few values: {node_embeddings[0][:5] if len(node_embeddings) > 0 else 'None'}")
        
        # Check if embeddings are normalized or have weird values
        if hasattr(node_embeddings, 'shape'):
            first_embedding = node_embeddings[0]
            print(f"   • First embedding norm: {np.linalg.norm(first_embedding):.6f}")
            print(f"   • Embedding range: [{np.min(first_embedding):.6f}, {np.max(first_embedding):.6f}]")
    
    # Test similarity calculation manually
    print(f"\n🧪 Manual Similarity Test:")
    query = "Risk Reversal strategy"
    print(f"Query: '{query}'")
    
    # Get query embedding
    query_emb = embedding_model.encode([query], query_type="entity")[0]
    print(f"Query embedding shape: {query_emb.shape}")
    print(f"Query embedding norm: {np.linalg.norm(query_emb):.6f}")
    
    if len(node_embeddings) > 0 and hasattr(node_embeddings, 'shape'):
        # Manual similarity calculation
        print(f"\n🔢 Raw Similarity Calculation:")
        
        # Calculate raw similarities
        raw_scores = node_embeddings @ query_emb.T
        print(f"Raw scores shape: {raw_scores.shape}")
        print(f"Raw scores range: [{np.min(raw_scores):.6f}, {np.max(raw_scores):.6f}]")
        print(f"Raw scores (first 10): {raw_scores[:10]}")
        
        # Apply min-max normalization
        normalized_scores = min_max_normalize(raw_scores)
        print(f"\nNormalized scores range: [{np.min(normalized_scores):.6f}, {np.max(normalized_scores):.6f}]")
        print(f"Normalized scores (first 10): {normalized_scores[:10]}")
        
        # Check if all values are the same (would cause 1.0000 scores)
        unique_raw = len(np.unique(np.round(raw_scores, 6)))
        unique_normalized = len(np.unique(np.round(normalized_scores, 6)))
        print(f"\nUnique raw scores: {unique_raw}")
        print(f"Unique normalized scores: {unique_normalized}")
        
        if unique_raw <= 1:
            print("❌ PROBLEM: All raw scores are identical!")
            print("   This suggests embeddings might be corrupted or all the same")
        elif unique_normalized <= 1:
            print("❌ PROBLEM: All normalized scores are identical!")
            print("   This suggests min-max normalization is not working properly")
        
        # Get top indices
        top_indices = np.argsort(normalized_scores)[-10:][::-1]
        print(f"\nTop 10 entities:")
        for i, idx in enumerate(top_indices, 1):
            if idx < len(node_list):
                entity = node_list[idx]
                raw_score = raw_scores[idx]
                norm_score = normalized_scores[idx]
                print(f"   #{i}: {entity[:40]}... (raw: {raw_score:.6f}, norm: {norm_score:.6f})")
    
    # Test with HippoRAG2 query2node method
    print(f"\n🎯 HippoRAG2 query2node Test:")
    
    try:
        # Create minimal retriever (without LLM to avoid complications)
        from atlas_rag.retriever.inference_config import InferenceConfig
        
        inference_config = InferenceConfig()
        
        retriever = HippoRAG2Retriever(
            llm_generator=None,  # Skip LLM
            sentence_encoder=embedding_model,
            data=complete_data,
            inference_config=inference_config
        )
        
        # Test query2node directly
        node_scores = retriever.query2node(query, topN=10)
        
        print(f"HippoRAG2 returned {len(node_scores)} entities:")
        sorted_entities = sorted(node_scores.items(), key=lambda x: x[1], reverse=True)
        
        score_values = [score for _, score in sorted_entities]
        unique_scores = len(set([round(s, 6) for s in score_values]))
        
        print(f"Unique HippoRAG2 scores: {unique_scores}")
        print(f"Score distribution: min={min(score_values):.6f}, max={max(score_values):.6f}")
        
        for i, (entity, score) in enumerate(sorted_entities[:5], 1):
            print(f"   #{i}: {entity[:40]}... (score: {score:.6f})")
            
        if unique_scores <= 1:
            print("❌ CONFIRMED: HippoRAG2 scores are all identical!")
        else:
            print("✅ HippoRAG2 scores show proper variation")
    
    except Exception as e:
        print(f"❌ HippoRAG2 test failed: {e}")
    
    print(f"\n" + "="*50)
    print("🎯 SCORING DEBUG SUMMARY:")
    print("If scores are all 1.0000, possible causes:")
    print("1. All embeddings are identical (corrupted data)")
    print("2. Query embedding is all zeros")  
    print("3. min_max_normalize function has a bug")
    print("4. Embedding dimensions mismatch (but we fixed that)")

if __name__ == "__main__":
    main()