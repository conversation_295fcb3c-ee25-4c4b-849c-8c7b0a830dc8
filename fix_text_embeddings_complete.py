#!/usr/bin/env python3
"""
Complete fix for text embeddings - rebuild all missing text embeddings and FAISS indexes.
"""

import os
import sys
import pickle
import numpy as np
import faiss
from pathlib import Path
from datetime import datetime
from tqdm import tqdm

# Set environment and add path
os.environ["TOKENIZERS_PARALLELISM"] = "false"
sys.path.insert(0, str(Path(__file__).parent))

from setup_embedding_model import setup_embedding_model

class CompleteTextEmbeddingFixer:
    """Fix the broken text embedding system by rebuilding all embeddings and indexes."""
    
    def __init__(self, data_dir: str = "import/pdf_dataset"):
        self.data_dir = Path(data_dir)
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.embedding_model = None
        self.batch_size = 32  # Conservative batch size for stability
        
    def setup_embedding_model(self):
        """Setup the embedding model."""
        print("🔧 Setting up embedding model...")
        self.embedding_model = setup_embedding_model()
        print(f"✅ Embedding model ready: {self.embedding_model}")
        
    def load_data(self):
        """Load the complete data structure."""
        print("📊 Loading complete data...")
        data_file = self.data_dir / "complete_data.pkl"
        
        with open(data_file, 'rb') as f:
            self.complete_data = pickle.load(f)
        
        print(f"✅ Data loaded successfully")
        print(f"   • text_dict entries: {len(self.complete_data.get('text_dict', {}))}")
        print(f"   • Current text embeddings: {len(self.complete_data.get('text_embeddings', []))}")
        print(f"   • Current text_id_list: {len(self.complete_data.get('text_id_list', []))}")
        
    def rebuild_text_embeddings(self):
        """Rebuild all text embeddings from text_dict."""
        print("\n🔧 Rebuilding ALL text embeddings...")
        
        text_dict = self.complete_data.get('text_dict', {})
        if not text_dict:
            print("❌ No text_dict found!")
            return False
            
        print(f"📊 Processing {len(text_dict)} text entries...")
        
        # Create new structures
        new_text_id_list = list(text_dict.keys())
        new_text_embeddings = []
        
        # Process in batches
        total_batches = (len(new_text_id_list) + self.batch_size - 1) // self.batch_size
        
        for i in tqdm(range(0, len(new_text_id_list), self.batch_size), 
                     desc="Generating embeddings", total=total_batches):
            batch_ids = new_text_id_list[i:i + self.batch_size]
            batch_texts = [text_dict[text_id] for text_id in batch_ids]
            
            # Generate embeddings for batch
            try:
                batch_embeddings = self.embedding_model.encode(batch_texts, show_progress_bar=False)
                new_text_embeddings.extend(batch_embeddings)
            except Exception as e:
                print(f"❌ Error processing batch {i//self.batch_size + 1}: {e}")
                # Add zero embeddings as fallback
                dim = 768  # all-mpnet-base-v2 dimension
                for _ in batch_ids:
                    new_text_embeddings.append(np.zeros(dim))
        
        # Update data structure
        self.complete_data['text_id_list'] = new_text_id_list
        self.complete_data['text_embeddings'] = np.array(new_text_embeddings)
        
        print(f"✅ Generated {len(new_text_embeddings)} text embeddings")
        print(f"   • Embedding dimension: {new_text_embeddings[0].shape if new_text_embeddings else 'N/A'}")
        
        return True
        
    def rebuild_text_faiss_index(self):
        """Rebuild the FAISS index for text embeddings."""
        print("\n🔧 Rebuilding text FAISS index...")
        
        text_embeddings = self.complete_data.get('text_embeddings', [])
        if len(text_embeddings) == 0:
            print("❌ No text embeddings to index!")
            return False
            
        # Convert to numpy array if not already
        if not isinstance(text_embeddings, np.ndarray):
            text_embeddings = np.array(text_embeddings)
            
        print(f"📊 Indexing {len(text_embeddings)} embeddings...")
        print(f"   • Embedding shape: {text_embeddings.shape}")
        
        # Create FAISS index
        dimension = text_embeddings.shape[1]
        index = faiss.IndexFlatIP(dimension)  # Inner product for cosine similarity
        
        # Normalize embeddings for cosine similarity
        faiss.normalize_L2(text_embeddings)
        
        # Add embeddings to index
        index.add(text_embeddings.astype('float32'))
        
        # Update data structure
        self.complete_data['text_index'] = index
        
        print(f"✅ FAISS text index created successfully")
        print(f"   • Index size: {index.ntotal}")
        print(f"   • Dimension: {index.d}")
        
        return True
        
    def test_risk_reversal_retrieval(self):
        """Test if Risk Reversal content can now be retrieved."""
        print("\n🎯 Testing Risk Reversal retrieval...")
        
        text_index = self.complete_data.get('text_index')
        text_id_list = self.complete_data.get('text_id_list', [])
        text_dict = self.complete_data.get('text_dict', {})
        
        if text_index is None:
            print("❌ No text index found!")
            return False
            
        # Test queries
        test_queries = [
            "Risk Reversal strategy FX option",
            "Example 4 Risk Reversal Strategy",
            "Figure 24 Pricing risk reversal",
            "Zero Cost Strategy FX"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Testing: '{query}'")
            
            # Generate query embedding
            query_embedding = self.embedding_model.encode([query])[0].reshape(1, -1)
            faiss.normalize_L2(query_embedding)
            
            # Search
            similarities, indices = text_index.search(query_embedding.astype('float32'), 10)
            
            found_risk_reversal = False
            for i, (sim, idx) in enumerate(zip(similarities[0], indices[0]), 1):
                if idx < len(text_id_list):
                    text_id = text_id_list[idx]
                    content = text_dict.get(text_id, "")
                    
                    # Check for risk reversal content
                    risk_keywords = ['risk reversal', 'Risk Reversal', 'zero cost', 'Zero Cost', 'strategy option']
                    if any(kw in content for kw in risk_keywords):
                        found_risk_reversal = True
                        print(f"   ✅ FOUND at rank #{i} (similarity: {sim:.4f})")
                        print(f"      Content: {content[:150]}...")
                        break
                        
            if not found_risk_reversal:
                print(f"   ❌ No Risk Reversal content found in top 10")
                
        return True
        
    def save_data(self):
        """Save the updated complete data structure."""
        print(f"\n💾 Saving updated data structure...")
        
        # Create backup first
        backup_file = self.data_dir / f"complete_data_backup_{self.timestamp}.pkl"
        original_file = self.data_dir / "complete_data.pkl"
        
        if original_file.exists():
            import shutil
            shutil.copy2(original_file, backup_file)
            print(f"   📁 Backup created: {backup_file.name}")
        
        # Save updated data
        with open(original_file, 'wb') as f:
            pickle.dump(self.complete_data, f)
            
        print(f"✅ Updated data saved successfully")
        
        # Print summary
        print(f"\n📊 FINAL SUMMARY:")
        print(f"   • text_dict entries: {len(self.complete_data.get('text_dict', {}))}")
        print(f"   • text_embeddings: {len(self.complete_data.get('text_embeddings', []))}")
        print(f"   • text_id_list: {len(self.complete_data.get('text_id_list', []))}")
        print(f"   • text_index size: {self.complete_data.get('text_index', {}).ntotal if self.complete_data.get('text_index') else 0}")
        
    def run_complete_fix(self):
        """Run the complete text embedding fix process."""
        print("🚀 COMPLETE TEXT EMBEDDING FIX")
        print("="*60)
        
        try:
            # Step 1: Setup
            self.setup_embedding_model()
            
            # Step 2: Load data
            self.load_data()
            
            # Step 3: Rebuild text embeddings
            if not self.rebuild_text_embeddings():
                print("❌ Failed to rebuild text embeddings")
                return False
                
            # Step 4: Rebuild FAISS index
            if not self.rebuild_text_faiss_index():
                print("❌ Failed to rebuild FAISS index")
                return False
                
            # Step 5: Test retrieval
            self.test_risk_reversal_retrieval()
            
            # Step 6: Save data
            self.save_data()
            
            print("\n🎉 COMPLETE TEXT EMBEDDING FIX SUCCESSFUL!")
            print("   • All 56K+ text entries now have embeddings")
            print("   • FAISS index rebuilt for fast similarity search")
            print("   • Risk Reversal content should now be retrievable")
            
            return True
            
        except Exception as e:
            print(f"❌ Error during fix process: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Main execution."""
    print("🔧 Complete Text Embedding Fix for HippoRAG2")
    print("This will rebuild ALL text embeddings and indexes")
    
    fixer = CompleteTextEmbeddingFixer()
    success = fixer.run_complete_fix()
    
    if success:
        print("\n✅ Fix completed successfully!")
        print("   Now test HippoRAG2 retrieval with Risk Reversal queries")
    else:
        print("\n❌ Fix failed!")

if __name__ == "__main__":
    main()