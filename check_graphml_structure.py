#!/usr/bin/env python3
"""
Check GraphML Structure

Analyze our GraphML structure to identify compatibility issues with the official 
create_embeddings_and_index function.
"""

import networkx as nx
from collections import Counter
from pathlib import Path

def analyze_graphml_structure():
    """Analyze our GraphML structure for official compatibility."""
    print("🔍 GRAPHML STRUCTURE ANALYSIS")
    print("="*50)
    
    graphml_path = Path("import/pdf_dataset/kg_graphml/knowledge_graph.graphml")
    
    if not graphml_path.exists():
        print(f"❌ GraphML file not found: {graphml_path}")
        return
    
    print(f"📂 Loading GraphML: {graphml_path}")
    
    try:
        with open(graphml_path, 'rb') as f:
            KG = nx.read_graphml(f)
        
        print(f"✅ Graph loaded: {len(KG.nodes)} nodes, {len(KG.edges)} edges")
        
        # Analyze node types
        print(f"\n📊 NODE TYPE ANALYSIS:")
        node_types = Counter()
        sample_nodes = {}
        
        for node_id in list(KG.nodes)[:10000]:  # Sample first 10k nodes
            node_data = KG.nodes[node_id]
            node_type = node_data.get('type', 'unknown')
            node_types[node_type] += 1
            
            # Store sample nodes for each type
            if node_type not in sample_nodes:
                sample_nodes[node_type] = {
                    'id': node_id,
                    'data': node_data
                }
        
        for node_type, count in node_types.most_common():
            print(f"   {node_type}: {count:,} nodes")
        
        # Check what the official function expects
        print(f"\n🔍 OFFICIAL COMPATIBILITY CHECK:")
        
        # Text nodes (official expects type="passage")
        text_nodes = [node for node in KG.nodes if "passage" in KG.nodes[node].get("type", "")]
        our_text_nodes = [node for node in KG.nodes if KG.nodes[node].get("type") == "text"]
        
        print(f"   Text nodes (type='passage'): {len(text_nodes)} ❌ Expected by official")
        print(f"   Text nodes (type='text'): {len(our_text_nodes)} ✅ Our format")
        
        # Node attributes check
        print(f"\n📋 NODE ATTRIBUTES ANALYSIS:")
        
        for node_type, info in sample_nodes.items():
            print(f"\n   {node_type} node sample:")
            print(f"      Node ID: {info['id']}")
            print(f"      Attributes: {list(info['data'].keys())}")
            
            # Check for required 'id' attribute
            if 'id' in info['data']:
                id_value = str(info['data']['id'])[:100]
                print(f"      ID value: {id_value}...")
            else:
                print(f"      ❌ Missing 'id' attribute!")
        
        # Edge analysis
        print(f"\n🔗 EDGE ANALYSIS:")
        edge_sample = list(KG.edges(data=True))[:5]
        
        for i, (source, target, edge_data) in enumerate(edge_sample):
            print(f"   Edge {i+1}: {source} -> {target}")
            print(f"      Attributes: {list(edge_data.keys())}")
            if 'relation' in edge_data:
                print(f"      Relation: {edge_data['relation']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing GraphML: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_compatibility_fixes():
    """Create a fixed GraphML with proper node types for official compatibility."""
    print(f"\n🔧 CREATING COMPATIBILITY FIXES")
    print("-"*40)
    
    graphml_path = Path("import/pdf_dataset/kg_graphml/knowledge_graph.graphml")
    fixed_path = Path("import/pdf_dataset/kg_graphml/pdf_dataset_graph_fixed.graphml")
    
    try:
        with open(graphml_path, 'rb') as f:
            KG = nx.read_graphml(f)
        
        print("📝 Applying compatibility fixes...")
        
        # Fix 1: Change text nodes from type="text" to type="passage"
        text_node_count = 0
        for node_id in KG.nodes:
            node_data = KG.nodes[node_id]
            if node_data.get('type') == 'text':
                KG.nodes[node_id]['type'] = 'passage'
                text_node_count += 1
        
        print(f"   ✅ Fixed {text_node_count} text nodes: type='text' -> type='passage'")
        
        # Save fixed version
        with open(fixed_path, 'wb') as f:
            nx.write_graphml(KG, f)
        
        print(f"   ✅ Fixed GraphML saved: {fixed_path}")
        
        # Also create the expected filename
        expected_path = Path("import/pdf_dataset/kg_graphml/pdf_dataset_graph.graphml")
        if expected_path.exists():
            expected_path.unlink()
        
        import shutil
        shutil.copy2(fixed_path, expected_path)
        print(f"   ✅ Created expected filename: {expected_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating fixes: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main analysis function."""
    success = analyze_graphml_structure()
    
    if success:
        print(f"\n🎯 KEY FINDINGS:")
        print("- Our GraphML uses type='text' but official expects type='passage'")
        print("- This is why create_embeddings_and_index() fails with empty text list")
        print("- Need to fix node types for compatibility")
        
        create_compatibility_fixes()
    
    return success

if __name__ == "__main__":
    main()