#!/usr/bin/env python3
"""
Fix Text Content Loading - Phase 3.1b

This script fixes the text content loading issue that prevents retrieval
of actual text passages despite having complete embeddings and finding
text candidates.

Issue: Text dictionary has 0 entries, preventing text content access
Solution: Reconstruct text dictionary from available data sources
"""

import os
import sys
import json
import numpy as np
import networkx as nx
from pathlib import Path
from datetime import datetime
from collections import defaultdict

class TextContentLoader:
    """Fixes text content loading for retrieval systems."""
    
    def __init__(self, data_directory="import/pdf_dataset"):
        self.data_directory = Path(data_directory)
        self.vector_dir = self.data_directory / "vector_index"
        
        print("🔧 TEXT CONTENT LOADING FIX - Phase 3.1b")
        print("="*60)
        print(f"Fixing text content access for retrieval systems")
        print(f"Data Directory: {self.data_directory}")
        print()
    
    def analyze_text_content_sources(self):
        """Analyze available text content sources."""
        print("📊 Analyzing Text Content Sources...")
        
        # Load knowledge graph
        graphml_path = self.data_directory / "kg_graphml" / "knowledge_graph.graphml"
        self.KG = nx.read_graphml(str(graphml_path))
        
        # Analyze text nodes in GraphML
        text_nodes_with_content = 0
        text_nodes_without_content = 0
        total_text_length = 0
        
        self.text_content_sources = {}
        
        for node_id, node_data in self.KG.nodes(data=True):
            if node_data.get('type') == 'text':
                original_text = node_data.get('original_text', '')
                if original_text and len(original_text.strip()) > 0:
                    text_nodes_with_content += 1
                    total_text_length += len(original_text)
                    self.text_content_sources[node_id] = original_text
                else:
                    text_nodes_without_content += 1
        
        print(f"   📊 GraphML Text Analysis:")
        print(f"      Text nodes with content: {text_nodes_with_content:,}")
        print(f"      Text nodes without content: {text_nodes_without_content:,}")
        print(f"      Total text length: {total_text_length:,} characters")
        print(f"      Average text length: {total_text_length/text_nodes_with_content:.0f} chars per node")
        
        # Check existing text dictionary
        text_dict_path = self.vector_dir / "text_dict.json"
        existing_text_dict = {}
        
        if text_dict_path.exists():
            try:
                with open(text_dict_path, 'r') as f:
                    existing_text_dict = json.load(f)
                print(f"\n   📊 Existing Text Dictionary:")
                print(f"      Entries: {len(existing_text_dict):,}")
            except Exception as e:
                print(f"   ⚠️  Error loading existing text dictionary: {e}")
        else:
            print(f"\n   ⚠️  No existing text dictionary found")
        
        # Check CSV source files for text content
        csv_text_sources = self.check_csv_text_sources()
        
        return text_nodes_with_content > 0
    
    def check_csv_text_sources(self):
        """Check CSV files for additional text content sources."""
        print(f"\n   🔍 Checking CSV Text Sources...")
        
        csv_files = [
            'text_nodes__from_json.csv',
            'text_edges__from_json.csv'
        ]
        
        csv_sources = {}
        
        for csv_file in csv_files:
            csv_path = self.data_directory / csv_file
            if csv_path.exists():
                try:
                    import pandas as pd
                    df = pd.read_csv(csv_path)
                    print(f"      ✅ {csv_file}: {len(df):,} rows")
                    
                    # Check for text content columns
                    text_columns = [col for col in df.columns if 'text' in col.lower() or 'content' in col.lower()]
                    if text_columns:
                        print(f"         Text columns: {text_columns}")
                        csv_sources[csv_file] = {
                            'dataframe': df,
                            'text_columns': text_columns
                        }
                    
                except Exception as e:
                    print(f"      ❌ Error loading {csv_file}: {e}")
            else:
                print(f"      ⚠️  {csv_file}: Not found")
        
        return csv_sources
    
    def reconstruct_text_dictionary(self):
        """Reconstruct the text dictionary from available sources."""
        print("\n🔄 Reconstructing Text Dictionary...")
        
        reconstructed_dict = {}
        
        # Primary source: GraphML text nodes
        graphml_entries = 0
        for node_id, text_content in self.text_content_sources.items():
            if text_content.strip():
                reconstructed_dict[node_id] = text_content.strip()
                graphml_entries += 1
        
        print(f"   ✅ GraphML source: {graphml_entries:,} text entries added")
        
        # TODO: Add other sources if available (CSV files, JSON files, etc.)
        # For now, GraphML should be the primary and most reliable source
        
        if reconstructed_dict:
            # Verify quality of reconstructed dictionary
            total_chars = sum(len(text) for text in reconstructed_dict.values())
            avg_length = total_chars / len(reconstructed_dict)
            
            print(f"   📊 Reconstructed Dictionary Quality:")
            print(f"      Total entries: {len(reconstructed_dict):,}")
            print(f"      Total characters: {total_chars:,}")
            print(f"      Average length: {avg_length:.0f} chars per entry")
            
            # Sample a few entries to verify content quality
            sample_entries = list(reconstructed_dict.items())[:3]
            print(f"   📝 Sample entries:")
            for node_id, content in sample_entries:
                preview = content[:100] + "..." if len(content) > 100 else content
                print(f"      {node_id}: {preview}")
            
            self.reconstructed_text_dict = reconstructed_dict
            return True
        else:
            print(f"   ❌ No text content found to reconstruct dictionary")
            return False
    
    def save_reconstructed_text_dictionary(self):
        """Save the reconstructed text dictionary."""
        print("\n💾 Saving Reconstructed Text Dictionary...")
        
        if not hasattr(self, 'reconstructed_text_dict'):
            print("❌ No reconstructed dictionary to save")
            return False
        
        try:
            # Save as JSON
            output_path = self.vector_dir / "text_dict_RECONSTRUCTED.json"
            with open(output_path, 'w') as f:
                json.dump(self.reconstructed_text_dict, f, indent=2)
            
            file_size_mb = output_path.stat().st_size / (1024*1024)
            print(f"   ✅ Reconstructed dictionary saved: {output_path}")
            print(f"   📊 File size: {file_size_mb:.1f} MB")
            print(f"   📊 Entries: {len(self.reconstructed_text_dict):,}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error saving reconstructed dictionary: {e}")
            return False
    
    def create_backup_and_replace(self):
        """Backup original text dictionary and replace with reconstructed version."""
        print("\n🔄 Creating Backup and Replacing Text Dictionary...")
        
        original_path = self.vector_dir / "text_dict.json"
        backup_path = self.vector_dir / "text_dict_BACKUP.json"
        reconstructed_path = self.vector_dir / "text_dict_RECONSTRUCTED.json"
        
        try:
            # Backup original if it exists
            if original_path.exists():
                os.rename(original_path, backup_path)
                print(f"   ✅ Original dictionary backed up: {backup_path}")
            else:
                print(f"   ℹ️  No original dictionary to backup")
            
            # Replace with reconstructed version
            if reconstructed_path.exists():
                os.rename(reconstructed_path, original_path)
                print(f"   ✅ Reconstructed dictionary now active: {original_path}")
                
                # Verify the replacement
                with open(original_path, 'r') as f:
                    active_dict = json.load(f)
                
                print(f"   ✅ Verification: {len(active_dict):,} text entries now accessible")
                
                if len(active_dict) > 1000:
                    print(f"   🎉 SUCCESS: Text content access restored!")
                    return True
                else:
                    print(f"   ⚠️  Low entry count: {len(active_dict):,}")
                    return False
            else:
                print(f"❌ Reconstructed dictionary file not found: {reconstructed_path}")
                return False
                
        except Exception as e:
            print(f"❌ Error during backup/replace: {e}")
            # Try to restore backup if something went wrong
            if backup_path.exists() and not original_path.exists():
                os.rename(backup_path, original_path)
                print(f"   🔄 Restored original dictionary from backup")
            return False
    
    def test_text_content_access(self):
        """Test that text content can now be accessed properly."""
        print("\n🧪 Testing Text Content Access...")
        
        try:
            # Load the active text dictionary
            text_dict_path = self.vector_dir / "text_dict.json"
            
            if not text_dict_path.exists():
                print(f"❌ Active text dictionary not found: {text_dict_path}")
                return False
            
            with open(text_dict_path, 'r') as f:
                text_dict = json.load(f)
            
            print(f"   ✅ Text dictionary loaded: {len(text_dict):,} entries")
            
            # Test access to a few random entries
            import random
            
            if len(text_dict) > 0:
                sample_size = min(5, len(text_dict))
                sample_keys = random.sample(list(text_dict.keys()), sample_size)
                
                print(f"   🧪 Testing {sample_size} random entries:")
                
                accessible_count = 0
                for key in sample_keys:
                    content = text_dict.get(key, '')
                    if content and len(content.strip()) > 0:
                        accessible_count += 1
                        preview = content[:80] + "..." if len(content) > 80 else content
                        print(f"      ✅ {key}: {preview}")
                    else:
                        print(f"      ❌ {key}: Empty or missing content")
                
                success_rate = (accessible_count / sample_size) * 100
                print(f"   📊 Content accessibility: {accessible_count}/{sample_size} ({success_rate:.1f}%)")
                
                if success_rate >= 80:
                    print(f"   🎉 SUCCESS: Text content access is working!")
                    return True
                else:
                    print(f"   ⚠️  Content access issues detected")
                    return False
            else:
                print(f"   ❌ Empty text dictionary")
                return False
                
        except Exception as e:
            print(f"❌ Error testing text content access: {e}")
            return False
    
    def run_complete_fix(self):
        """Run the complete text content loading fix."""
        print("🚀 Starting Text Content Loading Fix...")
        print("   This enables retrieval of actual text passages")
        print()
        
        steps = [
            ("Analyze Text Content Sources", self.analyze_text_content_sources),
            ("Reconstruct Text Dictionary", self.reconstruct_text_dictionary),
            ("Save Reconstructed Dictionary", self.save_reconstructed_text_dictionary),
            ("Create Backup and Replace", self.create_backup_and_replace),
            ("Test Text Content Access", self.test_text_content_access)
        ]
        
        for step_name, step_func in steps:
            print(f"📋 {step_name}...")
            if not step_func():
                print(f"❌ {step_name} failed. Stopping fix.")
                return False
        
        print("\n✅ TEXT CONTENT LOADING FIX COMPLETE")
        print("="*60)
        print("🎯 RESULTS:")
        print(f"   • Text dictionary reconstructed and active")
        print(f"   • Text content access restored")
        print(f"   • Retrieval systems can now access text passages")
        print(f"   🎉 Combined with 100% embeddings: Complete retrieval pipeline!")
        
        print(f"\n📋 Next: Re-test original HippoRAG2 with both fixes")
        
        return True

def main():
    """Main fix function."""
    loader = TextContentLoader()
    return loader.run_complete_fix()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)