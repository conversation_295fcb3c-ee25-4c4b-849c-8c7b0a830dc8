# HippoRAG2 Complete Workflow Analysis

## Executive Summary

HippoRAG2 is a neurobiologically-inspired RAG framework that mimics human long-term memory through Knowledge Graphs and Personalized PageRank. This document provides a comprehensive analysis of its workflow, data structures, and retrieval mechanisms.

## Table of Contents
1. [Core Architecture](#core-architecture)
2. [Data Structures](#data-structures)
3. [Retrieval Workflow](#retrieval-workflow)
4. [PageRank Algorithm](#pagerank-algorithm)
5. [Multi-Hop Reasoning](#multi-hop-reasoning)
6. [Why Only 5 Results?](#why-only-5-results)
7. [Optimization Opportunities](#optimization-opportunities)

---

## Core Architecture

### Biological Inspiration
HippoRAG2 mimics the human hippocampus, which is responsible for:
- **Pattern Separation**: Distinguishing between similar memories
- **Pattern Completion**: Retrieving complete memories from partial cues
- **Associative Memory**: Connecting related concepts across documents

### System Components

```mermaid
graph TB
    subgraph "Data Preparation Phase"
        D1[Raw Documents] --> D2[Text Chunking]
        D2 --> D3[Entity/Event Extraction]
        D3 --> D4[Triple Formation]
        D4 --> D5[Knowledge Graph Construction]
        D5 --> D6[Embedding Generation]
    end
    
    subgraph "Data Structures"
        D6 --> DS1[Node Embeddings]
        D6 --> DS2[Edge Embeddings]
        D6 --> DS3[Text Embeddings]
        D5 --> DS4[NetworkX Graph]
        D5 --> DS5[FAISS Index]
    end
    
    subgraph "Retrieval Phase"
        Q[User Query] --> R1[Query Processing]
        R1 --> R2{Retrieval Mode}
        R2 -->|query2node| R3[Entity-Based Search]
        R2 -->|query2edge| R4[Edge-Based Search]
        R2 -->|ner2node| R5[NER-Based Search]
        R3 --> R6[Personalized PageRank]
        R4 --> R6
        R5 --> R6
        R6 --> R7[Top-K Selection]
        R7 --> R8[Answer Generation]
    end
```

---

## Data Structures

### 1. **Node Data** (Entities & Concepts)
```python
node_embeddings: np.array  # Shape: [num_nodes, embedding_dim]
node_list: List[str]       # Entity/concept names
```
- **What**: Entities extracted from text (people, places, concepts, events)
- **Example**: "MMC Market Maker Cockpit", "Risk Reversal", "OCO order"
- **Embedding**: 768-dimensional vectors (all-mpnet-base-v2)

### 2. **Edge Data** (Relationships)
```python
edge_embeddings: np.array   # Shape: [num_edges, embedding_dim]
edge_list: List[Tuple]      # (source_node, target_node) pairs
edge_faiss_index: faiss.Index  # Fast similarity search
```
- **What**: Relationships between entities
- **Example**: ("OCO order", "consists_of", "two orders")
- **Purpose**: Captures semantic connections for multi-hop reasoning

### 3. **Text Data** (Document Passages)
```python
text_embeddings: np.array      # Shape: [num_passages, embedding_dim]
text_dict: Dict[str, str]      # passage_id -> passage_content
text_id_list: List[str]        # List of passage IDs
```
- **What**: Original document chunks (~500 tokens each)
- **Example**: Full paragraphs explaining trading concepts
- **Purpose**: Final retrieval targets containing complete information

### 4. **Knowledge Graph**
```python
KG: nx.Graph  # NetworkX graph structure
# Nodes include both entities AND text passages
# Edges connect entities to entities AND entities to passages
```

---

## Retrieval Workflow

### Phase 1: Query Processing

```mermaid
flowchart LR
    Q[User Query] --> QE[Query Encoding]
    QE --> QT{Query Type}
    QT -->|Entity Query| EE[Entity Embedding<br/>768-dim]
    QT -->|Edge Query| EdE[Edge Embedding<br/>768-dim]
    QT -->|Passage Query| PE[Passage Embedding<br/>768-dim]
```

**Current Implementation**: Uses `query2node` mode (entity-based)

### Phase 2: Initial Retrieval

```python
def query2node(self, query, topN=10):
    # 1. Encode query as entity embedding
    query_emb = self.sentence_encoder.encode([query], query_type="entity")
    
    # 2. Compute similarity with all node embeddings
    scores = self.node_embeddings @ query_emb[0].T
    scores = min_max_normalize(scores)
    
    # 3. Get top candidates (3x topN for filtering)
    num_candidates = min(topN * 3, len(scores))
    index_matrix = np.argsort(scores)[-num_candidates:][::-1]
    
    # 4. Apply domain-specific filtering
    # - Boost longer, specific entities (5+ words: 1.2x)
    # - Penalize generic phrases (≤3 words: 0.8x)
    # - Boost domain terms (MMC, Trading, etc.: 1.3x)
    
    return top_entities_with_scores
```

**Key Parameters**:
- `topk_edges`: 30 (number of initial entities to retrieve)
- `weight_adjust`: 0.1 (passage similarity weight)

### Phase 3: Personalized PageRank

```mermaid
graph TD
    subgraph "PageRank Initialization"
        E1[Selected Entities] --> P1[Entity Scores<br/>0.8-1.0]
        T1[Text Passages] --> P2[Passage Scores<br/>0.01-0.1]
        P1 --> P3[Personalization Dict]
        P2 --> P3
    end
    
    subgraph "PageRank Propagation"
        P3 --> PR1[Iteration 1]
        PR1 --> PR2[Iteration 2]
        PR2 --> PR3[...]
        PR3 --> PRN[Iteration N<br/>max=200]
    end
    
    subgraph "Score Propagation Rules"
        N1[Node A<br/>Score: 0.8] -->|0.15 damping| N2[Node B]
        N1 -->|0.85 × weight| N3[Node C]
        N2 --> T2[Text Passage]
        N3 --> T2
    end
```

**PageRank Formula**:
```
PR(node) = (1-α)/N + α × Σ(PR(neighbor) × weight(neighbor→node))
```

Where:
- **α (alpha)**: 0.15 (damping factor - probability of random jump)
- **max_iter**: 200 (maximum iterations)
- **tolerance**: 1e-7 (convergence threshold)

### Phase 4: Final Selection

```python
def retrieve(self, query, topN=5):
    # 1. Get entity scores and passage scores
    node_dict, text_dict = self.retrieve_personalization_dict(query)
    
    # 2. Run Personalized PageRank
    pr = nx.pagerank(
        self.KG, 
        personalization=personalization_dict,
        alpha=0.15,  # Random jump probability
        max_iter=200
    )
    
    # 3. Extract passage scores from PageRank results
    text_scores = {node: pr[node] for node in text_id_list if pr[node] > 0}
    
    # 4. Sort and return top-N passages
    sorted_passages = sorted(text_scores.items(), key=lambda x: x[1], reverse=True)
    return sorted_passages[:topN]
```

---

## PageRank Algorithm Details

### How PageRank Selects Nodes

1. **Initialization**:
   - Query-relevant entities get high initial scores (0.8-1.0)
   - All passages get low initial scores (0.01-0.1)

2. **Iterative Propagation**:
   ```
   For each iteration:
     For each node in graph:
       new_score = (1-α) × uniform_prob + α × Σ(neighbor_scores × edge_weights)
   ```

3. **Multi-Hop Reasoning**:
   - **1st hop**: Direct entity matches get high scores
   - **2nd hop**: Entities connected to matched entities receive score
   - **3rd hop**: Passages connected to 2nd-hop entities accumulate score
   - **Result**: Passages with multiple connection paths score highest

### Example Multi-Hop Path

```mermaid
graph LR
    Q[Query: OCO order placement] --> E1[OCO order<br/>Score: 0.9]
    E1 --> E2[One-Cancels-Other<br/>Score: 0.7]
    E1 --> E3[Bridge Platform<br/>Score: 0.6]
    E2 --> P1[Passage about OCO<br/>Final: 0.8]
    E3 --> P1
    E3 --> P2[Passage about Bridge<br/>Final: 0.5]
```

---

## Multi-Hop Reasoning

### How Multi-Hop Works

HippoRAG2 enables multi-hop reasoning through:

1. **Graph Connectivity**: Entities are connected through extracted relationships
2. **Score Propagation**: PageRank spreads relevance across multiple hops
3. **Path Aggregation**: Passages reachable through multiple paths score higher

### Example Scenario

**Query**: "What county is Erik Hort's birthplace a part of?"

```mermaid
graph TD
    Q[Query] --> H1[Hop 1: Erik Hort]
    H1 --> H2[Hop 2: Montebello<br/>birthplace]
    H2 --> H3[Hop 3: Rockland County<br/>part of]
    H3 --> A[Answer: Rockland County]
    
    style H1 fill:#f9f,stroke:#333
    style H2 fill:#bbf,stroke:#333
    style H3 fill:#bfb,stroke:#333
```

**Retrieved Passages**:
1. "Erik Hort's birthplace is Montebello" (via direct entity match)
2. "Montebello is a part of Rockland County" (via 2-hop connection)

---

## Why Only 5 Results?

### Rationale for topN=5

1. **Cognitive Load**: Research shows humans can effectively process 5-7 items in working memory

2. **Relevance Concentration**: PageRank naturally concentrates scores
   ```
   Typical distribution:
   - Rank 1: 0.0234 (100%)
   - Rank 2: 0.0156 (67%)
   - Rank 3: 0.0098 (42%)
   - Rank 4: 0.0067 (29%)
   - Rank 5: 0.0045 (19%)
   - Rank 6: 0.0023 (10%)  <- Sharp drop-off
   ```

3. **LLM Context Efficiency**:
   - Each passage ≈ 500 tokens
   - 5 passages ≈ 2,500 tokens
   - Leaves room for system prompt and answer generation

4. **Quality vs Quantity Trade-off**:
   - More passages → More noise
   - Fewer passages → Risk missing information
   - 5 passages = Optimal balance

### Configuration Options

```python
# You can adjust these parameters:
inference_config = InferenceConfig(
    topk=10,           # Increase for more results
    topk_edges=50,     # More initial entities
    topk_nodes=20,     # More node candidates
    ppr_alpha=0.10,    # Lower = more exploration
    ppr_max_iter=500,  # More iterations for convergence
)
```

---

## Optimization Opportunities

### 1. **Dynamic topN Selection**
```python
def adaptive_topn(scores):
    """Select topN based on score distribution"""
    if len(scores) < 2:
        return len(scores)
    
    # Find elbow point in score curve
    gradients = np.gradient(sorted(scores, reverse=True))
    elbow = np.argmax(np.abs(gradients)) + 1
    
    return min(max(3, elbow), 10)  # Between 3-10 results
```

### 2. **Query-Specific Alpha**
```python
def get_adaptive_alpha(query_complexity):
    """Adjust damping based on query complexity"""
    if "multi-part" in query or "?" in query:
        return 0.10  # More exploration for complex queries
    else:
        return 0.20  # More focused for simple queries
```

### 3. **Passage Reranking**
```python
def rerank_passages(passages, query, scores):
    """Rerank using both PageRank and semantic similarity"""
    query_emb = encode(query)
    
    for i, passage in enumerate(passages):
        passage_emb = encode(passage)
        semantic_score = cosine_similarity(query_emb, passage_emb)
        
        # Combine PageRank and semantic scores
        final_score = 0.7 * scores[i] + 0.3 * semantic_score
        
    return sorted_by_final_score
```

### 4. **Caching Strategy**
```python
class HippoRAGCache:
    def __init__(self):
        self.entity_cache = {}  # Query → Entity scores
        self.pagerank_cache = {}  # Entity set → PageRank scores
        
    def get_or_compute(self, query, compute_fn):
        cache_key = hash(query)
        if cache_key in self.entity_cache:
            return self.entity_cache[cache_key]
        
        result = compute_fn(query)
        self.entity_cache[cache_key] = result
        return result
```

---

## Performance Metrics

### Current System Performance

| Metric | Value | Notes |
|--------|-------|--------|
| Entity Retrieval | ~50ms | Via embedding similarity |
| PageRank Computation | ~200ms | 200 iterations, 60K+ nodes |
| LLM Filtering | ~500ms | Optional, can be disabled |
| Total Retrieval | ~750ms | End-to-end |
| Memory Usage | ~2GB | Embeddings + Graph |

### Scaling Considerations

- **10K documents**: Current approach works well
- **100K documents**: Consider FAISS indexing for all embeddings
- **1M+ documents**: Need distributed PageRank, graph partitioning

---

## Conclusion

HippoRAG2's strength lies in its ability to perform multi-hop reasoning through:
1. **Rich Knowledge Graph** representation
2. **Personalized PageRank** for relevance propagation
3. **Multiple retrieval modes** for flexibility
4. **Domain-specific optimizations** for better results

The limit of 5 results is a deliberate design choice balancing:
- Computational efficiency
- LLM context limitations
- Human cognitive capacity
- Result quality over quantity

For optimal results, tune:
- `topk_edges`: More initial entities (30-50)
- `ppr_alpha`: Lower for more exploration (0.10-0.15)
- `weight_adjust`: Balance entity vs text similarity (0.05-0.20)
- Response mode: Use "detailed" or "comprehensive" for better answers