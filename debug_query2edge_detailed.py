#!/usr/bin/env python3
"""
Detailed Debug of HippoRAG2 query2edge Method

This script creates a modified version of the query2edge method with extensive logging
to identify exactly where the LLM filtering is failing in the real HippoRAG2 pipeline.
"""

import os
import sys
import json
import pickle
import numpy as np
import networkx as nx
import warnings
import json_repair
from pathlib import Path

# Set environment variable to avoid tokenizer warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Add the project root to Python path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from atlas_rag.retriever.hipporag2 import min_max_normalize
from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
from atlas_rag.llm_generator import LLMGenerator
from openai import OpenAI
from sentence_transformers import SentenceTransformer

def setup_debug_environment():
    """Setup the same environment as HippoRAG2 for debugging."""
    print("🔧 Setting up debug environment...")
    
    # Load complete data (same as HippoRAG2)
    data_file = Path("import/pdf_dataset/complete_data.pkl")
    if not data_file.exists():
        print(f"❌ Complete data file not found: {data_file}")
        return None
    
    try:
        with open(data_file, 'rb') as f:
            data = pickle.load(f)
        print(f"✅ Complete data loaded")
        print(f"   📊 Graph: {len(data['KG'].nodes)} nodes, {len(data['KG'].edges)} edges")
    except Exception as e:
        print(f"❌ Error loading complete data: {e}")
        return None
    
    # Setup Ollama LLM Generator (same as HippoRAG2)
    try:
        ollama_url = "http://localhost:11434"
        model_name = "qwen3:30b-a3b-instruct-2507-q4_K_M"
        
        client = OpenAI(
            base_url=f"{ollama_url}/v1",
            api_key="dummy-key",
        )
        llm_generator = LLMGenerator(client=client, model_name=model_name)
        print(f"✅ LLM Generator created")
    except Exception as e:
        print(f"❌ Error creating LLM Generator: {e}")
        return None
    
    # Setup Sentence Encoder (same as HippoRAG2)
    try:
        encoder_model_name = 'all-MiniLM-L6-v2'
        sentence_model = SentenceTransformer(encoder_model_name)
        sentence_encoder = SentenceEmbedding(sentence_model)
        print(f"✅ Sentence encoder created")
    except Exception as e:
        print(f"❌ Error creating sentence encoder: {e}")
        return None
    
    return {
        'data': data,
        'llm_generator': llm_generator,
        'sentence_encoder': sentence_encoder
    }

def debug_query2edge(env, query, topN=10):
    """
    Debug version of the query2edge method with extensive logging.
    This replicates the exact logic from HippoRAG2Retriever.query2edge()
    """
    print(f"\n🔍 DEBUG QUERY2EDGE METHOD")
    print("=" * 40)
    print(f"🎯 Query: '{query}'")
    print(f"📊 TopN: {topN}")
    
    # Extract data (same as HippoRAG2)
    edge_embeddings = env['data']['edge_embeddings']
    edge_list = env['data']['edge_list']
    edge_faiss_index = env['data']['edge_faiss_index']
    KG = env['data']['KG']
    sentence_encoder = env['sentence_encoder']
    llm_generator = env['llm_generator']
    
    print(f"📋 Available data:")
    print(f"   - Edge embeddings shape: {edge_embeddings.shape}")
    print(f"   - Edge list length: {len(edge_list)}")
    print(f"   - Graph nodes: {len(KG.nodes)}")
    print(f"   - Graph edges: {len(KG.edges)}")
    
    # Step 1: Query embedding (same as HippoRAG2)
    print(f"\n📍 STEP 1: Creating query embedding...")
    try:
        query_emb = sentence_encoder.encode([query], query_type="edge")
        print(f"✅ Query embedding created: shape {query_emb.shape}")
    except Exception as e:
        print(f"❌ Query embedding failed: {e}")
        return {}
    
    # Step 2: Edge similarity search (same as HippoRAG2)
    print(f"\n📍 STEP 2: Computing edge similarities...")
    try:
        with warnings.catch_warnings():
            warnings.filterwarnings('ignore', category=RuntimeWarning)
            raw_scores = edge_embeddings @ query_emb[0].T
        
        scores = min_max_normalize(raw_scores)
        index_matrix = np.argsort(scores)[-topN:][::-1]
        
        print(f"✅ Edge similarity search completed")
        print(f"   - Raw scores shape: {raw_scores.shape}")
        print(f"   - Top {topN} scores: {[f'{scores[i]:.3f}' for i in index_matrix[:5]]}")
        
    except Exception as e:
        print(f"❌ Edge similarity search failed: {e}")
        return {}
    
    # Step 3: Construct candidate triples (same as HippoRAG2)
    print(f"\n📍 STEP 3: Constructing candidate triples...")
    try:
        before_filter_edge_json = {'fact': []}
        similarity_matrix = [scores[i] for i in index_matrix]
        
        for index, sim_score in zip(index_matrix, similarity_matrix):
            edge = edge_list[index]
            edge_str = [KG.nodes[edge[0]]['id'], KG.edges[edge]['relation'], KG.nodes[edge[1]]['id']]
            before_filter_edge_json['fact'].append(edge_str)
        
        print(f"✅ Candidate triples constructed")
        print(f"   - Number of candidate triples: {len(before_filter_edge_json['fact'])}")
        print(f"   - Sample triples:")
        for i, fact in enumerate(before_filter_edge_json['fact'][:3]):
            print(f"     {i+1}. {fact}")
        
    except Exception as e:
        print(f"❌ Candidate triple construction failed: {e}")
        return {}
    
    # Step 4: LLM Filtering (critical step)
    print(f"\n📍 STEP 4: LLM filtering...")
    try:
        triples_json = json.dumps(before_filter_edge_json, ensure_ascii=False)
        print(f"📤 Sending to LLM: {len(triples_json)} characters")
        print(f"   First 200 chars: {triples_json[:200]}...")
        
        filtered_facts_raw = llm_generator.filter_triples_with_entity_event(query, triples_json)
        print(f"📥 LLM response received: {len(filtered_facts_raw)} characters")
        print(f"   Raw response: {filtered_facts_raw}")
        
        # Parse LLM response
        filtered_facts_dict = json_repair.loads(filtered_facts_raw)
        filtered_facts = filtered_facts_dict['fact']
        
        print(f"✅ LLM filtering successful")
        print(f"   - Filtered facts count: {len(filtered_facts)}")
        print(f"   - Filtered facts: {filtered_facts}")
        
        if len(filtered_facts) == 0:
            print(f"⚠️  LLM returned 0 filtered facts - this would cause fallback!")
            return {}
        
    except Exception as e:
        print(f"❌ LLM filtering failed: {e}")
        print(f"   Exception type: {type(e)}")
        import traceback
        traceback.print_exc()
        print(f"   Using fallback strategy...")
        filtered_facts = before_filter_edge_json['fact'][:5]
    
    # Step 5: FAISS lookup for filtered facts (critical step)
    print(f"\n📍 STEP 5: FAISS lookup for filtered facts...")
    try:
        node_score_dict = {}
        
        for i, edge in enumerate(filtered_facts):
            print(f"   Processing filtered fact {i+1}: {edge}")
            
            edge_str = f'{edge[0]} {edge[1]} {edge[2]}'
            search_emb = sentence_encoder.encode([edge_str], query_type="search")
            D, I = edge_faiss_index.search(search_emb, 1)
            filtered_index = I[0][0]
            
            # Get the edge and original score
            actual_edge = edge_list[filtered_index]
            head, tail = actual_edge[0], actual_edge[1]
            sim_score = scores[filtered_index]
            
            print(f"     - FAISS index: {filtered_index}")
            print(f"     - Actual edge: {actual_edge}")
            print(f"     - Similarity score: {sim_score:.3f}")
            
            # Add to node score dict
            if head not in node_score_dict:
                node_score_dict[head] = [sim_score]
            else:
                node_score_dict[head].append(sim_score)
            if tail not in node_score_dict:
                node_score_dict[tail] = [sim_score]
            else:
                node_score_dict[tail].append(sim_score)
        
        print(f"✅ FAISS lookup completed")
        print(f"   - Node score dict size: {len(node_score_dict)}")
        
    except Exception as e:
        print(f"❌ FAISS lookup failed: {e}")
        import traceback
        traceback.print_exc()
        return {}
    
    # Average the scores and return
    for node in node_score_dict:
        node_score_dict[node] = np.mean(node_score_dict[node])
    
    print(f"✅ Query2Edge method completed successfully!")
    print(f"   - Final node count: {len(node_score_dict)}")
    print(f"   - Sample nodes: {list(node_score_dict.keys())[:3]}")
    
    return node_score_dict

def main():
    """Main debug function."""
    print("🔍 DETAILED DEBUG OF HIPPORAG2 QUERY2EDGE METHOD")
    print("=" * 60)
    print("Investigating exactly where LLM filtering fails in the real pipeline")
    print()
    
    # Setup environment
    env = setup_debug_environment()
    if not env:
        print("❌ Failed to setup debug environment")
        return False
    
    # Test with the same query that's failing
    query = "what is a risk reversal option, and how to create one in Bridge?"
    
    # Run debug query2edge
    result = debug_query2edge(env, query, topN=10)
    
    if result:
        print(f"\n🎉 DEBUG SUCCESSFUL!")
        print(f"✅ Query2edge method should be working")
        print(f"📊 Returned {len(result)} nodes for PageRank")
    else:
        print(f"\n❌ DEBUG FAILED!")
        print(f"Issue identified in query2edge pipeline")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)