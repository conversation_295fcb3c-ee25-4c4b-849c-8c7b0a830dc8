#!/usr/bin/env python3
"""
Check what's in the text_dict to diagnose passage retrieval issue.
"""

import os
import pickle
from pathlib import Path

# Set environment variables
os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'

def check_text_dict():
    """Check the contents of text_dict."""
    
    print("=" * 70)
    print("🔍 Checking text_dict Contents")
    print("=" * 70)
    
    # Check pre-computed text_dict
    precompute_dir = Path("import/pdf_dataset/precompute")
    text_dict_file = precompute_dir / "pdf_dataset_original_text_dict_with_node_id.pkl"
    
    if text_dict_file.exists():
        print(f"\n✅ Found text_dict file: {text_dict_file}")
        
        with open(text_dict_file, 'rb') as f:
            text_dict = pickle.load(f)
        
        print(f"   Total entries: {len(text_dict)}")
        
        # Check first few entries
        print("\n📄 Sample entries:")
        for i, (key, value) in enumerate(list(text_dict.items())[:3]):
            print(f"\n{i+1}. Key: {key[:50]}...")
            print(f"   Value type: {type(value)}")
            if isinstance(value, str):
                preview = value[:200] + "..." if len(value) > 200 else value
                print(f"   Content: {preview}")
            else:
                print(f"   Content: {value}")
        
        # Check for specific terms
        print("\n🔍 Searching for specific content:")
        search_terms = ["resting order", "Supersonic", "OCO", "risk reversal"]
        
        for term in search_terms:
            count = 0
            samples = []
            for key, value in text_dict.items():
                if isinstance(value, str) and term.lower() in value.lower():
                    count += 1
                    if len(samples) < 2:
                        samples.append((key, value))
            
            print(f"\n'{term}': {count} passages")
            if samples:
                for key, value in samples:
                    print(f"  Key: {key[:50]}...")
                    idx = value.lower().index(term.lower())
                    start = max(0, idx - 50)
                    end = min(len(value), idx + len(term) + 50)
                    print(f"  Context: ...{value[start:end]}...")
    
    else:
        print(f"❌ text_dict file not found: {text_dict_file}")
    
    # Now check what's loaded by create_embeddings_and_index
    print("\n" + "=" * 70)
    print("🔍 Checking Loaded Data Structure")
    print("=" * 70)
    
    try:
        from atlas_rag.kg_construction.utils.load_data import create_embeddings_and_index
        from sentence_transformers import SentenceTransformer
        from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
        
        encoder_model_name = "sentence-transformers/all-mpnet-base-v2"
        sentence_model = SentenceTransformer(encoder_model_name)
        sentence_encoder = SentenceEmbedding(sentence_model)
        
        print("\nLoading data via create_embeddings_and_index...")
        data = create_embeddings_and_index(
            sentence_encoder=sentence_encoder,
            model_name=encoder_model_name,
            working_directory="import/pdf_dataset",
            keyword="pdf_dataset",
            include_concept=True,
            include_events=True,
            normalize_embeddings=True
        )
        
        if "text_dict" in data:
            print(f"✅ text_dict loaded: {len(data['text_dict'])} entries")
            
            # Check sample
            for i, (key, value) in enumerate(list(data['text_dict'].items())[:3]):
                print(f"\n{i+1}. Key: {key[:50]}...")
                if isinstance(value, str):
                    preview = value[:200] + "..." if len(value) > 200 else value
                    print(f"   Content: {preview}")
                else:
                    print(f"   Content type: {type(value)}")
                    print(f"   Content: {value}")
        else:
            print("❌ text_dict not found in loaded data")
            print(f"   Available keys: {list(data.keys())}")
            
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function."""
    check_text_dict()

if __name__ == "__main__":
    main()