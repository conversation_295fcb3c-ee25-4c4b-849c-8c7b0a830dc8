#!/usr/bin/env python3
"""
Regenerate text_dict with the fixed create_graph_index.py that uses 'text' field.
"""

import os
import pickle
from pathlib import Path

# Set environment variables
os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'

def regenerate_text_dict():
    """Force regeneration of text_dict."""
    
    print("=" * 70)
    print("🔧 Regenerating text_dict with Fixed Code")
    print("=" * 70)
    
    # Remove the old pkl files to force regeneration
    precompute_dir = Path("import/pdf_dataset/precompute")
    
    files_to_remove = [
        precompute_dir / "pdf_dataset_text_list.pkl",
        precompute_dir / "pdf_dataset_original_text_dict_with_node_id.pkl"
    ]
    
    print("\n📦 Removing old files...")
    for file in files_to_remove:
        if file.exists():
            print(f"   Removing: {file.name}")
            file.unlink()
    
    print("\n🔄 Running create_embeddings_and_index with fixed code...")
    
    from sentence_transformers import SentenceTransformer
    from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
    from atlas_rag.vectorstore.create_graph_index import create_embeddings_and_index
    
    encoder_model_name = "sentence-transformers/all-mpnet-base-v2"
    sentence_model = SentenceTransformer(encoder_model_name)
    sentence_encoder = SentenceEmbedding(sentence_model)
    
    # This will now use the fixed code that reads 'text' field
    data = create_embeddings_and_index(
        sentence_encoder=sentence_encoder,
        model_name=encoder_model_name,
        working_directory="import/pdf_dataset",
        keyword="pdf_dataset",
        include_concept=True,
        include_events=True,
        normalize_embeddings=True,
        text_batch_size=64,
        node_and_edge_batch_size=256
    )
    
    print("\n✅ Regeneration complete!")
    
    # Verify the new text_dict
    print("\n🔍 Verifying new text_dict content...")
    
    if "text_dict" in data:
        text_dict = data["text_dict"]
        print(f"   Total entries: {len(text_dict)}")
        
        # Check for real text
        has_real_text = 0
        for value in text_dict.values():
            if isinstance(value, str) and len(value) > 100 and (" " in value or "." in value):
                has_real_text += 1
        
        print(f"   Entries with real text: {has_real_text}/{len(text_dict)}")
        
        # Search for specific terms
        print("\n   Content verification:")
        terms = ["resting order", "Supersonic", "OCO", "risk reversal", "Market Maker"]
        for term in terms:
            count = sum(1 for v in text_dict.values() 
                       if isinstance(v, str) and term.lower() in v.lower())
            print(f"     '{term}': {count} passages")
        
        # Show sample
        print("\n   Sample entry:")
        for i, (key, value) in enumerate(list(text_dict.items())[:1]):
            print(f"     Key: {key[:50]}...")
            if isinstance(value, str):
                preview = value[:300] + "..." if len(value) > 300 else value
                print(f"     Text: {preview}")
        
        return True
    else:
        print("❌ text_dict not found in data")
        return False

def main():
    """Main function."""
    
    success = regenerate_text_dict()
    
    if success:
        print("\n✅ SUCCESS! Text dict regenerated with actual text content.")
        print("\nYou can now run the Q&A system:")
        print("  python hipporag2_interactive_debug.py")
    else:
        print("\n❌ Failed to regenerate text dict")

if __name__ == "__main__":
    main()