#!/usr/bin/env python3
"""
Targeted fix for Risk Reversal content - add missing Risk Reversal entries to embeddings.
"""

import os
import sys
import pickle
import numpy as np
import faiss
from pathlib import Path
from datetime import datetime

# Set environment and add path
os.environ["TOKENIZERS_PARALLELISM"] = "false"
sys.path.insert(0, str(Path(__file__).parent))

from setup_embedding_model import setup_embedding_model

def main():
    print("🎯 Targeted Fix for Risk Reversal Content")
    print("="*50)
    
    # Setup embedding model
    print("🔧 Setting up embedding model...")
    embedding_model = setup_embedding_model()
    
    # Load data
    print("📊 Loading complete data...")
    data_file = Path("import/pdf_dataset/complete_data.pkl")
    with open(data_file, 'rb') as f:
        complete_data = pickle.load(f)
    
    # Identify Risk Reversal text IDs
    risk_reversal_ids = [
        "cdcd632ca440b3caf201d917d6a8ac2f109905b716b570b0eb557b9e753591ff",  # Table of contents
        "4eb22e1fee9bad1777d1e0d7493e5ae03450eb9402a4a3f08e638051a9dc2b87"   # Figure references
    ]
    
    text_dict = complete_data.get('text_dict', {})
    current_text_id_list = complete_data.get('text_id_list', [])
    current_text_embeddings = complete_data.get('text_embeddings', [])
    
    print(f"📊 Current state:")
    print(f"   • text_dict entries: {len(text_dict)}")
    print(f"   • Current text_id_list: {len(current_text_id_list)}")
    print(f"   • Current text embeddings: {len(current_text_embeddings)}")
    
    # Check what Risk Reversal content we have
    print(f"\n🔍 Risk Reversal content analysis:")
    risk_reversal_texts = []
    for i, text_id in enumerate(risk_reversal_ids, 1):
        if text_id in text_dict:
            content = text_dict[text_id]
            risk_reversal_texts.append((text_id, content))
            print(f"   ✅ ID #{i}: Found ({len(content)} chars)")
            print(f"      Preview: {content[:100]}...")
        else:
            print(f"   ❌ ID #{i}: Not found in text_dict")
    
    if not risk_reversal_texts:
        print("❌ No Risk Reversal content found!")
        return
    
    # Create minimal working text embedding system
    print(f"\n🔧 Creating minimal text embedding system...")
    
    # If no existing text system, create new one with just Risk Reversal content
    if len(current_text_id_list) == 0:
        print("   Creating new text embedding system...")
        new_text_id_list = [text_id for text_id, _ in risk_reversal_texts]
        texts_to_embed = [content for _, content in risk_reversal_texts]
        
        # Generate embeddings
        print("   Generating embeddings...")
        embeddings = embedding_model.encode(texts_to_embed, show_progress_bar=True)
        
        # Create FAISS index
        print("   Creating FAISS index...")
        dimension = embeddings.shape[1]
        index = faiss.IndexFlatIP(dimension)
        
        # Normalize for cosine similarity
        faiss.normalize_L2(embeddings)
        index.add(embeddings.astype('float32'))
        
        # Update data
        complete_data['text_id_list'] = new_text_id_list
        complete_data['text_embeddings'] = embeddings
        complete_data['text_index'] = index
        
        print(f"✅ Created text system with {len(new_text_id_list)} entries")
        
    else:
        # Add to existing system
        print("   Adding to existing text embedding system...")
        
        # Check which Risk Reversal IDs are missing
        missing_ids = [text_id for text_id, _ in risk_reversal_texts if text_id not in current_text_id_list]
        
        if missing_ids:
            print(f"   Adding {len(missing_ids)} missing Risk Reversal entries...")
            
            # Get missing texts
            missing_texts = [text_dict[text_id] for text_id in missing_ids]
            
            # Generate embeddings for missing content
            new_embeddings = embedding_model.encode(missing_texts, show_progress_bar=True)
            
            # Combine with existing
            updated_text_id_list = list(current_text_id_list) + missing_ids
            
            if len(current_text_embeddings) > 0:
                updated_embeddings = np.vstack([current_text_embeddings, new_embeddings])
            else:
                updated_embeddings = new_embeddings
            
            # Rebuild FAISS index
            dimension = updated_embeddings.shape[1]
            index = faiss.IndexFlatIP(dimension)
            faiss.normalize_L2(updated_embeddings)
            index.add(updated_embeddings.astype('float32'))
            
            # Update data
            complete_data['text_id_list'] = updated_text_id_list
            complete_data['text_embeddings'] = updated_embeddings
            complete_data['text_index'] = index
            
            print(f"✅ Updated text system to {len(updated_text_id_list)} entries")
        else:
            print("   All Risk Reversal content already indexed")
    
    # Test retrieval
    print(f"\n🎯 Testing Risk Reversal retrieval...")
    
    text_index = complete_data.get('text_index')
    text_id_list = complete_data.get('text_id_list', [])
    
    test_queries = [
        "Risk Reversal strategy FX option",
        "Figure 24 Pricing risk reversal"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        
        # Generate query embedding
        query_embedding = embedding_model.encode([query])[0].reshape(1, -1)
        faiss.normalize_L2(query_embedding)
        
        # Search
        similarities, indices = text_index.search(query_embedding.astype('float32'), 5)
        
        found_risk_reversal = False
        for i, (sim, idx) in enumerate(zip(similarities[0], indices[0]), 1):
            if idx < len(text_id_list):
                text_id = text_id_list[idx]
                content = text_dict.get(text_id, "")
                
                # Check for risk reversal content
                if any(kw in content.lower() for kw in ['risk reversal', 'zero cost', 'strategy option']):
                    found_risk_reversal = True
                    print(f"   ✅ FOUND at rank #{i} (similarity: {sim:.4f})")
                    print(f"      ID: {text_id[:32]}...")
                    print(f"      Content: {content[:200]}...")
                    break
                    
        if not found_risk_reversal:
            print(f"   ❌ No Risk Reversal content found in top 5")
    
    # Save updated data
    print(f"\n💾 Saving updated data...")
    backup_file = data_file.parent / f"complete_data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
    
    # Create backup
    import shutil
    shutil.copy2(data_file, backup_file)
    print(f"   📁 Backup created: {backup_file.name}")
    
    # Save updated data
    with open(data_file, 'wb') as f:
        pickle.dump(complete_data, f)
    
    print(f"✅ Data saved successfully")
    
    # Final summary
    final_text_id_list = complete_data.get('text_id_list', [])
    final_text_embeddings = complete_data.get('text_embeddings', [])
    final_text_index = complete_data.get('text_index')
    
    print(f"\n📊 FINAL STATE:")
    print(f"   • text_id_list: {len(final_text_id_list)} entries")
    print(f"   • text_embeddings: {len(final_text_embeddings)} embeddings")
    print(f"   • text_index: {final_text_index.ntotal if final_text_index else 0} indexed")
    print(f"   • Risk Reversal content should now be retrievable! 🎉")

if __name__ == "__main__":
    main()