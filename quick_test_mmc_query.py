#!/usr/bin/env python3
"""
Quick Test MMC Query with Official Data Loading

Test MMC query retrieval once the official create_embeddings_and_index completes.
"""

import os
import sys
from pathlib import Path
from datetime import datetime

# Set environment variable to avoid tokenizer warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Add the project root to Python path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_official_data_completion():
    """Check if official data loading has completed."""
    precompute_dir = Path("import/pdf_dataset/precompute")
    
    if not precompute_dir.exists():
        print("❌ Precompute directory not found - official workflow not started")
        return False
    
    # Check for key files that indicate completion
    required_files = [
        "pdf_dataset_text_faiss.index", 
        "pdf_dataset_text_list.pkl",
        "pdf_dataset_original_text_dict_with_node_id.pkl"
    ]
    
    print(f"📂 Checking for official data files...")
    for req_file in required_files:
        file_path = precompute_dir / req_file
        if file_path.exists():
            print(f"   ✅ {req_file}")
        else:
            print(f"   ❌ {req_file} (still computing...)")
            return False
    
    # Check node and edge files
    node_files = list(precompute_dir.glob("*node_embeddings.pkl"))
    edge_files = list(precompute_dir.glob("*edge_embeddings.pkl"))
    
    print(f"   Node embedding files: {len(node_files)}")
    print(f"   Edge embedding files: {len(edge_files)}")
    
    if len(node_files) > 0 and len(edge_files) > 0:
        print(f"✅ Official data loading appears complete!")
        return True
    else:
        print(f"⏳ Still computing embeddings...")
        return False

def quick_mmc_test():
    """Quick test of MMC query using any available data."""
    print(f"\n🧪 QUICK MMC QUERY TEST")
    print("-"*30)
    
    try:
        # Try to load official data first
        if check_official_data_completion():
            print("🎯 Testing with official data workflow...")
            # TODO: Load official data and test
            return test_with_official_data()
        else:
            print("🎯 Testing with our existing custom pipeline...")
            return test_with_custom_pipeline()
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_with_custom_pipeline():
    """Test MMC query with our existing custom pipeline."""
    try:
        from hipporag2_pipeline import HippoRAG2Pipeline
        
        # Initialize pipeline
        pipeline = HippoRAG2Pipeline(data_directory="import/pdf_dataset")
        data = pipeline.load_existing_data()
        pipeline.setup_models()
        pipeline.data = data
        pipeline.initialize_hipporag2()
        
        # Test MMC query
        query = "how to create scenarios in MMC?"
        print(f"Query: '{query}'")
        
        results = pipeline.hipporag2_retriever.retrieve(query, topN=3)
        
        if isinstance(results, tuple) and len(results) == 2:
            contents, passage_ids = results
            print(f"✅ Retrieved {len(contents)} passages")
            
            mmc_relevant = 0
            for i, content in enumerate(contents):
                content_preview = content[:150] + "..." if len(content) > 150 else content
                print(f"\n   Result {i+1}:")
                print(f"   {content_preview}")
                
                # Check MMC relevance
                content_lower = content.lower()
                if any(term in content_lower for term in ['mmc', 'market maker cockpit']):
                    print(f"   ✅ MMC-relevant!")
                    mmc_relevant += 1
                elif 'fix' in content_lower:
                    print(f"   ⚠️  FIX protocol content")
                else:
                    print(f"   ❓ Other content")
            
            print(f"\n📊 MMC relevance: {mmc_relevant}/{len(contents)} passages")
            return mmc_relevant > 0
        else:
            print(f"❌ Unexpected result format")
            return False
            
    except Exception as e:
        print(f"❌ Custom pipeline test failed: {e}")
        return False

def test_with_official_data():
    """Test MMC query using official data loading workflow."""
    try:
        # Initialize models
        from sentence_transformers import SentenceTransformer
        from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
        
        encoder_model_name = 'sentence-transformers/all-MiniLM-L6-v2'
        sentence_model = SentenceTransformer(encoder_model_name)
        sentence_encoder = SentenceEmbedding(sentence_model)
        
        # Use create_embeddings_and_index to load official data
        from atlas_rag.vectorstore import create_embeddings_and_index
        
        print("📊 Loading official data structure...")
        data = create_embeddings_and_index(
            sentence_encoder=sentence_encoder,
            model_name=encoder_model_name,
            working_directory="import/pdf_dataset",
            keyword="pdf_dataset",
            include_concept=True,
            include_events=True,
            normalize_embeddings=True,
            text_batch_size=64,
            node_and_edge_batch_size=256,
        )
        
        print("✅ Official data loaded successfully")
        
        # Initialize LLM generator
        from atlas_rag.llm_generator import LLMGenerator
        from openai import OpenAI
        from configparser import ConfigParser
        
        config = ConfigParser()
        config.read('config.ini')
        
        client = OpenAI(
            base_url="https://api.deepinfra.com/v1/openai",
            api_key=config['settings']['DEEPINFRA_API_KEY'],
        )
        
        llm_generator = LLMGenerator(client=client, model_name="meta-llama/Llama-3.3-70B-Instruct")
        
        # Initialize HippoRAG2 with official data
        from atlas_rag.retriever import HippoRAG2Retriever
        
        hipporag2_retriever = HippoRAG2Retriever(
            llm_generator=llm_generator,
            sentence_encoder=sentence_encoder,
            data=data,
        )
        
        print("✅ HippoRAG2 initialized with official data")
        
        # Test MMC query
        query = "how to create scenarios in MMC?"
        print(f"Query: '{query}'")
        
        results = hipporag2_retriever.retrieve(query, topN=3)
        
        if isinstance(results, tuple) and len(results) == 2:
            contents, passage_ids = results
            print(f"✅ Retrieved {len(contents)} passages with OFFICIAL WORKFLOW")
            
            mmc_relevant = 0
            for i, content in enumerate(contents):
                content_preview = content[:150] + "..." if len(content) > 150 else content
                print(f"\n   Result {i+1} (Official):")
                print(f"   {content_preview}")
                
                # Check MMC relevance
                content_lower = content.lower()
                if any(term in content_lower for term in ['mmc', 'market maker cockpit']):
                    print(f"   🎉 MMC-relevant with OFFICIAL workflow!")
                    mmc_relevant += 1
                elif 'fix' in content_lower:
                    print(f"   ⚠️  FIX protocol content")
                else:
                    print(f"   ❓ Other content")
            
            print(f"\n🎯 Official workflow MMC relevance: {mmc_relevant}/{len(contents)} passages")
            return mmc_relevant > 0
        else:
            print(f"❌ Unexpected result format from official workflow")
            return False
            
    except Exception as e:
        print(f"❌ Official data test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🧪 QUICK MMC QUERY TEST")
    print("="*60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = quick_mmc_test()
    
    if success:
        print(f"\n✅ MMC query test SUCCESSFUL!")
        print("🎯 Found MMC-relevant content in retrieval results")
    else:
        print(f"\n❌ MMC query test showed issues")
        print("💡 May still be processing or need further investigation")
    
    return success

if __name__ == "__main__":
    main()