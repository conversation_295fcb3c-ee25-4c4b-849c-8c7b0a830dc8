#!/usr/bin/env python3
"""
HippoRAG2 Algorithm Analysis - Phase 1.2

This script analyzes the original HippoRAG2 algorithm to understand exactly
where and how it breaks when encountering missing embeddings.

Key Questions:
1. Where does HippoRAG2 expect embeddings to exist?
2. How does it handle missing embeddings (or fail to handle them)?
3. What specific failures occur during retrieval?
4. How do these failures manifest in poor query results?

Based on Phase 1.1 findings: 27,762 missing edge embeddings (17.3% gap)
"""

import os
import sys
import json
import pickle
import numpy as np
import networkx as nx
import faiss
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import traceback

# Set environment variable to avoid tokenizer warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Add the project root to Python path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class HippoRAG2AlgorithmAnalyzer:
    """Analyzer for HippoRAG2 algorithm failure points with missing embeddings."""
    
    def __init__(self, data_directory="import/pdf_dataset"):
        self.data_directory = Path(data_directory)
        self.analysis_report = {
            'timestamp': datetime.now().isoformat(),
            'data_directory': str(self.data_directory),
            'algorithm_failures': {},
            'missing_embedding_impacts': {},
            'failure_points': []
        }
        
        print("🔬 HIPPORAG2 ALGORITHM ANALYSIS - Phase 1.2")
        print("="*60)
        print(f"Analyzing where HippoRAG2 breaks with missing embeddings")
        print(f"Data Directory: {self.data_directory}")
        print(f"Known Issue: 27,762 missing edge embeddings (17.3% gap)")
        print()
    
    def load_hipporag2_components(self):
        """Load all HippoRAG2 components to analyze algorithm dependencies."""
        print("📊 Loading HippoRAG2 Components...")
        
        try:
            # Import HippoRAG2 retriever
            from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
            self.HippoRAG2Retriever = HippoRAG2Retriever
            print("   ✅ HippoRAG2Retriever imported successfully")
            
            # Load knowledge graph
            graphml_path = self.data_directory / "kg_graphml" / "knowledge_graph.graphml"
            self.KG = nx.read_graphml(str(graphml_path))
            print(f"   ✅ Knowledge graph loaded: {len(self.KG.nodes):,} nodes, {len(self.KG.edges):,} edges")
            
            # Load embeddings
            vector_dir = self.data_directory / "vector_index"
            
            # Node embeddings
            node_emb_path = vector_dir / "triple_nodes__from_json_with_emb.npy"
            self.node_embeddings = np.load(node_emb_path)
            print(f"   ✅ Node embeddings loaded: {self.node_embeddings.shape}")
            
            # Edge embeddings  
            edge_emb_path = vector_dir / "triple_edges__from_json_with_concept_with_emb.npy"
            self.edge_embeddings = np.load(edge_emb_path)
            print(f"   ✅ Edge embeddings loaded: {self.edge_embeddings.shape}")
            
            # Text embeddings
            text_emb_path = vector_dir / "text_nodes__from_json_with_emb.npy"
            self.text_embeddings = np.load(text_emb_path)
            print(f"   ✅ Text embeddings loaded: {self.text_embeddings.shape}")
            
            # Load text dictionary
            text_dict_path = vector_dir / "text_dict.json"
            if text_dict_path.exists():
                with open(text_dict_path, 'r') as f:
                    self.text_dict = json.load(f)
                print(f"   ✅ Text dictionary loaded: {len(self.text_dict):,} entries")
            else:
                self.text_dict = {}
                print("   ⚠️  Text dictionary not found, will extract from GraphML")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading HippoRAG2 components: {e}")
            traceback.print_exc()
            return False
    
    def analyze_embedding_expectations(self):
        """Analyze what embeddings HippoRAG2 expects and where gaps occur."""
        print("\n🎯 Analyzing Embedding Expectations vs Reality...")
        
        # Graph structure analysis
        total_nodes = len(self.KG.nodes)
        total_edges = len(self.KG.edges) 
        available_node_embeddings = len(self.node_embeddings)
        available_edge_embeddings = len(self.edge_embeddings)
        
        print(f"   📊 Graph Structure:")
        print(f"      Total nodes: {total_nodes:,}")
        print(f"      Total edges: {total_edges:,}")
        
        print(f"   📊 Available Embeddings:")
        print(f"      Node embeddings: {available_node_embeddings:,}")
        print(f"      Edge embeddings: {available_edge_embeddings:,}")
        
        print(f"   📊 Coverage Gaps:")
        node_gap = total_nodes - available_node_embeddings
        edge_gap = total_edges - available_edge_embeddings
        print(f"      Missing node embeddings: {node_gap:,}")
        print(f"      Missing edge embeddings: {edge_gap:,} (17.3%)")
        
        # Analyze which edges are missing embeddings
        print(f"   🔍 Analyzing Missing Edge Types...")
        
        # Create mapping of edges to check which ones have embeddings
        edge_list = list(self.KG.edges())
        available_edges = available_edge_embeddings
        
        if edge_gap > 0:
            print(f"      🚨 CRITICAL: {edge_gap:,} edges lack embeddings")
            print(f"      This breaks HippoRAG2's assumption of complete embedding coverage")
            
            # Analyze edge types that might be missing
            edge_types = defaultdict(int)
            text_edges = 0
            
            for src, dst in edge_list:
                src_type = self.KG.nodes[src].get('type', 'unknown')
                dst_type = self.KG.nodes[dst].get('type', 'unknown')
                
                edge_key = f"{src_type} -> {dst_type}"
                edge_types[edge_key] += 1
                
                if src_type == 'text' or dst_type == 'text':
                    text_edges += 1
            
            print(f"      📊 Edge type distribution:")
            for edge_type, count in sorted(edge_types.items(), key=lambda x: x[1], reverse=True):
                print(f"         {edge_type}: {count:,}")
            
            self.analysis_report['missing_embedding_impacts']['edge_gap'] = {
                'missing_count': edge_gap,
                'missing_percentage': (edge_gap / total_edges) * 100,
                'edge_types': dict(edge_types),
                'text_edges': text_edges
            }
        
        return True
    
    def simulate_hipporag2_retrieval_failures(self):
        """Simulate HippoRAG2 retrieval with missing embeddings to identify failure points."""
        print("\n🧪 Simulating HippoRAG2 Retrieval Failures...")
        
        test_queries = [
            "how to create scenarios in MMC?",
            "what is cross currency trading?", 
            "FIX protocol implementation",
            "market maker cockpit setup"
        ]
        
        failure_analysis = {}
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n   {i}. Testing Query: '{query}'")
            
            try:
                # Try to simulate the HippoRAG2 retrieval process
                query_failures = self.simulate_retrieval_process(query)
                failure_analysis[query] = query_failures
                
                if query_failures['failed']:
                    print(f"      ❌ Retrieval failed: {query_failures['failure_reason']}")
                else:
                    print(f"      ✅ Retrieval completed with {query_failures['result_count']} results")
                    
            except Exception as e:
                print(f"      💥 Exception during simulation: {e}")
                failure_analysis[query] = {
                    'failed': True,
                    'failure_reason': f"Exception: {str(e)}",
                    'failure_stage': 'unknown'
                }
        
        self.analysis_report['algorithm_failures']['retrieval_simulation'] = failure_analysis
        return True
    
    def simulate_retrieval_process(self, query):
        """Simulate the HippoRAG2 retrieval process step by step."""
        failure_info = {
            'failed': False,
            'failure_reason': None,
            'failure_stage': None,
            'result_count': 0
        }
        
        try:
            # Stage 1: Query to entity mapping
            print(f"      🔍 Stage 1: Query to entity mapping...")
            
            # This is where HippoRAG2 would typically do similarity search on entity embeddings
            # With missing embeddings, this stage might fail or return poor results
            
            # Check if we can find relevant entities (simulate entity search)
            query_terms = query.lower().split()
            relevant_entities = []
            
            # Look for entities matching query terms
            for node_id, node_data in self.KG.nodes(data=True):
                if node_data.get('type') in ['entity', 'event']:
                    node_name = node_data.get('name', '').lower()
                    if any(term in node_name for term in query_terms):
                        relevant_entities.append(node_id)
            
            print(f"         Found {len(relevant_entities)} potentially relevant entities")
            
            if len(relevant_entities) == 0:
                failure_info.update({
                    'failed': True,
                    'failure_reason': 'No relevant entities found',
                    'failure_stage': 'entity_mapping'
                })
                return failure_info
            
            # Stage 2: Edge traversal and PageRank
            print(f"      🔍 Stage 2: Edge traversal and PageRank...")
            
            # This is where missing edge embeddings would cause problems
            # HippoRAG2 expects to traverse edges with embeddings for scoring
            connected_text_nodes = set()
            
            for entity_id in relevant_entities[:10]:  # Limit to top 10
                try:
                    # Find connected text nodes
                    for neighbor in self.KG.neighbors(entity_id):
                        if self.KG.nodes[neighbor].get('type') == 'text':
                            connected_text_nodes.add(neighbor)
                except:
                    pass
            
            print(f"         Found {len(connected_text_nodes)} connected text nodes")
            
            if len(connected_text_nodes) == 0:
                failure_info.update({
                    'failed': True,
                    'failure_reason': 'No connected text nodes found',
                    'failure_stage': 'edge_traversal'
                })
                return failure_info
            
            # Stage 3: Text ranking and retrieval
            print(f"      🔍 Stage 3: Text ranking and retrieval...")
            
            # This is where missing text embeddings or incomplete coverage hurts
            retrieved_texts = []
            
            for text_node_id in list(connected_text_nodes)[:5]:  # Top 5
                # Get text content
                text_content = self.KG.nodes[text_node_id].get('original_text', '')
                if not text_content and text_node_id in self.text_dict:
                    text_content = self.text_dict[text_node_id]
                
                if text_content:
                    retrieved_texts.append(text_content)
            
            print(f"         Retrieved {len(retrieved_texts)} text passages")
            
            failure_info['result_count'] = len(retrieved_texts)
            
            if len(retrieved_texts) == 0:
                failure_info.update({
                    'failed': True,
                    'failure_reason': 'No text content retrieved',
                    'failure_stage': 'text_retrieval'
                })
            
            return failure_info
            
        except Exception as e:
            failure_info.update({
                'failed': True,
                'failure_reason': f"Simulation exception: {str(e)}",
                'failure_stage': 'simulation_error'
            })
            return failure_info
    
    def identify_algorithm_weak_points(self):
        """Identify specific points where HippoRAG2 is vulnerable to missing embeddings."""
        print("\n💡 Identifying Algorithm Weak Points...")
        
        weak_points = [
            {
                'component': 'Entity Similarity Search',
                'vulnerability': 'Missing node embeddings prevent entity discovery',
                'impact': 'Query-entity mapping fails, no starting points for retrieval',
                'severity': 'Critical'
            },
            {
                'component': 'Edge Traversal & Scoring',
                'vulnerability': '27,762 missing edge embeddings (17.3% gap)',
                'impact': 'Incomplete graph traversal, missing entity-text connections',
                'severity': 'High'
            },
            {
                'component': 'Personalized PageRank',  
                'vulnerability': 'Expects complete edge weights from embeddings',
                'impact': 'Biased ranking toward nodes with embedding coverage',
                'severity': 'High'
            },
            {
                'component': 'Text Passage Ranking',
                'vulnerability': 'Missing text embeddings affect final ranking',
                'impact': 'Poor relevance scoring, wrong passages returned',
                'severity': 'Medium'
            },
            {
                'component': 'LLM Filtering',
                'vulnerability': 'Poor candidates from previous stages',
                'impact': 'LLM filter rejects all candidates, returns 0 results',
                'severity': 'High'
            }
        ]
        
        print("   🎯 ALGORITHM WEAK POINTS IDENTIFIED:")
        for i, weak_point in enumerate(weak_points, 1):
            print(f"      {i}. {weak_point['component']} ({weak_point['severity']})")
            print(f"         Vulnerability: {weak_point['vulnerability']}")
            print(f"         Impact: {weak_point['impact']}")
            print()
        
        self.analysis_report['algorithm_failures']['weak_points'] = weak_points
        return True
    
    def generate_fix_recommendations(self):
        """Generate specific recommendations to fix the identified algorithm issues."""
        print("💡 Generating Fix Recommendations...")
        
        recommendations = [
            {
                'priority': 'Critical',
                'fix': 'Complete Embedding Generation',
                'description': 'Generate embeddings for all 27,762 missing edges',
                'implementation': 'Fix embedding pipeline batch processing and memory limits'
            },
            {
                'priority': 'High', 
                'fix': 'Algorithm Robustness',
                'description': 'Make HippoRAG2 handle missing embeddings gracefully',
                'implementation': 'Add fallback mechanisms: keyword matching, graph connectivity'
            },
            {
                'priority': 'High',
                'fix': 'Edge Weight Fallbacks',
                'description': 'Use graph topology when edge embeddings missing',
                'implementation': 'Implement distance-based or connectivity-based edge weights'
            },
            {
                'priority': 'Medium',
                'fix': 'Multi-Strategy Retrieval',
                'description': 'Combine embedding similarity with graph traversal',
                'implementation': 'Hybrid approach: embedding + structural + keyword matching'
            },
            {
                'priority': 'Medium',
                'fix': 'Enhanced Error Handling',
                'description': 'Provide meaningful errors instead of silent failures',
                'implementation': 'Add logging and diagnostics for missing embedding cases'
            }
        ]
        
        print("   💡 FIX RECOMMENDATIONS:")
        for i, rec in enumerate(recommendations, 1):
            print(f"      {i}. {rec['fix']} ({rec['priority']})")
            print(f"         Problem: {rec['description']}")
            print(f"         Solution: {rec['implementation']}")
            print()
        
        self.analysis_report['algorithm_failures']['recommendations'] = recommendations
        return True
    
    def save_analysis_report(self):
        """Save the complete algorithm analysis report."""
        report_path = self.data_directory.parent / "hipporag2_algorithm_analysis_report.json"
        
        try:
            with open(report_path, 'w') as f:
                json.dump(self.analysis_report, f, indent=2, default=str)
            
            print(f"\n📄 Analysis Report Saved: {report_path}")
            return True
            
        except Exception as e:
            print(f"❌ Error saving analysis report: {e}")
            return False
    
    def run_complete_analysis(self):
        """Run the complete HippoRAG2 algorithm analysis."""
        print("🚀 Starting Complete HippoRAG2 Algorithm Analysis...")
        print("   Investigating where algorithm breaks with missing embeddings")
        print()
        
        steps = [
            ("Load HippoRAG2 Components", self.load_hipporag2_components),
            ("Analyze Embedding Expectations", self.analyze_embedding_expectations),
            ("Simulate Retrieval Failures", self.simulate_hipporag2_retrieval_failures),
            ("Identify Algorithm Weak Points", self.identify_algorithm_weak_points),
            ("Generate Fix Recommendations", self.generate_fix_recommendations),
            ("Save Analysis Report", self.save_analysis_report)
        ]
        
        for step_name, step_func in steps:
            print(f"📋 {step_name}...")
            if not step_func():
                print(f"❌ {step_name} failed. Stopping analysis.")
                return False
        
        print("\n✅ HIPPORAG2 ALGORITHM ANALYSIS COMPLETE")
        print("="*60)
        print("🎯 KEY FINDINGS:")
        print("   • 27,762 missing edge embeddings break graph traversal")
        print("   • Algorithm assumes complete embedding coverage")
        print("   • Missing embeddings cause cascading failures in retrieval")
        print("   • LLM filtering fails due to poor candidate quality")
        print("   • Need both: complete embeddings + algorithm robustness")
        
        print(f"\n📄 Full report: hipporag2_algorithm_analysis_report.json")
        print(f"📋 Next: Phase 2.1 - Fix Embedding Generation Pipeline")
        
        return True

def main():
    """Main analysis function."""
    analyzer = HippoRAG2AlgorithmAnalyzer()
    return analyzer.run_complete_analysis()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)