accelerate==1.8.1
acvl_utils==0.2.5
addict==2.4.0
aiobotocore @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_1cl06d5vjc/croot/aiobotocore_1714464399334/work
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.13
aioitertools @ file:///tmp/build/80754af9/aioitertools_1607109665762/work
aiosignal @ file:///tmp/build/80754af9/aiosignal_1637843061372/work
aiosqlite==0.21.0
alabaster @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_39uesgct45/croot/alabaster_1718201495024/work
altair @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/altair_1699282542592/work
anaconda-anon-usage @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_3eler6mjxh/croot/anaconda-anon-usage_1710965076906/work
anaconda-catalogs @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/anaconda-catalogs_1701813581302/work
anaconda-client @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_4fl23009pr/croot/anaconda-client_1708640644054/work
anaconda-cloud-auth @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_bazegf935a/croot/anaconda-cloud-auth_1713991395391/work
anaconda-navigator @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_dbsedxpmfb/croot/anaconda-navigator_1727709704056/work
anaconda-project @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_edkrilp7t2/croot/anaconda-project_1706049207276/work
annotated-types==0.7.0
anthropic==0.46.0
antlr4-python3-runtime==4.13.2
anyio==4.9.0
appdirs==1.4.4
applaunchservices @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_3ani429u_q/croot/applaunchservices_1710249784568/work
applicationinsights==0.11.10
appnope @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/appnope_1699303798458/work
appscript @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_bev456p1le/croot/appscript_1725388760502/work
archspec @ file:///croot/archspec_1709217642129/work
argcomplete==3.5.3
argon2-cffi @ file:///opt/conda/conda-bld/argon2-cffi_1645000214183/work
argon2-cffi-bindings @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/argon2-cffi-bindings_1699251412744/work
arrow @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/arrow_1699251442701/work
ascii_colors==0.5.2
asgiref==3.8.1
astroid @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/astroid_1699240820293/work
astropy @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_fea_ls8y83/croot/astropy_1726174615584/work
astropy-iers-data @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_8elfwy87fe/croot/astropy-iers-data_1726000556608/work
asttokens @ file:///opt/conda/conda-bld/asttokens_1646925590279/work
async-lru @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/async-lru_1701803623704/work
-e git+https://github.com/HKUST-KnowComp/AutoSchemaKG@e5db59d0aa26e29addd3fea3ad1555a0d572b66f#egg=atlas_rag
atomicwrites==1.4.0
attrs @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/attrs_1699281960663/work
aurelio-sdk==0.0.19
Automat @ file:///tmp/build/80754af9/automat_1600298431173/work
autopep8 @ file:///croot/autopep8_1708962882016/work
av==14.4.0
azure-ai-agents==1.0.2
azure-ai-inference==1.0.0b9
azure-ai-projects==1.0.0b12
azure-appconfiguration==1.7.1
azure-batch==15.0.0b2
azure-cli==2.75.0
azure-cli-core==2.75.0
azure-cli-telemetry==1.1.0
azure-common==1.1.28
azure-core==1.35.0
azure-cosmos==3.2.0
azure-data-tables==12.4.0
azure-datalake-store==1.0.1
azure-identity==1.23.1
azure-keyvault-administration==4.4.0b2
azure-keyvault-certificates==4.7.0
azure-keyvault-keys==4.11.0b1
azure-keyvault-secrets==4.7.0
azure-keyvault-securitydomain==1.0.0b1
azure-mgmt-advisor==9.0.0
azure-mgmt-apimanagement==4.0.0
azure-mgmt-appconfiguration==3.1.0
azure-mgmt-appcontainers==2.0.0
azure-mgmt-applicationinsights==1.0.0
azure-mgmt-authorization==4.0.0
azure-mgmt-batch==17.3.0
azure-mgmt-batchai==7.0.0b1
azure-mgmt-billing==6.0.0
azure-mgmt-botservice==2.0.0
azure-mgmt-cdn==12.0.0
azure-mgmt-cognitiveservices==13.5.0
azure-mgmt-compute==34.1.0
azure-mgmt-containerinstance==10.2.0b1
azure-mgmt-containerregistry==14.1.0b1
azure-mgmt-containerservice==37.0.0
azure-mgmt-core==1.6.0
azure-mgmt-cosmosdb==9.8.0
azure-mgmt-databoxedge==1.0.0
azure-mgmt-datalake-store==1.1.0b1
azure-mgmt-datamigration==10.0.0
azure-mgmt-dns==8.0.0
azure-mgmt-eventgrid==10.2.0b2
azure-mgmt-eventhub==10.1.0
azure-mgmt-extendedlocation==1.0.0b2
azure-mgmt-hdinsight==9.0.0b3
azure-mgmt-imagebuilder==1.3.0
azure-mgmt-iotcentral==10.0.0b2
azure-mgmt-iothub==3.0.0
azure-mgmt-iothubprovisioningservices==1.1.0
azure-mgmt-keyvault==11.0.0
azure-mgmt-loganalytics==13.0.0b4
azure-mgmt-managementgroups==1.0.0
azure-mgmt-maps==2.0.0
azure-mgmt-marketplaceordering==1.1.0
azure-mgmt-media==9.0.0
azure-mgmt-monitor==7.0.0
azure-mgmt-msi==7.0.0
azure-mgmt-mysqlflexibleservers==1.0.0b3
azure-mgmt-netapp==10.1.0
azure-mgmt-policyinsights==1.1.0b4
azure-mgmt-postgresqlflexibleservers==1.1.0b2
azure-mgmt-privatedns==1.0.0
azure-mgmt-rdbms==10.2.0b17
azure-mgmt-recoveryservices==3.1.0
azure-mgmt-recoveryservicesbackup==9.2.0
azure-mgmt-redhatopenshift==1.5.0
azure-mgmt-redis==14.5.0
azure-mgmt-resource==23.3.0
azure-mgmt-search==9.2.0
azure-mgmt-security==6.0.0
azure-mgmt-servicebus==8.2.1
azure-mgmt-servicefabric==2.1.0
azure-mgmt-servicefabricmanagedclusters==2.1.0b1
azure-mgmt-servicelinker==1.2.0b3
azure-mgmt-signalr==2.0.0b2
azure-mgmt-sql==4.0.0b21
azure-mgmt-sqlvirtualmachine==1.0.0b5
azure-mgmt-storage==23.0.0
azure-mgmt-synapse==2.1.0b5
azure-mgmt-trafficmanager==1.0.0
azure-mgmt-web==7.3.1
azure-monitor-query==1.2.0
azure-multiapi-storage==1.4.1
azure-storage-blob==12.26.0
azure-storage-common==1.4.2
azure-synapse-accesscontrol==0.5.0
azure-synapse-artifacts==0.20.0
azure-synapse-managedprivateendpoints==0.4.0
azure-synapse-spark==0.7.0
Babel @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/babel_1699241901875/work
backoff==2.2.1
banks==2.1.2
batchgenerators==0.25.1
batchgeneratorsv2==0.2.3
bcrypt==4.3.0
beautifulsoup4==4.13.4
binaryornot @ file:///tmp/build/80754af9/binaryornot_1617751525010/work
black @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_a1kx8enwxb/croot/black_1725573855432/work
bleach @ file:///opt/conda/conda-bld/bleach_1641577558959/work
blinker==1.9.0
blis==1.3.0
blosc2==3.3.1
bokeh @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_90dvw43ym1/croot/bokeh_1727914490404/work
boltons @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/boltons_1699240838368/work
botocore @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_733ekid_nr/croot/botocore_1714460542833/work
Bottleneck==1.4.2
Brotli @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_27zk0eqdh0/croot/brotli-split_1714483157007/work
build==1.2.2.post1
cachetools==5.5.2
catalogue @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_80m8_830f9/croot/catalogue_1703688152663/work
certifi @ file:///home/<USER>/feedstock_root/build_artifacts/certifi_1739515848642/work/certifi
cffi @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_06ndtibm2c/croot/cffi_1726856446111/work
cfgv==3.4.0
chardet==5.2.0
charset-normalizer==3.4.1
chroma-hnswlib==0.7.6
chromadb==1.0.12
click==8.2.1
cloudpathlib==0.21.0
cloudpickle @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_15c4lneh7c/croot/cloudpickle_1721657359029/work
cobble==0.1.4
colorama @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/colorama_1699282140182/work
colorcet @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_b4xbyn75js/croot/colorcet_1709758362167/work
coloredlogs==15.0.1
colorlog==6.9.0
comm @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_3doui0bmzb/croot/comm_1709322861485/work
conda @ file:///Users/<USER>/miniforge3/conda-bld/conda_1743712328979/work/conda-src
conda-build @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_34v8f_chyc/croot/conda-build_1726839919610/work
conda-content-trust @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_9fusbfzixa/croot/conda-content-trust_1714483157715/work
conda-libmamba-solver @ file:///croot/conda-libmamba-solver_1727775630457/work/src
conda-pack @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_bf7z50aw6o/croot/conda-pack_1710258031692/work
conda-package-handling @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_ef9phnqphe/croot/conda-package-handling_1718138279942/work
conda-repo-cli @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_f19ffcbhgt/croot/conda-repo-cli_1727366914573/work
conda-token @ file:///croot/conda-token_1718995751285/work
conda_index @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_0au56q2_5k/croot/conda-index_1719338215248/work
conda_package_streaming @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_6dgq200203/croot/conda-package-streaming_1718136087190/work
confection==0.1.5
configparser==7.2.0
connected-components-3d==3.23.0
constantly @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_efw7euxpjs/croot/constantly_1703165606144/work
contourpy==1.3.2
cookiecutter @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_38jzqhs2jm/croot/cookiecutter_1711059824217/work
coverage @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_davv13psfi/croot/coverage_1734018337762/work
cryptography==43.0.3
cssselect @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_47oh46v5h0/croot/cssselect_1707339886455/work
cssselect2==0.8.0
csvw==3.5.1
ctranslate2==4.6.0
curated-tokenizers==0.0.9
curated-transformers==0.1.1
cycler==0.12.1
cymem==2.0.11
cytoolz @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_f0etqooaak/croot/cytoolz_1701723613874/work
dashscope==1.24.1
dask @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_aaiijmokbm/croot/dask-core_1725461363220/work
dask-expr @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_e54n8hzk7f/croot/dask-expr_1725523003908/work
dataclasses-json==0.6.7
datamodel-code-generator==0.26.1
datasets==4.0.0
datashader @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_fbalsd7ww9/croot/datashader_1720540173106/work
debugpy @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/debugpy_1699253073094/work
decorator==4.4.2
defusedxml @ file:///tmp/build/80754af9/defusedxml_1615228127516/work
Deprecated==1.2.18
dicom2nifti==2.6.0
diff-match-patch @ file:///Users/<USER>/demo/mc3/conda-bld/diff-match-patch_1630511840874/work
diffusers==0.33.1
dill @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_28zwy_olqk/croot/dill_1715094676263/work
dirtyjson==1.0.8
diskcache==5.6.3
distlib==0.4.0
distributed @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_fflqhqrq0u/croot/distributed_1725523026486/work
distro @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_ddkyz0575y/croot/distro_1714488254309/work
dlinfo==2.0.0
dmglib @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_e8vusks82c/croot/dmglib_1719996269222/work
dnspython==2.7.0
docopt==0.6.2
docstring-to-markdown @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/docstring-to-markdown_1699242114044/work
docutils @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/docutils_1699238275731/work
durationpy==0.9
dynamic_network_architectures==0.3.1
EbookLib==0.18
effdet==0.4.1
einops==0.8.1
email_validator==2.2.0
emoji==2.14.1
en-core-web-sm @ https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.7.1/en_core_web_sm-3.7.1-py3-none-any.whl#sha256=86cc141f63942d4b2c5fcee06630fd6f904788d2f0ab005cce45aadb8fb73889
en_core_web_lg @ https://github.com/explosion/spacy-models/releases/download/en_core_web_lg-3.8.0/en_core_web_lg-3.8.0-py3-none-any.whl#sha256=293e9547a655b25499198ab15a525b05b9407a75f10255e405e8c3854329ab63
espeakng-loader==0.2.4
et_xmlfile==2.0.0
eval_type_backport==0.2.2
executing @ file:///opt/conda/conda-bld/executing_1646925071911/work
fabric==3.2.2
faiss-cpu==1.11.0.post1
Faker==37.1.0
fastapi==0.115.9
faster-whisper==1.1.1
fastjsonschema @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/python-fastjsonschema_1699238535414/work
fft-conv-pytorch==1.2.0
filelock==3.18.0
filetype==1.2.0
flake8 @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_8fyuzhayos/croot/flake8_1708965272166/work
Flask==3.1.0
flatbuffers==25.2.10
fonttools==4.57.0
frozendict @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_8b0cest_id/croot/frozendict_1713194839836/work
frozenlist @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/frozenlist_1699254257028/work
fsspec==2024.12.0
ftfy==6.3.1
future==1.0.0
fvcore==0.1.5.post20221221
gensim @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_5cdoei19at/croot/gensim_1725057952958/work
genson==1.3.0
gitdb @ file:///tmp/build/80754af9/gitdb_1617117951232/work
GitPython @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_ebpnpowk3c/croot/gitpython_1720455037823/work
google-ai-generativelanguage==0.6.15
google-api-core==2.24.2
google-api-python-client==2.174.0
google-auth==2.39.0
google-auth-httplib2==0.2.0
google-cloud-vision==3.10.1
google-genai==1.27.0
google-generativeai==0.8.5
googleapis-common-protos==1.70.0
graphdatascience==1.16
graphiti-core==0.18.0
graphviz==0.20.3
greenlet @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_516imz09pb/croot/greenlet_1702059966336/work
griffe==1.7.3
grpcio==1.72.0rc1
grpcio-status==1.71.2
gTTS==2.5.4
h11 @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_110bmw2coo/croot/h11_1706652289620/work
h5py==3.13.0
HeapDict @ file:///Users/<USER>/demo/mc3/conda-bld/heapdict_1630598515714/work
hnswlib==0.8.0
holoviews @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_d9699cvcyt/croot/holoviews_1720539755521/work
html5lib==1.1
httpcore==1.0.8
httplib2==0.22.0
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.30.2
humanfriendly==10.0
hvplot @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_c6l1nubo_c/croot/hvplot_1727775577392/work
hyperlink @ file:///tmp/build/80754af9/hyperlink_1610130746837/work
identify==2.6.12
idna==3.10
ijson==3.3.0
imagebind @ git+https://github.com/facebookresearch/ImageBind.git@3fcf5c9039de97f6ff5528ee4a9dce903c5979b3
imagecodecs @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/imagecodecs_1699241106295/work
imageio @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_3d_rw6fwwk/croot/imageio_1707247298334/work
imageio-ffmpeg==0.6.0
imagesize @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/imagesize_1699242222508/work
imbalanced-learn @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_2fmriz1nk5/croot/imbalanced-learn_1718132237893/work
importlib-metadata @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_5498c88e7n/croot/importlib_metadata-suite_1704813534254/work
importlib_resources==6.5.2
incremental @ file:///croot/incremental_1708639938299/work
inflect==5.6.2
inflection @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/inflection_1699245330843/work
iniconfig @ file:///home/<USER>/recipes/ci/iniconfig_1610983019677/work
intake @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_9coumc4ufj/croot/intake_1726109564581/work
intervaltree @ file:///Users/<USER>/demo/mc3/conda-bld/intervaltree_1630511889664/work
invoke==2.2.0
iopath==0.1.10
ipykernel @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_f428_5tjvx/croot/ipykernel_1705933835534/work
ipython @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_31k34m3e25/croot/ipython_1726064238879/work
ipython-genutils @ file:///tmp/build/80754af9/ipython_genutils_1606773439826/work
ipywidgets @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_01t3jhj_j9/croot/ipywidgets_1710961498393/work
isodate==0.7.2
isort @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_1bnvhw7_ex/croot/isort_1718291367347/work
itemadapter @ file:///tmp/build/80754af9/itemadapter_1626442940632/work
itemloaders @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_9810zcegev/croot/itemloaders_1708639928835/work
itsdangerous @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_00jdi1gu23/croot/itsdangerous_1716533346702/work
jaraco.classes @ file:///tmp/build/80754af9/jaraco.classes_1620983179379/work
javaproperties==0.5.2
jedi @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_194648shy3/croot/jedi_1721058355221/work
jellyfish @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/jellyfish_1699255065421/work
Jinja2==3.1.6
jiter==0.9.0
jmespath @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/jmespath_1701804490553/work
joblib @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_8ff7uji0v2/croot/joblib_1718217225309/work
jsf==0.11.2
json5 @ file:///tmp/build/80754af9/json5_1624432770122/work
json_repair==0.39.1
jsondiff==2.0.0
jsonpatch @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_3ajyoz8zoj/croot/jsonpatch_1714483362270/work
jsonpointer==2.1
jsonschema @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_7boelfqucq/croot/jsonschema_1728486715888/work
jsonschema-specifications @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/jsonschema-specifications_1701803122948/work
jupyter @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_c2jou6olpk/croot/jupyter_1709837195270/work
jupyter-console @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/jupyter_console_1707340206137/work
jupyter-events @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_db0avcjzq5/croot/jupyter_events_1718738111427/work
jupyter-lsp @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/jupyter-lsp-meta_1707339967035/work
jupyter_client @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/jupyter_client_1701803191601/work
jupyter_core @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_73nomeum4p/croot/jupyter_core_1718818302815/work
jupyter_server @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_d1t69bk94b/croot/jupyter_server_1718827086930/work
jupyter_server_terminals @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/jupyter_server_terminals_1701803399551/work
jupyterlab @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_a2d0br6r6g/croot/jupyterlab_1725895226942/work
jupyterlab-pygments @ file:///tmp/build/80754af9/jupyterlab_pygments_1601490720602/work
jupyterlab-widgets @ file:///tmp/build/80754af9/jupyterlab_widgets_1609884341231/work
jupyterlab_server @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_f64fg3hglz/croot/jupyterlab_server_1725865356410/work
keyring @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_8fybah8hcr/croot/keyring_1709632516643/work
kiwisolver==1.4.8
knack==0.11.0
kokoro==0.9.4
kokoro-onnx==0.4.9
kubernetes==32.0.1
-e git+https://github.com/HumanSignal/label-studio-ml-backend.git@a2b78a2efcb5f06b388a4e8e16a9e0d5b5b3f891#egg=label_studio_ml
label-studio-sdk @ git+https://github.com/HumanSignal/label-studio-sdk.git@e207b82924a1f9a1c0affb7e1fd7d4c9b0f5506b
langchain==0.3.23
langchain-chroma==0.2.4
langchain-community==0.3.21
langchain-core==0.3.65
langchain-experimental==0.3.4
langchain-google-genai==2.0.10
langchain-huggingface==0.3.0
langchain-neo4j==0.4.0
langchain-ollama==0.3.3
langchain-openai==0.3.12
langchain-text-splitters==0.3.8
langcodes==3.5.0
langdetect==1.0.9
langsmith==0.3.45
language-tags==1.2.0
language_data==1.3.0
lazy-object-proxy @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_56nrnaax6j/croot/lazy-object-proxy_1712908715628/work
lazy_loader @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_e4fhv6eyhq/croot/lazy_loader_1718176742928/work
lckr_jupyterlab_variableinspector @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/jupyterlab-variableinspector_1709224907973/work
libarchive-c @ file:///croot/python-libarchive-c_1726069797193/work
libmambapy @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_14wu5sk1ap/croot/mamba-split_1725563369022/work/libmambapy
lightrag-hku==1.3.2
linecache2==1.0.0
linkify-it-py @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/linkify-it-py_1699255587400/work
litellm==1.72.6.post1
llama-cloud==0.1.23
llama-cloud-services==0.6.25
llama-index-core==0.12.39
llama-parse==0.6.25
llvmlite @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_22a8mvi_vz/croot/llvmlite_1720455324093/work
lmdb @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/python-lmdb_1699242995414/work
locket @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/locket_1699239197253/work
loguru==0.7.3
lxml==5.3.2
lz4 @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/lz4_1699258569924/work
mammoth==1.9.1
marisa-trie==1.2.1
Markdown==3.8
markdown-it-py==3.0.0
markdown2==2.5.4
markdownify==1.1.0
marker-pdf==1.8.2
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.1
matplotlib-inline @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/matplotlib-inline_1699242375920/work
mccabe @ file:///opt/conda/conda-bld/mccabe_1644221741721/work
mdit-py-plugins @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/mdit-py-plugins_1699258689091/work
mdurl==0.1.2
menuinst @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_ad321a45td/croot/menuinst_1723567614652/work
microsoft-security-utilities-secret-masker==1.0.0b4
misaki==0.9.4
mistralai==1.6.0
mistune @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/mistune_1699258805653/work
mmh3==5.1.0
monotonic==1.6
more-itertools @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_1di4owdn65/croot/more-itertools_1727185454223/work
motor==3.7.0
moviepy==1.0.3
mpmath @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/mpmath_1699242500508/work
msal==1.33.0b1
msal-extensions==1.2.0
msgpack @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/msgpack-python_1699237897243/work
msrest==0.7.1
multidict @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_10voz9m15i/croot/multidict_1701096890858/work
multimethod==2.0
multipledispatch @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/multipledispatch_1699237920047/work
multiprocess==0.70.16
murmurhash @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_65h_q_dxgn/croot/murmurhash_1741707823315/work
mypy @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_fdoskmy3x7/croot/mypy-split_1725573898279/work
mypy-extensions @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/mypy_extensions_1699241356727/work
nano-vectordb==*******
navigator-updater @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_15xz58jgev/croot/navigator-updater_1718030392983/work
nbclient @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/nbclient_1701803280377/work
nbconvert @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_f4c1s1qk1f/croot/nbconvert_1728049432295/work
nbformat @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_2cv_qoc1gw/croot/nbformat_1728049423516/work
ndindex==1.9.2
neo4j==5.28.1
neo4j-graphrag==1.6.1
nest-asyncio @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_310vb5e2a0/croot/nest-asyncio_1708532678212/work
networkx==3.4.2
nibabel==5.3.2
nltk @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_66jwomcxtp/croot/nltk_1724427705781/work
nnunetv2==2.6.0
nodeenv==1.9.1
notebook @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_539v4hufo2/croot/notebook_1727199149603/work
notebook_shim @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/notebook-shim_1707340024494/work
num2words==0.5.14
numba @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_adaws2bya8/croot/numba_1720607446395/work
numexpr==2.10.2
numpy==1.26.4
numpydoc @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_69xghc3i3n/croot/numpydoc_1718279166747/work
oauthlib==3.2.2
olefile==0.47
ollama==0.5.1
omegaconf==2.3.0
onnx==1.17.0
onnxruntime==1.21.1
openai==1.97.1
opencv-python==4.11.0.86
opencv-python-headless==4.12.0.88
openpyxl @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_96hhik4ygv/croot/openpyxl_1721752931204/work
opentelemetry-api==1.32.0
opentelemetry-exporter-otlp-proto-common==1.32.0
opentelemetry-exporter-otlp-proto-grpc==1.32.0
opentelemetry-instrumentation==0.53b0
opentelemetry-instrumentation-asgi==0.53b0
opentelemetry-instrumentation-fastapi==0.53b0
opentelemetry-proto==1.32.0
opentelemetry-sdk==1.32.0
opentelemetry-semantic-conventions==0.53b0
opentelemetry-util-http==0.53b0
orjson==3.10.16
overrides @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/overrides_1701803470591/work
packaging==24.2
pandas==2.2.3
pandocfilters @ file:///opt/conda/conda-bld/pandocfilters_1643405455980/work
panel @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_68psfg10tp/croot/panel_1728066379997/work
param @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_b966xs8glj/croot/param_1719347934708/work
parameterized==0.9.0
paramiko==3.5.1
parsel @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_da55frlnfu/croot/parsel_1707503460023/work
parso @ file:///opt/conda/conda-bld/parso_1641458642106/work
partd @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/partd_1699241371660/work
pathspec @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/pathspec_1699237228964/work
patsy @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_70o6mid86v/croot/patsy_1718378180175/work
pdf2image==1.17.0
pdfminer.six==20250416
pdftext==0.6.3
pexpect @ file:///tmp/build/80754af9/pexpect_1605563209008/work
phonemizer-fork==3.3.2
pi_heif==0.22.0
pickleshare @ file:///tmp/build/80754af9/pickleshare_1606932040724/work
pikepdf==9.7.0
pillow==11.0.0
pipmaster==0.5.4
pkce @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/pkce_1699241403777/work
pkginfo @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_21aly_cba3/croot/pkginfo_1715695988648/work
platformdirs==4.3.8
plotly @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_c4cwziyfrp/croot/plotly_1726245572537/work
pluggy==1.6.0
ply @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/ply_1699237976675/work
portalocker==2.10.1
posthog==3.24.1
pre_commit==4.2.0
preshed==3.0.9
proglog==0.1.12
prometheus-client @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/prometheus_client_1699242791845/work
prompt-toolkit @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_c63v4kqjzr/croot/prompt-toolkit_1704404354115/work
propcache==0.3.2
Protego @ file:///tmp/build/80754af9/protego_1598657180827/work
proto-plus==1.26.1
protobuf==5.29.5
psutil==7.0.0
ptyprocess @ file:///tmp/build/80754af9/ptyprocess_1609355006118/work/dist/ptyprocess-0.7.0-py2.py3-none-any.whl
pure-eval @ file:///opt/conda/conda-bld/pure_eval_1646925070566/work
py-cpuinfo @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/py-cpuinfo_1699242825254/work
py-deviceid==0.1.1
pyarrow==19.0.1
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycocotools==2.0.8
pycodestyle @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_eeqv1b4oo7/croot/pycodestyle_1701910176728/work
pycomposefile==0.0.33
pycosat @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_19qelmdbl6/croot/pycosat_1714510743067/work
pycparser==2.22
pyct @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/pyct_1699248363282/work
pycurl @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_da76jm_izu/croot/pycurl_1725370249316/work
pydantic==2.11.7
pydantic-settings==2.8.1
pydantic_core==2.33.2
pydeck @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_df_5iy65um/croot/pydeck_1706194077123/work
pydicom==3.0.1
PyDispatcher @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/pydispatcher_1699248379667/work
pydocstyle @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/pydocstyle_1699242840130/work
pydyf==0.11.0
pyerfa @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_4b_blcl5tb/croot/pyerfa_1717700766739/work
pyflakes @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_bbjb40dmuh/croot/pyflakes_1708962969108/work
PyGithub==1.59.1
Pygments==2.19.1
PyJWT==2.10.1
pylint @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/pylint_1699242870766/work
pylint-venv @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_80rsj1kckp/croot/pylint-venv_1709837627074/work
pyls-spyder==0.4.0
pymongo==4.12.0
PyMuPDF==1.25.5
PyNaCl==1.5.0
pyobjc==11.1
pyobjc-core==11.1
pyobjc-framework-Accessibility==11.1
pyobjc-framework-Accounts==11.1
pyobjc-framework-AddressBook==11.1
pyobjc-framework-AdServices==11.1
pyobjc-framework-AdSupport==11.1
pyobjc-framework-AppleScriptKit==11.1
pyobjc-framework-AppleScriptObjC==11.1
pyobjc-framework-ApplicationServices==11.1
pyobjc-framework-AppTrackingTransparency==11.1
pyobjc-framework-AudioVideoBridging==11.1
pyobjc-framework-AuthenticationServices==11.1
pyobjc-framework-AutomaticAssessmentConfiguration==11.1
pyobjc-framework-Automator==11.1
pyobjc-framework-AVFoundation==11.1
pyobjc-framework-AVKit==11.1
pyobjc-framework-AVRouting==11.1
pyobjc-framework-BackgroundAssets==11.1
pyobjc-framework-BrowserEngineKit==11.1
pyobjc-framework-BusinessChat==11.1
pyobjc-framework-CalendarStore==11.1
pyobjc-framework-CallKit==11.1
pyobjc-framework-Carbon==11.1
pyobjc-framework-CFNetwork==11.1
pyobjc-framework-Cinematic==11.1
pyobjc-framework-ClassKit==11.1
pyobjc-framework-CloudKit==11.1
pyobjc-framework-Cocoa==11.1
pyobjc-framework-Collaboration==11.1
pyobjc-framework-ColorSync==11.1
pyobjc-framework-Contacts==11.1
pyobjc-framework-ContactsUI==11.1
pyobjc-framework-CoreAudio==11.1
pyobjc-framework-CoreAudioKit==11.1
pyobjc-framework-CoreBluetooth==11.1
pyobjc-framework-CoreData==11.1
pyobjc-framework-CoreHaptics==11.1
pyobjc-framework-CoreLocation==11.1
pyobjc-framework-CoreMedia==11.1
pyobjc-framework-CoreMediaIO==11.1
pyobjc-framework-CoreMIDI==11.1
pyobjc-framework-CoreML==11.1
pyobjc-framework-CoreMotion==11.1
pyobjc-framework-CoreServices==11.1
pyobjc-framework-CoreSpotlight==11.1
pyobjc-framework-CoreText==11.1
pyobjc-framework-CoreWLAN==11.1
pyobjc-framework-CryptoTokenKit==11.1
pyobjc-framework-DataDetection==11.1
pyobjc-framework-DeviceCheck==11.1
pyobjc-framework-DeviceDiscoveryExtension==11.1
pyobjc-framework-DictionaryServices==11.1
pyobjc-framework-DiscRecording==11.1
pyobjc-framework-DiscRecordingUI==11.1
pyobjc-framework-DiskArbitration==11.1
pyobjc-framework-DVDPlayback==11.1
pyobjc-framework-EventKit==11.1
pyobjc-framework-ExceptionHandling==11.1
pyobjc-framework-ExecutionPolicy==11.1
pyobjc-framework-ExtensionKit==11.1
pyobjc-framework-ExternalAccessory==11.1
pyobjc-framework-FileProvider==11.1
pyobjc-framework-FileProviderUI==11.1
pyobjc-framework-FinderSync==11.1
pyobjc-framework-FSEvents==11.1
pyobjc-framework-FSKit==11.1
pyobjc-framework-GameCenter==11.1
pyobjc-framework-GameController==11.1
pyobjc-framework-GameKit==11.1
pyobjc-framework-GameplayKit==11.1
pyobjc-framework-HealthKit==11.1
pyobjc-framework-ImageCaptureCore==11.1
pyobjc-framework-InputMethodKit==11.1
pyobjc-framework-InstallerPlugins==11.1
pyobjc-framework-InstantMessage==11.1
pyobjc-framework-Intents==11.1
pyobjc-framework-IntentsUI==11.1
pyobjc-framework-IOBluetooth==11.1
pyobjc-framework-IOBluetoothUI==11.1
pyobjc-framework-IOSurface==11.1
pyobjc-framework-iTunesLibrary==11.1
pyobjc-framework-KernelManagement==11.1
pyobjc-framework-LatentSemanticMapping==11.1
pyobjc-framework-LaunchServices==11.1
pyobjc-framework-libdispatch==11.1
pyobjc-framework-libxpc==11.1
pyobjc-framework-LinkPresentation==11.1
pyobjc-framework-LocalAuthentication==11.1
pyobjc-framework-LocalAuthenticationEmbeddedUI==11.1
pyobjc-framework-MailKit==11.1
pyobjc-framework-MapKit==11.1
pyobjc-framework-MediaAccessibility==11.1
pyobjc-framework-MediaExtension==11.1
pyobjc-framework-MediaLibrary==11.1
pyobjc-framework-MediaPlayer==11.1
pyobjc-framework-MediaToolbox==11.1
pyobjc-framework-Metal==11.1
pyobjc-framework-MetalFX==11.1
pyobjc-framework-MetalKit==11.1
pyobjc-framework-MetalPerformanceShaders==11.1
pyobjc-framework-MetalPerformanceShadersGraph==11.1
pyobjc-framework-MetricKit==11.1
pyobjc-framework-MLCompute==11.1
pyobjc-framework-ModelIO==11.1
pyobjc-framework-MultipeerConnectivity==11.1
pyobjc-framework-NaturalLanguage==11.1
pyobjc-framework-NetFS==11.1
pyobjc-framework-Network==11.1
pyobjc-framework-NetworkExtension==11.1
pyobjc-framework-NotificationCenter==11.1
pyobjc-framework-OpenDirectory==11.1
pyobjc-framework-OSAKit==11.1
pyobjc-framework-OSLog==11.1
pyobjc-framework-PassKit==11.1
pyobjc-framework-PencilKit==11.1
pyobjc-framework-PHASE==11.1
pyobjc-framework-Photos==11.1
pyobjc-framework-PhotosUI==11.1
pyobjc-framework-PreferencePanes==11.1
pyobjc-framework-PushKit==11.1
pyobjc-framework-Quartz==11.1
pyobjc-framework-QuickLookThumbnailing==11.1
pyobjc-framework-ReplayKit==11.1
pyobjc-framework-SafariServices==11.1
pyobjc-framework-SafetyKit==11.1
pyobjc-framework-SceneKit==11.1
pyobjc-framework-ScreenCaptureKit==11.1
pyobjc-framework-ScreenSaver==11.1
pyobjc-framework-ScreenTime==11.1
pyobjc-framework-ScriptingBridge==11.1
pyobjc-framework-SearchKit==11.1
pyobjc-framework-Security==11.1
pyobjc-framework-SecurityFoundation==11.1
pyobjc-framework-SecurityInterface==11.1
pyobjc-framework-SecurityUI==11.1
pyobjc-framework-SensitiveContentAnalysis==11.1
pyobjc-framework-ServiceManagement==11.1
pyobjc-framework-SharedWithYou==11.1
pyobjc-framework-SharedWithYouCore==11.1
pyobjc-framework-ShazamKit==11.1
pyobjc-framework-Social==11.1
pyobjc-framework-SoundAnalysis==11.1
pyobjc-framework-Speech==11.1
pyobjc-framework-SpriteKit==11.1
pyobjc-framework-StoreKit==11.1
pyobjc-framework-Symbols==11.1
pyobjc-framework-SyncServices==11.1
pyobjc-framework-SystemConfiguration==11.1
pyobjc-framework-SystemExtensions==11.1
pyobjc-framework-ThreadNetwork==11.1
pyobjc-framework-UniformTypeIdentifiers==11.1
pyobjc-framework-UserNotifications==11.1
pyobjc-framework-UserNotificationsUI==11.1
pyobjc-framework-VideoSubscriberAccount==11.1
pyobjc-framework-VideoToolbox==11.1
pyobjc-framework-Virtualization==11.1
pyobjc-framework-Vision==11.1
pyobjc-framework-WebKit==11.1
pyodbc @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_6fm48uguvm/croot/pyodbc_1725560248566/work
pyOpenSSL @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_55z9ffoadh/croot/pyopenssl_1723651585773/work
pypandoc==1.15
pyparsing==3.2.3
pypdf==5.4.0
PyPDF2==3.0.1
pypdfium2==4.30.0
pyphen==0.17.2
PyPika==0.48.9
pyproject_hooks==1.2.0
PyQt5==5.15.10
PyQt5-sip @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/pyqt-split_1699239482609/work/pyqt_sip
PyQtWebEngine==5.15.6
PySocks @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/pysocks_1699237568675/work
pytesseract==0.3.13
pytest==8.4.1
pytest-asyncio==1.1.0
pytest-cov @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_40hkmsbn0m/croot/pytest-cov_1732897215149/work
python-dateutil @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_66ud1l42_h/croot/python-dateutil_1716495741162/work
python-docx==1.1.2
python-dotenv==1.1.0
python-gdcm==********
python-iso639==2025.2.18
python-json-logger @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/python-json-logger_1699249563021/work
python-lsp-black @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_82mkiw3l7w/croot/python-lsp-black_1709232912184/work
python-lsp-jsonrpc @ file:///croot/python-lsp-jsonrpc_1708962872556/work
python-lsp-server @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_bdnoklk478/croot/python-lsp-server_1708971744028/work
python-magic==0.4.27
python-multipart==0.0.20
python-oxmsg==0.0.2
python-pptx==1.0.2
python-slugify @ file:///tmp/build/80754af9/python-slugify_1620405669636/work
pytoolconfig @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_c0c43xm9fv/croot/pytoolconfig_1701728714940/work
pytorchvideo @ git+https://github.com/Re-bin/pytorchvideo.git@58f50da4e4b7bf0b17b1211dc6b283ba42e522df
pyttsx3==2.98
pytz==2025.2
pyviz_comms @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_c98m0kf4qk/croot/pyviz_comms_1711136840525/work
PyWavelets @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_daz4vp9nbh/croot/pywavelets_1725657952023/work
PyYAML==6.0.2
pyzmq @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_43pxpbos3z/croot/pyzmq_1705605108344/work
QDarkStyle @ file:///croot/qdarkstyle_1709231003551/work
qstylizer @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/qstylizer_1699262427573/work/dist/qstylizer-0.2.2-py2.py3-none-any.whl#sha256=cd3b31aa7090b6c66ce5853330138531f3c0dde133f5a7f1e8a395c62fa57ac1
QtAwesome @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_50dnstqs1c/croot/qtawesome_1726169352530/work
qtconsole @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_cfdakfnvf3/croot/qtconsole_1709231159186/work
QtPy @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/qtpy_1701804233944/work
queuelib @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/queuelib_1699249732700/work
RapidFuzz==3.13.0
rdflib==7.1.4
referencing @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/referencing_1701803099840/work
regex==2024.11.6
requests @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_70sm12ba9w/croot/requests_1721414707360/work
requests-file @ file:///Users/<USER>/demo/mc3/conda-bld/requests-file_1629455781986/work
requests-mock==1.12.1
requests-oauthlib==2.0.0
requests-toolbelt @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/requests-toolbelt_1699238632371/work
rfc3339-validator @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/rfc3339-validator_1699249772675/work
rfc3986==1.5.0
rfc3986-validator @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/rfc3986-validator_1699249792387/work
rich==14.0.0
rope @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_337no7p3xs/croot/rope_1708963178733/work
rpds-py @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/rpds-py_1699262599461/work
rsa==4.9.1
rstr==3.2.2
Rtree @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/rtree_1699262640425/work
ruamel-yaml-conda @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/ruamel_yaml_1699249814223/work
ruamel.yaml @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_35yvtl3p84/croot/ruamel.yaml_1727980165481/work
ruamel.yaml.clib @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_70l_orv46q/croot/ruamel.yaml.clib_1727769819918/work
s3fs @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_fa25uuxfjq/croot/s3fs_1724924107705/work
safetensors==0.5.3
scikit-image @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_d6j00jzmsp/croot/scikit-image_1726737419383/work
scikit-learn==1.6.1
scipy==1.15.2
scp==0.13.6
Scrapy @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_26idk0ntp9/croot/scrapy_1708714690612/work
seaborn @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_f3_ueh70ud/croot/seaborn_1718302932585/work
segments==2.3.0
semantic-chunkers==0.1.1
semantic-router==0.1.8
semver @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_99ujwp04tw/croot/semver_1709243633470/work
Send2Trash @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/send2trash_1701803532643/work
sentence-transformers==4.1.0
service-identity @ file:///Users/<USER>/demo/mc3/conda-bld/service_identity_1629460757137/work
setuptools==75.8.0
shellingham==1.5.4
SimpleITK==2.4.1
sip @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/sip_1699238710791/work
six==1.17.0
smart-open==7.1.0
smmap @ file:///tmp/build/80754af9/smmap_1611694433573/work
sniffio==1.3.1
snowballstemmer @ file:///tmp/build/80754af9/snowballstemmer_1637937080595/work
sortedcontainers @ file:///tmp/build/80754af9/sortedcontainers_1623949099177/work
soundfile==0.13.1
soupsieve==2.6
spacy==3.8.5
spacy-alignments==0.9.1
spacy-curated-transformers==0.3.1
spacy-legacy @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/spacy-legacy_1699243438786/work
spacy-loggers==1.0.5
spacy-lookups-data==1.0.5
spacy-transformers==1.3.8
Sphinx @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_50hy2c49dh/croot/sphinx_1718275395179/work
sphinxcontrib-applehelp @ file:///home/<USER>/src/ci/sphinxcontrib-applehelp_1611920841464/work
sphinxcontrib-devhelp @ file:///home/<USER>/src/ci/sphinxcontrib-devhelp_1611920923094/work
sphinxcontrib-htmlhelp @ file:///tmp/build/80754af9/sphinxcontrib-htmlhelp_1623945626792/work
sphinxcontrib-jsmath @ file:///home/<USER>/src/ci/sphinxcontrib-jsmath_1611920942228/work
sphinxcontrib-qthelp @ file:///home/<USER>/src/ci/sphinxcontrib-qthelp_1611921055322/work
sphinxcontrib-serializinghtml @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_c0gy7ylkec/croot/sphinxcontrib-serializinghtml_1718201677157/work
spyder @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_892g68m5sn/croot/spyder_1727198303638/work
spyder-kernels @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_42vj8220mm/croot/spyder-kernels_1707937716590/work
SQLAlchemy @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_3frqduvs8h/croot/sqlalchemy_1725885054432/work
srsly @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_2b8vcy3i7c/croot/srsly_1741857988640/work
sshtunnel==0.1.5
stack-data @ file:///opt/conda/conda-bld/stack_data_1646927590127/work
starlette==0.45.3
statsmodels @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_af8vmuwbis/croot/statsmodels_1718381196260/work
streamlit @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_52t8o6xfs8/croot/streamlit_1724335169234/work
structlog==25.4.0
surya-ocr==0.14.7
sympy==1.13.1
tables @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_b6xsinywwn/croot/pytables_1725380789988/work
tabulate @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/tabulate_1701807377411/work
tblib @ file:///Users/<USER>/demo/mc3/conda-bld/tblib_1629402031467/work
tenacity==8.5.0
termcolor==3.1.0
terminado @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/terminado_1699283001768/work
text-unidecode @ file:///Users/<USER>/demo/mc3/conda-bld/text-unidecode_1629401354553/work
textdistance @ file:///tmp/build/80754af9/textdistance_1612461398012/work
thinc==8.3.6
threadpoolctl @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_efa5bvb6vi/croot/threadpoolctl_1719407806403/work
three-merge @ file:///tmp/build/80754af9/three-merge_1607553261110/work
tifffile @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/tifffile_1699243525693/work
tiktoken==0.9.0
timm==1.0.15
tinycss2==1.4.0
tinyhtml5==2.0.0
tldextract @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_86f1bw02kg/croot/tldextract_1723064389239/work
tokenizers==0.20.3
toml @ file:///tmp/build/80754af9/toml_1616166611790/work
tomli @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/tomli_1699237289925/work
tomlkit @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/tomlkit_1699238737474/work
toolz @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/toolz_1699238160466/work
torch==2.5.0
torchaudio==2.6.0
torchvision==0.21.0
tornado==6.5.1
TotalSegmentator==2.8.0
tqdm==4.66.5
traceback2==1.4.0
traitlets @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_500m2_1wyk/croot/traitlets_1718227071952/work
transformers==4.45.0
truststore @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/truststore_1701805928787/work
Twisted @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_c4spem5kwe/croot/twisted_1708702820544/work
typer==0.15.2
types-PyYAML==6.0.12.20250402
typing-inspect==0.9.0
typing-inspection @ file:///home/<USER>/feedstock_root/build_artifacts/typing-inspection_1741438046699/work
typing_extensions==4.13.2
tzdata==2025.2
uc-micro-py @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/uc-micro-py_1699250544885/work
ujson @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_dexxju769y/croot/ujson_1717597527341/work
unicodedata2 @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_a3epjto7gs/croot/unicodedata2_1713212955584/work
Unidecode @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_33gnubyv29/croot/unidecode_1724790055767/work
unittest2==1.1.0
unstructured==0.17.2
unstructured-client==0.33.0
unstructured-inference==0.8.10
unstructured.pytesseract==0.3.15
uritemplate==4.2.0
urllib3==1.26.20
uv==0.8.0
uvicorn==0.34.1
uvloop==0.21.0
virtualenv==20.32.0
w3lib @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/w3lib_1709223508304/work
wasabi==1.1.3
watchdog @ file:///Users/<USER>/cbouss/crwatchdog/watchdog_1717177010913/work
watchfiles==1.0.5
wcwidth @ file:///Users/<USER>/demo/mc3/conda-bld/wcwidth_1629357192024/work
weasel==0.4.1
weasyprint==63.1
webencodings @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/webencodings_1699243630039/work
websocket-client==1.3.3
websockets==15.0.1
Werkzeug==3.1.3
whatthepatch @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/whatthepatch_1699243659982/work
wheel==0.45.1
widgetsnbextension @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_45ynevdsp5/croot/widgetsnbextension_1710960054121/work
wrapt==1.17.2
wurlitzer @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/wurlitzer_1699265832005/work
xarray @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/xarray_1699240726099/work
xlrd==2.0.1
XlsxWriter==3.2.3
xlwings @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_1cs4qhgbiw/croot/xlwings_1725400081092/work
xmljson==0.2.1
xmltodict==0.14.2
xvfbwrapper==0.2.10
xxhash==3.5.0
xyzservices @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/xyzservices_1699243674756/work
yacs==0.1.8
yapf @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_29ra4n2npe/croot/yapf_1708964324475/work
yarl==1.20.1
zict @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/zict_1699250947936/work
zipp @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/zipp_1707348942775/work
zope.interface @ file:///Users/<USER>/cbouss/perseverance-python-buildout/croot/zope.interface_1699243689919/work
zopfli==0.2.3.post1
zstandard @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_cairdg5u3o/croot/zstandard_1728569200438/work
