#!/usr/bin/env python3
"""
Complete HippoRAG2 with Answer Generation (Following AutoSchemaKG Pattern)

This script implements the full HippoRAG2 pipeline:
1. Query expansion and entity filtering
2. Retrieval with real PageRank scores
3. LLM-based answer generation using Ollama

Follows the exact AutoSchemaKG pattern from the original implementation.
"""

import os
import sys
import pickle
from pathlib import Path
from datetime import datetime

# Setup environment
os.environ["TOKENIZERS_PARALLELISM"] = "false"
sys.path.insert(0, str(Path(__file__).parent))

# Import components
from enhanced_hipporag2_retriever_module import EnhancedHippoRAG2Retriever
from hipporag2_answer_generator import <PERSON><PERSON><PERSON><PERSON>2AnswerGenerator, OllamaConfig
from atlas_rag.retriever.inference_config import InferenceConfig
from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
from sentence_transformers import SentenceTransformer
from openai import OpenAI
from atlas_rag.llm_generator import LLMGenerator
from ollama_model_manager import OllamaModelManager

# Configuration - Change this to use a different Ollama model
DEFAULT_OLLAMA_MODEL = "qwen3:30b-a3b-thinking-2507-q4_K_M"  # Edit this to use your preferred model


class CompleteHippoRAG2System:
    """
    Complete HippoRAG2 system following AutoSchemaKG pattern.
    
    Components:
    1. Enhanced retriever with entity filtering and query expansion
    2. Answer generator with Ollama integration
    3. Full configuration control
    """
    
    def __init__(self, data_directory="import/pdf_dataset", ollama_config=None):
        self.data_directory = data_directory
        self.ollama_config = ollama_config or OllamaConfig()
        self.retriever = None
        self.answer_generator = None
        self.data = None
        
    def initialize(self):
        """Initialize all components."""
        print("🚀 INITIALIZING COMPLETE HIPPORAG2 SYSTEM")
        print("="*60)
        print("Following AutoSchemaKG Pattern:")
        print("  ✅ Entity-based retrieval (query2node)")
        print("  ✅ Real PageRank scoring")
        print("  ✅ LLM answer generation with Ollama")
        print()
        
        # Load data
        if not self._load_data():
            return False
            
        # Setup embedding model
        print("🔤 Setting up embedding model...")
        encoder_model_name = 'sentence-transformers/all-mpnet-base-v2'
        sentence_model = SentenceTransformer(encoder_model_name)
        sentence_encoder = SentenceEmbedding(sentence_model)
        print(f"   ✅ Using {encoder_model_name} (768-dim)")
        
        # Setup LLM for retriever (filtering)
        print("🤖 Setting up LLM for retriever...")
        llm_for_retriever = self._setup_retriever_llm()
        
        # Setup inference config (optimized for financial content)
        inference_config = InferenceConfig()
        inference_config.topk_edges = 30
        inference_config.ppr_alpha = 0.15
        inference_config.ppr_max_iter = 200
        inference_config.weight_adjust = 0.1
        
        # Initialize enhanced retriever
        print("🔍 Initializing Enhanced HippoRAG2 Retriever...")
        try:
            self.retriever = EnhancedHippoRAG2Retriever(
                llm_generator=llm_for_retriever,
                sentence_encoder=sentence_encoder,
                data=self.data,
                inference_config=inference_config
            )
            print("   ✅ Retriever ready with query expansion and entity filtering")
        except Exception as e:
            print(f"   ❌ Failed to initialize retriever: {e}")
            return False
        
        # Initialize answer generator
        print("💬 Initializing Answer Generator...")
        self.answer_generator = HippoRAG2AnswerGenerator(self.ollama_config)
        if not self.answer_generator.llm_generator:
            print("   ⚠️ Answer generator LLM not available")
            print("   Retrieval will work but answer generation will be limited")
        
        print("\n✅ SYSTEM READY!")
        return True
    
    def _load_data(self):
        """Load the complete data structure."""
        print("📊 Loading data...")
        data_file = Path(self.data_directory) / "complete_data.pkl"
        
        if not data_file.exists():
            print(f"   ❌ Data file not found: {data_file}")
            return False
            
        try:
            with open(data_file, 'rb') as f:
                self.data = pickle.load(f)
            print(f"   ✅ Data loaded: {len(self.data['KG'].nodes)} nodes, {len(self.data['KG'].edges)} edges")
            return True
        except Exception as e:
            print(f"   ❌ Error loading data: {e}")
            return False
    
    def _setup_retriever_llm(self):
        """Setup LLM for retriever (used for filtering)."""
        try:
            # Use the same Ollama instance for retriever
            client = OpenAI(
                base_url=self.ollama_config.base_url,
                api_key="dummy-key",
            )
            return LLMGenerator(
                client=client,
                model_name=self.ollama_config.model_name
            )
        except:
            print("   ⚠️ LLM for retriever not available (filtering disabled)")
            return None
    
    def query(self, question, top_k=5, use_cot=True, verbose=True):
        """
        Complete query pipeline: retrieval + answer generation.
        
        Args:
            question: User's query
            top_k: Number of passages to retrieve
            use_cot: Use Chain-of-Thought for answer generation
            verbose: Print detailed information
            
        Returns:
            Dict with answer, passages, scores, and metadata
        """
        if verbose:
            print(f"\n{'='*60}")
            print(f"🎯 QUERY: {question}")
            print(f"{'='*60}")
        
        # Step 1: Query expansion (if enhanced retriever)
        if hasattr(self.retriever, 'expand_query'):
            expanded = self.retriever.expand_query(question)
            if expanded != question and verbose:
                print(f"📝 Expanded query: {expanded[:100]}...")
        
        # Step 2: Retrieval with real PageRank scores
        if verbose:
            print(f"\n🔍 Retrieving top {top_k} passages...")
        
        passages, passage_ids, scores = self.retriever.retrieve(question, topN=top_k)
        
        if verbose:
            print(f"   ✅ Retrieved {len(passages)} passages")
            for i, (pid, score) in enumerate(zip(passage_ids[:3], scores[:3]), 1):
                if score < 0.001:
                    print(f"      {i}. Score: {score:.2e} | ID: {pid}")
                else:
                    print(f"      {i}. Score: {score:.6f} | ID: {pid}")
        
        # Step 3: Answer generation (following AutoSchemaKG pattern)
        if verbose:
            print(f"\n💬 Generating answer with {self.ollama_config.model_name}...")
        
        if self.answer_generator and self.answer_generator.llm_generator:
            answer, metadata = self.answer_generator.generate_answer_from_passages(
                question=question,
                passages=passages,
                passage_ids=passage_ids,
                scores=scores,
                use_cot=use_cot
            )
            
            if verbose:
                print(f"   ✅ Answer generated using {metadata.get('model', 'unknown')}")
        else:
            # Fallback: concatenate top passages
            answer = "LLM not available. Top retrieved passages:\n\n"
            for i, passage in enumerate(passages[:3], 1):
                answer += f"{i}. {passage[:200]}...\n\n"
            metadata = {"fallback": True}
        
        # Prepare result
        result = {
            "question": question,
            "answer": answer,
            "passages": passages,
            "passage_ids": passage_ids,
            "scores": scores,
            "metadata": metadata,
            "timestamp": datetime.now().isoformat()
        }
        
        if verbose:
            print(f"\n{'='*60}")
            print("✨ ANSWER:")
            print(f"{'='*60}")
            print(answer)
            print(f"{'='*60}\n")
        
        return result
    
    def interactive_session(self):
        """Run interactive Q&A session with full pipeline."""
        print("\n🎮 INTERACTIVE HIPPORAG2 SESSION")
        print("="*60)
        print("Commands:")
        print("  • Type your question to get an answer")
        print("  • 'params' - Show/modify LLM parameters")
        print("  • 'test' - Run test queries")
        print("  • 'quit' - Exit")
        print("="*60)
        
        while True:
            try:
                user_input = input("\n❓ Question: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                    
                elif user_input.lower() == 'params':
                    self.answer_generator.print_tunable_parameters()
                    # Allow parameter modification
                    print("\nModify parameters? (e.g., 'temperature=0.5' or 'skip')")
                    param_input = input(">>> ").strip()
                    if param_input != 'skip' and '=' in param_input:
                        key, value = param_input.split('=')
                        key = key.strip()
                        value = value.strip()
                        if hasattr(self.ollama_config, key):
                            # Convert value to appropriate type
                            if key in ['temperature', 'top_p', 'frequency_penalty', 'presence_penalty']:
                                setattr(self.ollama_config, key, float(value))
                            elif key in ['max_new_tokens', 'top_k', 'timeout', 'max_retries']:
                                setattr(self.ollama_config, key, int(value))
                            else:
                                setattr(self.ollama_config, key, value)
                            print(f"✅ Set {key} = {getattr(self.ollama_config, key)}")
                            # Reinitialize answer generator with new config
                            self.answer_generator = HippoRAG2AnswerGenerator(self.ollama_config)
                    continue
                    
                elif user_input.lower() == 'test':
                    self.run_test_queries()
                    continue
                    
                elif not user_input:
                    continue
                
                # Process the query
                result = self.query(user_input, top_k=5, use_cot=True, verbose=True)
                
            except KeyboardInterrupt:
                print("\n👋 Session interrupted. Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                continue
    
    def run_test_queries(self):
        """Run test queries to validate the system."""
        print("\n🧪 RUNNING TEST QUERIES")
        print("="*60)
        
        test_queries = [
            "What is an OCO order and how to place it in Bridge?",
            "How to create a scenario in MMC Market Maker Cockpit?",
            "Explain Risk Reversal strategy for FX options",
            "What are the features of SEF platform for trading?",
            "How does the FIX protocol work in trading systems?"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📌 Test {i}/{len(test_queries)}: {query}")
            result = self.query(query, top_k=3, use_cot=True, verbose=False)
            
            # Show brief summary
            answer = result['answer'][:200] + "..." if len(result['answer']) > 200 else result['answer']
            print(f"   Answer: {answer}")
            
            # Check relevance
            avg_score = sum(result['scores']) / len(result['scores']) if result['scores'] else 0
            print(f"   Avg Score: {avg_score:.6f}")
            print(f"   Passages Retrieved: {len(result['passages'])}")
        
        print("\n✅ Test queries completed!")


def main():
    """Main entry point."""
    print("🚀 COMPLETE HIPPORAG2 WITH ANSWER GENERATION")
    print("="*60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Configure Ollama - using the thinking variant as requested
    ollama_config = OllamaConfig(
        model_name=DEFAULT_OLLAMA_MODEL,  # Thinking variant
        temperature=0.7,
        max_new_tokens=2048,
        top_p=0.9,
        reasoning_effort="medium"  # For thinking model
    )
    
    # Note about model variants
    print("📝 Model Configuration:")
    print(f"   Using: {ollama_config.model_name}")
    print("   Note: If model not found, try:")
    print("   • qwen3:30b-a3b-instruct-2507-q4_K_M (instruct variant)")
    print("   • Pull with: ollama pull <model_name>")
    print()
    
    # Initialize system
    system = CompleteHippoRAG2System(ollama_config=ollama_config)
    
    if not system.initialize():
        print("❌ Failed to initialize system")
        return False
    
    # Print tunable parameters
    if system.answer_generator:
        system.answer_generator.print_tunable_parameters()
    
    # Run interactive session
    system.interactive_session()
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)