#!/usr/bin/env python3
"""
Ollama Memory Diagnostics Tool - Comprehensive analysis and optimization for large models.

This tool diagnoses memory issues with 30B+ models and provides optimization recommendations.
"""

import subprocess
import json
import time
import os
import psutil
from typing import Dict, List, Tuple, Optional
from datetime import datetime

class OllamaMemoryDiagnostics:
    """Comprehensive Ollama memory analysis and optimization tool."""
    
    def __init__(self):
        """Initialize diagnostics tool."""
        self.system_info = {}
        self.ollama_processes = []
        self.loaded_models = []
        self.memory_usage = {}
        self.gpu_info = {}
        
    def run_command(self, command: List[str]) -> Tuple[str, str, int]:
        """Run system command and return output, error, and return code."""
        try:
            result = subprocess.run(command, capture_output=True, text=True, timeout=30)
            return result.stdout.strip(), result.stderr.strip(), result.returncode
        except subprocess.TimeoutExpired:
            return "", "Command timed out", -1
        except Exception as e:
            return "", str(e), -1
    
    def get_system_memory_info(self):
        """Get detailed system memory information."""
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        self.memory_usage = {
            'total_ram_gb': round(memory.total / (1024**3), 2),
            'available_ram_gb': round(memory.available / (1024**3), 2),
            'used_ram_gb': round(memory.used / (1024**3), 2),
            'ram_percent': memory.percent,
            'swap_total_gb': round(swap.total / (1024**3), 2),
            'swap_used_gb': round(swap.used / (1024**3), 2),
            'swap_percent': swap.percent
        }
        
    def get_gpu_info(self):
        """Get GPU memory information if available."""
        stdout, stderr, code = self.run_command(['nvidia-smi', '--query-gpu=memory.total,memory.used,memory.free,utilization.gpu', '--format=csv,noheader,nounits'])
        
        if code == 0 and stdout:
            try:
                lines = stdout.strip().split('\n')
                self.gpu_info = []
                for i, line in enumerate(lines):
                    parts = line.split(', ')
                    if len(parts) >= 4:
                        self.gpu_info.append({
                            'gpu_id': i,
                            'total_vram_mb': int(parts[0]),
                            'used_vram_mb': int(parts[1]),
                            'free_vram_mb': int(parts[2]),
                            'gpu_utilization': int(parts[3])
                        })
            except (ValueError, IndexError) as e:
                self.gpu_info = {'error': f'Failed to parse GPU info: {e}'}
        else:
            self.gpu_info = {'error': 'NVIDIA GPU not detected or nvidia-smi not available'}
    
    def get_ollama_processes(self):
        """Get information about running Ollama processes."""
        self.ollama_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
            try:
                if 'ollama' in proc.info['name'].lower():
                    memory_mb = round(proc.info['memory_info'].rss / (1024**2), 1)
                    self.ollama_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'memory_mb': memory_mb,
                        'cpu_percent': proc.info['cpu_percent']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
    
    def get_loaded_models(self):
        """Get information about currently loaded Ollama models."""
        stdout, stderr, code = self.run_command(['ollama', 'ps'])
        
        if code == 0 and stdout:
            lines = stdout.strip().split('\n')
            self.loaded_models = []
            
            # Skip header line
            for line in lines[1:] if len(lines) > 1 else []:
                parts = line.split()
                if len(parts) >= 4:
                    self.loaded_models.append({
                        'name': parts[0],
                        'id': parts[1] if len(parts) > 1 else 'unknown',
                        'size': parts[2] if len(parts) > 2 else 'unknown',
                        'processor': parts[3] if len(parts) > 3 else 'unknown'
                    })
        else:
            self.loaded_models = [{'error': f'Failed to get models: {stderr}'}]
    
    def check_environment_variables(self):
        """Check important Ollama environment variables."""
        env_vars = {
            'OLLAMA_MAX_LOADED_MODELS': os.getenv('OLLAMA_MAX_LOADED_MODELS', 'not_set'),
            'OLLAMA_NUM_PARALLEL': os.getenv('OLLAMA_NUM_PARALLEL', 'not_set'),
            'OLLAMA_KV_CACHE_TYPE': os.getenv('OLLAMA_KV_CACHE_TYPE', 'not_set'),
            'OLLAMA_HOST': os.getenv('OLLAMA_HOST', 'not_set'),
            'CUDA_VISIBLE_DEVICES': os.getenv('CUDA_VISIBLE_DEVICES', 'not_set')
        }
        return env_vars
    
    def analyze_memory_pressure(self):
        """Analyze if system is under memory pressure."""
        analysis = {
            'memory_pressure': False,
            'vram_pressure': False,
            'recommendations': []
        }
        
        # System RAM analysis
        if self.memory_usage['ram_percent'] > 85:
            analysis['memory_pressure'] = True
            analysis['recommendations'].append("HIGH RAM usage detected (>85%)")
        elif self.memory_usage['ram_percent'] > 70:
            analysis['recommendations'].append("Moderate RAM usage (>70%)")
        
        # GPU VRAM analysis
        if isinstance(self.gpu_info, list) and self.gpu_info:
            for gpu in self.gpu_info:
                vram_percent = (gpu['used_vram_mb'] / gpu['total_vram_mb']) * 100
                if vram_percent > 90:
                    analysis['vram_pressure'] = True
                    analysis['recommendations'].append(f"HIGH VRAM usage on GPU {gpu['gpu_id']} ({vram_percent:.1f}%)")
                elif vram_percent > 75:
                    analysis['recommendations'].append(f"Moderate VRAM usage on GPU {gpu['gpu_id']} ({vram_percent:.1f}%)")
        
        # Model size analysis
        ollama_memory = sum(p['memory_mb'] for p in self.ollama_processes)
        if ollama_memory > 20000:  # 20GB
            analysis['recommendations'].append(f"Large Ollama memory footprint: {ollama_memory/1024:.1f}GB")
        
        return analysis
    
    def get_optimization_recommendations(self):
        """Generate specific optimization recommendations."""
        recommendations = []
        env_vars = self.check_environment_variables()
        
        # K/V Cache optimization (2024 feature)
        if env_vars['OLLAMA_KV_CACHE_TYPE'] == 'not_set':
            recommendations.append({
                'priority': 'HIGH',
                'action': 'Enable K/V Cache Quantization',
                'command': 'export OLLAMA_KV_CACHE_TYPE="q8_0"',
                'benefit': 'Reduces VRAM usage by 30-50%'
            })
        
        # Model concurrency limits
        if env_vars['OLLAMA_MAX_LOADED_MODELS'] == 'not_set':
            recommendations.append({
                'priority': 'MEDIUM',
                'action': 'Limit concurrent models',
                'command': 'export OLLAMA_MAX_LOADED_MODELS="1"',
                'benefit': 'Prevents memory fragmentation'
            })
        
        # Parallel request limits
        if env_vars['OLLAMA_NUM_PARALLEL'] == 'not_set':
            recommendations.append({
                'priority': 'MEDIUM',
                'action': 'Reduce parallel requests',
                'command': 'export OLLAMA_NUM_PARALLEL="1"',
                'benefit': 'Reduces memory per model'
            })
        
        # Memory pressure specific recommendations
        analysis = self.analyze_memory_pressure()
        if analysis['memory_pressure']:
            recommendations.append({
                'priority': 'CRITICAL',
                'action': 'Restart Ollama service',
                'command': 'ollama serve',
                'benefit': 'Clears memory fragmentation'
            })
        
        if analysis['vram_pressure']:
            recommendations.append({
                'priority': 'CRITICAL',
                'action': 'Switch to smaller model',
                'command': 'Try qwen3:13b or qwen3:7b',
                'benefit': 'Immediate memory relief'
            })
        
        return recommendations
    
    def generate_cleanup_script(self):
        """Generate a cleanup script for Ollama processes."""
        script = '''#!/bin/bash
# Ollama Memory Cleanup Script
echo "🧹 Ollama Memory Cleanup Starting..."

# Stop Ollama service gracefully
echo "Stopping Ollama service..."
pkill -TERM ollama
sleep 5

# Force kill if still running
if pgrep ollama > /dev/null; then
    echo "Force stopping Ollama..."
    pkill -KILL ollama
    sleep 2
fi

# Clear system caches (requires sudo)
echo "Clearing system caches..."
sync
echo 3 | sudo tee /proc/sys/vm/drop_caches > /dev/null 2>&1 || echo "Cache clear requires sudo"

# Set optimized environment variables
export OLLAMA_KV_CACHE_TYPE="q8_0"
export OLLAMA_MAX_LOADED_MODELS="1"
export OLLAMA_NUM_PARALLEL="1"

# Restart Ollama with optimizations
echo "Restarting Ollama with memory optimizations..."
ollama serve &

# Wait for startup
sleep 10

echo "✅ Ollama restart complete!"
echo "Environment optimizations:"
echo "  - K/V Cache Quantization: Enabled"
echo "  - Max Models: 1"
echo "  - Parallel Requests: 1"
'''
        return script
    
    def run_full_diagnostics(self):
        """Run complete memory diagnostics."""
        print("🔍 OLLAMA MEMORY DIAGNOSTICS")
        print("="*70)
        print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Collect all data
        print("📊 Gathering system information...")
        self.get_system_memory_info()
        self.get_gpu_info()
        self.get_ollama_processes()
        self.get_loaded_models()
        
        # System Memory Report
        print("\n💾 System Memory Status:")
        print("-" * 40)
        print(f"  Total RAM: {self.memory_usage['total_ram_gb']:.1f} GB")
        print(f"  Used RAM: {self.memory_usage['used_ram_gb']:.1f} GB ({self.memory_usage['ram_percent']:.1f}%)")
        print(f"  Available RAM: {self.memory_usage['available_ram_gb']:.1f} GB")
        print(f"  Swap Usage: {self.memory_usage['swap_used_gb']:.1f} GB / {self.memory_usage['swap_total_gb']:.1f} GB")
        
        # GPU Memory Report
        print("\n🎮 GPU Memory Status:")
        print("-" * 40)
        if isinstance(self.gpu_info, list) and self.gpu_info:
            for gpu in self.gpu_info:
                vram_percent = (gpu['used_vram_mb'] / gpu['total_vram_mb']) * 100
                print(f"  GPU {gpu['gpu_id']}: {gpu['used_vram_mb']/1024:.1f} GB / {gpu['total_vram_mb']/1024:.1f} GB ({vram_percent:.1f}%)")
                print(f"    Utilization: {gpu['gpu_utilization']}%")
        else:
            print(f"  {self.gpu_info.get('error', 'No GPU information available')}")
        
        # Ollama Processes
        print("\n🤖 Ollama Processes:")
        print("-" * 40)
        if self.ollama_processes:
            total_ollama_memory = 0
            for proc in self.ollama_processes:
                print(f"  PID {proc['pid']}: {proc['name']} - {proc['memory_mb']:.1f} MB")
                total_ollama_memory += proc['memory_mb']
            print(f"  Total Ollama Memory: {total_ollama_memory/1024:.1f} GB")
        else:
            print("  No Ollama processes found")
        
        # Loaded Models
        print("\n📋 Loaded Models:")
        print("-" * 40)
        if self.loaded_models and not any('error' in model for model in self.loaded_models):
            for model in self.loaded_models:
                print(f"  {model['name']} - Size: {model['size']} - Processor: {model['processor']}")
        else:
            print("  No models loaded or error retrieving model list")
        
        # Environment Variables
        print("\n⚙️  Environment Configuration:")
        print("-" * 40)
        env_vars = self.check_environment_variables()
        for var, value in env_vars.items():
            status = "✅" if value != "not_set" else "❌"
            print(f"  {status} {var}: {value}")
        
        # Memory Pressure Analysis
        print("\n🚨 Memory Pressure Analysis:")
        print("-" * 40)
        analysis = self.analyze_memory_pressure()
        if analysis['memory_pressure'] or analysis['vram_pressure']:
            print("  ⚠️  MEMORY PRESSURE DETECTED!")
        else:
            print("  ✅ Memory usage within normal ranges")
        
        for rec in analysis['recommendations']:
            print(f"    • {rec}")
        
        # Optimization Recommendations
        print("\n🛠️  Optimization Recommendations:")
        print("-" * 40)
        recommendations = self.get_optimization_recommendations()
        for i, rec in enumerate(recommendations, 1):
            priority_icon = "🔴" if rec['priority'] == 'CRITICAL' else "🟡" if rec['priority'] == 'HIGH' else "🔵"
            print(f"  {priority_icon} {rec['priority']}: {rec['action']}")
            print(f"     Command: {rec['command']}")
            print(f"     Benefit: {rec['benefit']}")
            print()
        
        return {
            'system_memory': self.memory_usage,
            'gpu_info': self.gpu_info,
            'ollama_processes': self.ollama_processes,
            'loaded_models': self.loaded_models,
            'analysis': analysis,
            'recommendations': recommendations
        }

def main():
    """Run memory diagnostics and generate cleanup script."""
    diagnostics = OllamaMemoryDiagnostics()
    
    # Run full diagnostics
    results = diagnostics.run_full_diagnostics()
    
    # Generate cleanup script
    print("\n🧹 Cleanup Script Generation:")
    print("-" * 40)
    cleanup_script = diagnostics.generate_cleanup_script()
    
    script_path = "ollama_memory_cleanup.sh"
    with open(script_path, 'w') as f:
        f.write(cleanup_script)
    
    print(f"✅ Cleanup script saved to: {script_path}")
    print("   Make executable with: chmod +x ollama_memory_cleanup.sh")
    print("   Run with: ./ollama_memory_cleanup.sh")
    
    print("\n" + "="*70)
    print("🎯 NEXT STEPS:")
    
    if results['analysis']['memory_pressure'] or results['analysis']['vram_pressure']:
        print("1. 🔴 CRITICAL: Run the cleanup script immediately")
        print("2. 🟡 HIGH: Enable K/V cache quantization")
        print("3. 🔵 MEDIUM: Consider switching to a smaller model")
    else:
        print("1. ✅ System memory looks healthy")
        print("2. 🔧 Apply optimization recommendations above")
        print("3. 📊 Monitor performance with new settings")

if __name__ == "__main__":
    main()