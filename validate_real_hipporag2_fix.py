#!/usr/bin/env python3
"""
Non-Interactive Real HippoRAG2 Validation Script

This script validates that:
1. The torchvision import error is resolved (MPS-compatible PyTorch fix)
2. Real HippoRAG2Retriever can be imported and initialized
3. The "risk-reversal option" query returns relevant financial content (not FIX API docs)
4. Quality improvement over the mock implementation is demonstrated
"""

import os
import sys
import pickle
from pathlib import Path
from datetime import datetime

# Set environment variable to avoid tokenizer warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Add the project root to Python path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_torchvision_fix():
    """Test that the torchvision import error is resolved."""
    print("🔧 TESTING TORCHVISION FIX")
    print("=" * 40)
    
    try:
        import torch
        import torchvision
        print(f"✅ PyTorch {torch.__version__} imported successfully")
        print(f"✅ Torchvision {torchvision.__version__} imported successfully") 
        print(f"✅ MPS available: {torch.backends.mps.is_available()}")
        
        # Test the specific operation that was failing
        import torchvision.ops
        print("✅ torchvision.ops imported successfully")
        print("🎉 TORCHVISION IMPORT ERROR RESOLVED!")
        return True
        
    except Exception as e:
        print(f"❌ Torchvision import failed: {e}")
        return False

def test_hipporag2_imports():
    """Test that HippoRAG2 components can be imported."""
    print("\n🧪 TESTING HIPPORAG2 COMPONENT IMPORTS")
    print("=" * 45)
    
    try:
        from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
        print("✅ HippoRAG2Retriever imported successfully")
        
        from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
        print("✅ SentenceEmbedding imported successfully")
        
        from atlas_rag.retriever.inference_config import InferenceConfig
        print("✅ InferenceConfig imported successfully")
        
        from sentence_transformers import SentenceTransformer
        print("✅ SentenceTransformer imported successfully")
        
        print("🎉 ALL HIPPORAG2 COMPONENTS IMPORTED SUCCESSFULLY!")
        return True
        
    except Exception as e:
        print(f"❌ HippoRAG2 component import failed: {e}")
        return False

def initialize_real_hipporag2():
    """Initialize the real HippoRAG2 system."""
    print("\n🚀 INITIALIZING REAL HIPPORAG2 SYSTEM")
    print("=" * 42)
    
    # Import required components
    from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
    from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
    from atlas_rag.retriever.inference_config import InferenceConfig
    from sentence_transformers import SentenceTransformer
    
    # Load complete data
    data_file = Path("import/pdf_dataset/complete_data.pkl")
    if not data_file.exists():
        print(f"❌ Complete data file not found: {data_file}")
        return None
    
    try:
        with open(data_file, 'rb') as f:
            data = pickle.load(f)
        print(f"✅ Complete data loaded successfully")
        print(f"   📊 Graph: {len(data['KG'].nodes)} nodes, {len(data['KG'].edges)} edges")
        print(f"   📚 Text passages: {len(data.get('text_dict', {}))} entries")
    except Exception as e:
        print(f"❌ Error loading complete data: {e}")
        return None
    
    # Initialize sentence encoder
    try:
        encoder_model_name = 'all-MiniLM-L6-v2'
        sentence_model = SentenceTransformer(encoder_model_name)
        sentence_encoder = SentenceEmbedding(sentence_model)
        print("✅ Sentence encoder initialized")
    except Exception as e:
        print(f"❌ Error initializing sentence encoder: {e}")
        return None
    
    # Initialize inference config
    inference_config = InferenceConfig()
    
    # Initialize HippoRAG2 (without LLM for simplicity)
    try:
        hipporag2_retriever = HippoRAG2Retriever(
            llm_generator=None,  # Skip LLM for this test
            sentence_encoder=sentence_encoder,
            data=data,
            inference_config=inference_config
        )
        print("✅ Real HippoRAG2Retriever initialized successfully!")
        return hipporag2_retriever, data
        
    except Exception as e:
        print(f"❌ Error initializing HippoRAG2Retriever: {e}")
        return None

def test_risk_reversal_query(hipporag2_retriever):
    """Test the critical 'risk-reversal option' query that was failing with mock."""
    print("\n🎯 TESTING RISK-REVERSAL OPTION QUERY")
    print("=" * 42)
    
    query = "what is a risk-reversal option?"
    print(f"Query: '{query}'")
    print("Expected: Financial options trading strategy content")
    print("Previous issue: Mock returned irrelevant FIX API documentation")
    print()
    
    try:
        # Use real HippoRAG2 retrieval
        content, sorted_context_ids = hipporag2_retriever.retrieve(query, topN=3)
        
        if not content:
            print("❌ No content retrieved")
            return False
        
        print(f"✅ Retrieved {len(content)} results using real HippoRAG2")
        print("\n📊 QUALITY ANALYSIS:")
        print("=" * 50)
        
        # Analyze content quality
        relevant_terms = ['option', 'call', 'put', 'strike', 'premium', 'volatility', 
                         'trading', 'strategy', 'hedge', 'risk', 'financial']
        irrelevant_terms = ['fix', 'protocol', 'api', 'message', 'field', 'tag']
        
        total_relevance_score = 0
        total_irrelevance_score = 0
        
        for i, text_content in enumerate(content):
            print(f"\n🔍 Result #{i+1}:")
            print("─" * 30)
            
            # Calculate relevance scores
            text_lower = text_content.lower()
            relevance_hits = sum(1 for term in relevant_terms if term in text_lower)
            irrelevance_hits = sum(1 for term in irrelevant_terms if term in text_lower)
            
            relevance_score = relevance_hits - irrelevance_hits
            total_relevance_score += relevance_score
            total_irrelevance_score += irrelevance_hits
            
            print(f"📊 Relevance score: {relevance_score} ({relevance_hits} relevant terms, {irrelevance_hits} irrelevant terms)")
            
            # Show preview of content
            preview = text_content[:300] + "..." if len(text_content) > 300 else text_content
            lines = preview.split('\n')[:5]  # First 5 lines
            preview_text = '\n'.join(lines)
            print(f"📄 Content preview:\n{preview_text}")
            
            if i < len(content) - 1:
                print()
        
        # Overall quality assessment
        print(f"\n📈 OVERALL QUALITY ASSESSMENT:")
        print("=" * 35)
        avg_relevance = total_relevance_score / len(content)
        total_irrelevant_results = sum(1 for i in range(len(content)) if irrelevant_terms[0] in content[i].lower())
        
        print(f"📊 Average relevance score: {avg_relevance:.1f}")
        print(f"📊 Results with irrelevant content: {total_irrelevant_results}/{len(content)}")
        
        if avg_relevance >= 2 and total_irrelevant_results == 0:
            print("🎉 EXCELLENT: High relevance, no irrelevant FIX API content!")
            return True
        elif avg_relevance >= 1 and total_irrelevant_results <= 1:
            print("👍 GOOD: Decent relevance, minimal irrelevant content")
            return True
        elif total_irrelevant_results < len(content):
            print("✅ IMPROVED: Better than mock (had only FIX API content)")
            return True
        else:
            print("⚠️  Still returning mostly irrelevant content")
            return False
            
    except Exception as e:
        print(f"❌ Error in HippoRAG2 retrieval: {e}")
        return False

def main():
    """Main validation function."""
    print("🎯 REAL HIPPORAG2 VALIDATION - TORCHVISION FIX TEST")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing that the MPS-compatible PyTorch fix resolved torchvision issues")
    print("and enabled the real HippoRAG2 algorithm to replace the mock implementation")
    print()
    
    # Test 1: Torchvision fix
    if not test_torchvision_fix():
        print("\n❌ VALIDATION FAILED: Torchvision import error not resolved")
        return False
    
    # Test 2: HippoRAG2 component imports
    if not test_hipporag2_imports():
        print("\n❌ VALIDATION FAILED: HippoRAG2 components cannot be imported")
        return False
    
    # Test 3: Initialize real HippoRAG2
    result = initialize_real_hipporag2()
    if not result:
        print("\n❌ VALIDATION FAILED: Cannot initialize real HippoRAG2 system")
        return False
    
    hipporag2_retriever, data = result
    
    # Test 4: Quality validation with risk-reversal query
    if not test_risk_reversal_query(hipporag2_retriever):
        print("\n❌ VALIDATION FAILED: Quality not improved over mock")
        return False
    
    # Success!
    print("\n" + "="*60)
    print("🎉 VALIDATION SUCCESSFUL!")
    print("✅ Torchvision import error resolved with MPS-compatible PyTorch")
    print("✅ Real HippoRAG2Retriever can be imported and initialized")
    print("✅ Quality improvement over mock implementation demonstrated")
    print("✅ Risk-reversal option query returns relevant financial content")
    print("🚀 Real HippoRAG2 system is operational and ready for use!")
    print("="*60)
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)