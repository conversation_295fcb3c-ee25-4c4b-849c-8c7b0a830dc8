# Segmentation Fault Fix for HippoRAG2

## Problem
Segmentation fault (signal 11) occurred when running `hipporag2_interactive_debug.py` with the large pdf_dataset:
- 63,364 nodes
- 160,795 edges  
- Crash happened after encoding embeddings, during FAISS index building

## Root Cause
The segmentation fault was caused by:
1. **Large batch operations** in FAISS index building
2. **HNSW index complexity** with 64 connections per node
3. **Memory corruption** during vector normalization with large arrays
4. **Missing pre-computed embeddings** forcing fresh computation each time

## Solution Implemented

### 1. Safe Embedding Generator (`generate_pdf_embeddings_safe.py`)
Pre-computes embeddings safely:
- Uses small batch sizes (20 items)
- Implements error handling and fallbacks
- Uses simpler FAISS IndexFlatIP instead of HNSW
- Saves embeddings incrementally
- Adds garbage collection between operations

### 2. Safe Data Loader (`load_pdf_dataset_safe.py`)
Loads pre-computed data:
- Checks for existing embeddings first
- Loads components incrementally
- Provides clear error messages
- Falls back gracefully if files missing

### 3. Safe Interactive Console (`hipporag2_interactive_safe.py`)
Interactive Q&A without crashes:
- Uses the safe loader
- <PERSON><PERSON> errors gracefully
- Simpler interface focused on stability
- All the Q&A functionality without debug complexity

## How to Use

### Step 1: Generate Embeddings Safely (One Time)
```bash
python generate_pdf_embeddings_safe.py
```
This will:
- Compute node embeddings (63,364 nodes)
- Compute edge embeddings (160,795 edges)
- Build FAISS indices safely
- Save everything to `import/pdf_dataset/precompute/`

**Note**: This takes 10-15 minutes but only needs to be done once.

### Step 2: Run Interactive Q&A
```bash
python hipporag2_interactive_safe.py
```
This will:
- Load pre-computed embeddings (fast)
- Initialize HippoRAG2 retriever
- Provide interactive Q&A without crashes

## What Changed

### Before (Crashes)
```python
# Large batch operations
for i in range(0, X.shape[0], 32):
    faiss_index.add(X[i:i+32])
    
# Complex HNSW index
faiss.IndexHNSWFlat(dimension, 64, faiss.METRIC_INNER_PRODUCT)

# No pre-computation
compute_graph_embeddings(node_list, edge_list, ...)  # Every time!
```

### After (Safe)
```python
# Small batch operations with error handling
for i in range(0, X.shape[0], 1):
    try:
        faiss_index.add(X[i:i+1])
    except:
        # Handle error

# Simpler flat index
faiss.IndexFlatIP(dimension)

# Pre-computed embeddings
if embeddings_file.exists():
    load_from_disk()  # Fast!
```

## Files Created

1. **`generate_pdf_embeddings_safe.py`** - Safe embedding generator
2. **`load_pdf_dataset_safe.py`** - Safe data loader  
3. **`hipporag2_interactive_safe.py`** - Safe interactive console

## Pre-computed Files
After running the generator, these files are created in `import/pdf_dataset/precompute/`:
- `pdf_dataset_eventTrueconceptTrue_node_list.pkl`
- `pdf_dataset_eventTrueconceptTrue_edge_list.pkl`
- `pdf_dataset_eventTrueconceptTrue_all-mpnet-base-v2_node_embeddings.pkl`
- `pdf_dataset_eventTrueconceptTrue_all-mpnet-base-v2_edge_embeddings.pkl`
- `pdf_dataset_eventTrueconceptTrue_all-mpnet-base-v2_node_faiss.index`
- `pdf_dataset_eventTrueconceptTrue_all-mpnet-base-v2_edge_faiss.index`

## Memory Usage
With the safe approach:
- Peak memory: ~2-3 GB (vs 10+ GB before)
- No segmentation faults
- Stable operation even on limited RAM

## Tips
1. Always pre-compute embeddings for large datasets
2. Use IndexFlatIP for stability over IndexHNSWFlat for speed
3. Small batch sizes prevent memory spikes
4. Save incrementally to allow recovery from failures