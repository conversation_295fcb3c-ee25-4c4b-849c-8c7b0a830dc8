#!/usr/bin/env python3
"""
Debug Risk Reversal embeddings to find why they're not being retrieved.
"""

import os
import sys
import pickle
import numpy as np
from pathlib import Path

# Set environment and add path
os.environ["TOKENIZERS_PARALLELISM"] = "false"
sys.path.insert(0, str(Path(__file__).parent))

from setup_embedding_model import setup_embedding_model

def main():
    print("🔍 Debugging Risk Reversal Embeddings")
    
    # Load complete data
    data_file = Path("import/pdf_dataset/complete_data.pkl")
    with open(data_file, 'rb') as f:
        complete_data = pickle.load(f)
    
    # Setup embedding model
    embedding_model = setup_embedding_model()
    
    # Known Risk Reversal text IDs from manual search
    risk_reversal_ids = [
        "cdcd632ca440b3caf201d917d6a8ac2f109905b716b570b0eb557b9e753591ff",  # Contains table of contents
        "4eb22e1fee9bad1777d1e0d7493e5ae03450eb9402a4a3f08e638051a9dc2b87"   # Contains figure references
    ]
    
    print(f"\n📊 Data Structure Analysis:")
    print(f"   • Total text_dict entries: {len(complete_data.get('text_dict', {}))}")
    print(f"   • Total text embeddings: {len(complete_data.get('text_embeddings', []))}")
    print(f"   • text_id_list length: {len(complete_data.get('text_id_list', []))}")
    
    # Check if Risk Reversal IDs are in the various data structures
    text_dict = complete_data.get('text_dict', {})
    text_id_list = complete_data.get('text_id_list', [])
    text_embeddings = complete_data.get('text_embeddings', [])
    
    print(f"\n🔍 Risk Reversal Content Analysis:")
    for i, text_id in enumerate(risk_reversal_ids, 1):
        print(f"\n--- Risk Reversal ID #{i} ---")
        print(f"ID: {text_id}")
        
        # Check if in text_dict
        in_text_dict = text_id in text_dict
        print(f"✅ In text_dict: {in_text_dict}")
        
        if in_text_dict:
            content = text_dict[text_id]
            risk_keywords = ['risk reversal', 'Risk Reversal', 'zero cost', 'Zero Cost', 'strategy option', 'Strategy option']
            found_keywords = [kw for kw in risk_keywords if kw in content]
            print(f"📝 Content preview: {content[:200]}...")
            print(f"🎯 Risk keywords found: {found_keywords}")
        
        # Check if in text_id_list (for embeddings)
        in_id_list = text_id in text_id_list
        print(f"📍 In text_id_list: {in_id_list}")
        
        if in_id_list:
            id_index = text_id_list.index(text_id)
            print(f"📊 Index in text_id_list: {id_index}")
            
            # Check if has embedding
            if id_index < len(text_embeddings):
                embedding = text_embeddings[id_index]
                print(f"🔢 Has embedding: {embedding.shape if hasattr(embedding, 'shape') else type(embedding)}")
                
                # Test similarity with Risk Reversal query
                query = "Risk Reversal strategy FX option"
                query_embedding = embedding_model.encode([query])[0]
                
                if hasattr(embedding, 'shape') and len(embedding.shape) > 0:
                    similarity = np.dot(query_embedding, embedding) / (np.linalg.norm(query_embedding) * np.linalg.norm(embedding))
                    print(f"🎯 Similarity with '{query}': {similarity:.4f}")
                else:
                    print(f"❌ Invalid embedding format: {type(embedding)}")
            else:
                print(f"❌ No embedding at index {id_index} (embeddings length: {len(text_embeddings)})")
    
    # Test direct FAISS search
    print(f"\n🔍 Direct FAISS Search Test:")
    text_index = complete_data.get('text_index')
    if text_index is not None:
        query = "Risk Reversal strategy FX option"
        query_embedding = embedding_model.encode([query])[0].reshape(1, -1)
        
        # Search for top 20 results
        distances, indices = text_index.search(query_embedding, 20)
        
        print(f"📊 FAISS search results for '{query}':")
        for i, (dist, idx) in enumerate(zip(distances[0], indices[0]), 1):
            if idx < len(text_id_list):
                text_id = text_id_list[idx]
                similarity = 1 - dist  # Convert distance to similarity
                
                # Check if this is one of our target IDs
                is_target = text_id in risk_reversal_ids
                marker = "🎯" if is_target else "  "
                
                print(f"{marker} #{i:2d}: Similarity={similarity:.4f}, ID={text_id[:16]}...")
                
                if is_target:
                    content = text_dict.get(text_id, "")
                    print(f"      📝 FOUND TARGET! Content: {content[:100]}...")
    else:
        print("❌ No text_index found in data")
    
    print(f"\n{'='*60}")
    print("🎯 SUMMARY:")
    print("• Risk Reversal content exists in text_dict ✅")
    print("• Checking if content is properly indexed and embedded")
    print("• Testing direct FAISS similarity search")
    print("• This reveals why HippoRAG2 retrieval is failing")

if __name__ == "__main__":
    main()