#!/usr/bin/env python3
"""
Debug script to trace HippoRAG2 retrieval process for MMC scenario queries.
This will help identify why MMC content ranks poorly despite being present.
"""

import json
import logging
import numpy as np
import networkx as nx
from pathlib import Path
import sys

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
from atlas_rag.llm_generator.llm_generator import LLMGenerator
from atlas_rag.retriever.inference_config import InferenceConfig
from hipporag2_pipeline import HippoRAG2Pipeline

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_kg_data():
    """Load the knowledge graph data using HippoRAG2Pipeline."""
    try:
        # Use the pipeline to load existing data
        pipeline = HippoRAG2Pipeline(data_directory="import/pdf_dataset")
        data = pipeline.load_existing_data()
        
        print(f"✅ Loaded KG data with {len(data['node_list'])} nodes and {len(data['text_dict'])} text passages")
        return data, pipeline
    except Exception as e:
        print(f"❌ Error loading KG data: {e}")
        return None, None

def analyze_mmc_entities(kg_data):
    """Analyze MMC-related entities in the knowledge graph."""
    KG = kg_data['KG']
    node_list = kg_data['node_list']
    
    print("\n🔍 ANALYZING MMC-RELATED ENTITIES")
    print("=" * 50)
    
    # Find MMC-related entities
    mmc_entities = []
    scenario_entities = []
    
    for node_id in node_list:
        node_data = KG.nodes[node_id]
        entity_name = node_data.get('id', '').lower()
        
        if 'mmc' in entity_name or 'market maker cockpit' in entity_name:
            mmc_entities.append((node_id, node_data))
            
        if 'scenario' in entity_name:
            scenario_entities.append((node_id, node_data))
    
    print(f"Found {len(mmc_entities)} MMC-related entities:")
    for node_id, node_data in mmc_entities[:10]:  # Show first 10
        print(f"  - {node_id}: {node_data.get('id', 'N/A')}")
    
    print(f"\nFound {len(scenario_entities)} scenario-related entities:")
    for node_id, node_data in scenario_entities[:10]:  # Show first 10
        print(f"  - {node_id}: {node_data.get('id', 'N/A')}")
    
    return mmc_entities, scenario_entities

def analyze_text_node_133(kg_data):
    """Analyze the specific text node 133 that contains MMC scenario content."""
    KG = kg_data['KG']
    text_dict = kg_data['text_dict']
    
    print("\n🔍 ANALYZING TEXT NODE 133 (MMC SCENARIO CONTENT)")
    print("=" * 50)
    
    # Check if text node 133 exists
    if 133 in text_dict:
        content = text_dict[133]
        print(f"✅ Text node 133 found")
        print(f"Content length: {len(content)} characters")
        print(f"Content preview: {content[:200]}...")
        
        # Find connected entities
        connected_entities = []
        if 133 in KG.nodes:
            neighbors = list(KG.neighbors(133))
            print(f"Direct neighbors: {len(neighbors)}")
            
            for neighbor in neighbors[:10]:  # Show first 10
                if neighbor in KG.nodes:
                    neighbor_data = KG.nodes[neighbor]
                    connected_entities.append((neighbor, neighbor_data.get('id', 'N/A')))
            
            print("Connected entities:")
            for node_id, entity_name in connected_entities:
                print(f"  - {node_id}: {entity_name}")
        
        return True
    else:
        print("❌ Text node 133 not found in text_dict")
        return False

def debug_entity_matching(retriever, query):
    """Debug the entity matching process for the query."""
    print(f"\n🔍 DEBUGGING ENTITY MATCHING FOR QUERY: '{query}'")
    print("=" * 50)
    
    # Step 1: NER extraction
    try:
        entities = retriever.ner(query)
        entities_list = entities.split(", ") if entities else []
        print(f"NER extracted entities: {entities_list}")
    except Exception as e:
        print(f"❌ NER extraction failed: {e}")
        entities_list = [query]
        print(f"Fallback to query as entity: {entities_list}")
    
    # Step 2: Entity to node matching
    node_name_list = [retriever.KG.nodes[node]["id"] for node in retriever.node_list]
    print(f"Total entities in KG: {len(node_name_list)}")
    
    matched_entities = []
    for entity in entities_list:
        if entity in node_name_list:
            index = node_name_list.index(entity)
            node_id = retriever.node_list[index]
            matched_entities.append((entity, node_id))
            print(f"✅ Direct match found: '{entity}' -> node {node_id}")
        else:
            print(f"❌ No direct match for: '{entity}'")
            
            # Try semantic similarity search
            try:
                entity_embedding = retriever.sentence_encoder.encode([entity], query_type="search")
                scores = retriever.node_embeddings @ entity_embedding[0].T
                top_indices = np.argsort(scores)[-5:][::-1]  # Top 5 similar
                
                print(f"Top 5 semantically similar entities for '{entity}':")
                for i, idx in enumerate(top_indices):
                    node_id = retriever.node_list[idx]
                    entity_name = retriever.KG.nodes[node_id]["id"]
                    score = scores[idx]
                    print(f"  {i+1}. {entity_name} (score: {score:.4f}, node: {node_id})")
                    
            except Exception as e:
                print(f"❌ Semantic similarity search failed: {e}")
    
    return matched_entities

def debug_query2edge_process(retriever, query):
    """Debug the query2edge process in detail."""
    print(f"\n🔍 DEBUGGING QUERY2EDGE PROCESS FOR: '{query}'")
    print("=" * 50)
    
    try:
        # Get query embedding
        query_emb = retriever.sentence_encoder.encode([query], query_type="edge")
        print(f"✅ Query embedding generated: shape {query_emb[0].shape}")
        
        # Calculate edge similarities
        raw_scores = retriever.edge_embeddings @ query_emb[0].T
        scores = retriever.min_max_normalize(raw_scores) if hasattr(retriever, 'min_max_normalize') else raw_scores
        
        print(f"Edge similarity scores calculated: {len(scores)} edges")
        print(f"Score range: {np.min(scores):.4f} to {np.max(scores):.4f}")
        
        # Get top 20 edges for analysis
        top_indices = np.argsort(scores)[-20:][::-1]
        print(f"\nTop 20 most similar edges:")
        
        mmc_related_edges = []
        scenario_related_edges = []
        
        for i, idx in enumerate(top_indices):
            edge = retriever.edge_list[idx]
            head_entity = retriever.KG.nodes[edge[0]]['id']
            relation = retriever.KG.edges[edge]['relation']
            tail_entity = retriever.KG.nodes[edge[1]]['id']
            score = scores[idx]
            
            edge_str = f"{head_entity} -> {relation} -> {tail_entity}"
            print(f"  {i+1:2d}. {edge_str} (score: {score:.4f})")
            
            # Check for MMC/scenario relevance
            if 'mmc' in edge_str.lower() or 'market maker cockpit' in edge_str.lower():
                mmc_related_edges.append((edge, score, edge_str))
            if 'scenario' in edge_str.lower():
                scenario_related_edges.append((edge, score, edge_str))
        
        print(f"\n🎯 Found {len(mmc_related_edges)} MMC-related edges in top 20")
        print(f"🎯 Found {len(scenario_related_edges)} scenario-related edges in top 20")
        
        return top_indices, scores, mmc_related_edges, scenario_related_edges
        
    except Exception as e:
        print(f"❌ Query2edge process failed: {e}")
        return None, None, [], []

def debug_pagerank_propagation(retriever, query):
    """Debug the PageRank propagation process."""
    print(f"\n🔍 DEBUGGING PAGERANK PROPAGATION FOR: '{query}'")
    print("=" * 50)
    
    try:
        # Get personalization dictionaries
        node_dict, text_dict = retriever.retrieve_personalization_dict(query)
        
        print(f"Node personalization: {len(node_dict)} nodes")
        print(f"Text personalization: {len(text_dict)} passages")
        
        if node_dict:
            print("Top personalized nodes:")
            sorted_nodes = sorted(node_dict.items(), key=lambda x: x[1], reverse=True)
            for node_id, score in sorted_nodes[:10]:
                entity_name = retriever.KG.nodes[node_id]['id']
                print(f"  - {entity_name} (node {node_id}): {score:.4f}")
        
        # Combine personalization
        personalization_dict = {}
        personalization_dict.update(node_dict)
        personalization_dict.update(text_dict)
        
        print(f"Total personalization nodes: {len(personalization_dict)}")
        
        # Run PageRank
        pr = nx.pagerank(retriever.KG, personalization=personalization_dict, 
                        alpha=retriever.inference_config.ppr_alpha, 
                        max_iter=retriever.inference_config.ppr_max_iter, 
                        tol=retriever.inference_config.ppr_tol)
        
        # Analyze text passage scores
        text_scores = {}
        for text_id in retriever.text_id_list:
            if pr[text_id] > 0.0:
                text_scores[text_id] = pr[text_id]
        
        print(f"Text passages with non-zero PageRank: {len(text_scores)}")
        
        # Check specifically for node 133
        if 133 in text_scores:
            rank_133 = text_scores[133]
            sorted_scores = sorted(text_scores.items(), key=lambda x: x[1], reverse=True)
            rank_position = next((i for i, (text_id, _) in enumerate(sorted_scores) if text_id == 133), -1)
            print(f"🎯 Text node 133 PageRank score: {rank_133:.6f} (rank: {rank_position + 1}/{len(sorted_scores)})")
        else:
            print("❌ Text node 133 has zero PageRank score")
        
        # Show top 10 text passages
        print("\nTop 10 text passages by PageRank:")
        sorted_scores = sorted(text_scores.items(), key=lambda x: x[1], reverse=True)
        for i, (text_id, score) in enumerate(sorted_scores[:10]):
            content_preview = retriever.passage_dict[text_id][:100].replace('\n', ' ')
            print(f"  {i+1:2d}. Text {text_id}: {score:.6f} - {content_preview}...")
        
        return pr, text_scores
        
    except Exception as e:
        print(f"❌ PageRank propagation failed: {e}")
        return None, None

def run_full_retrieval_debug(retriever, query):
    """Run the full retrieval process and analyze results."""
    print(f"\n🔍 RUNNING FULL RETRIEVAL DEBUG FOR: '{query}'")
    print("=" * 50)
    
    try:
        # Run retrieval
        contents, passage_ids = retriever.retrieve(query, topN=10)
        
        print(f"Retrieved {len(contents)} passages:")
        for i, (content, passage_id) in enumerate(zip(contents, passage_ids)):
            content_preview = content[:150].replace('\n', ' ')
            print(f"  {i+1:2d}. Passage {passage_id}: {content_preview}...")
            
            # Check if this is MMC-related content
            if 'mmc' in content.lower() or 'market maker cockpit' in content.lower() or 'scenario' in content.lower():
                print(f"      🎯 MMC/Scenario related content found!")
        
        return contents, passage_ids
        
    except Exception as e:
        print(f"❌ Full retrieval failed: {e}")
        return None, None

def main():
    print("🚀 STARTING HIPPORAG2 MMC RETRIEVAL DEBUGGING")
    print("=" * 60)
    
    # Load data
    kg_data, pipeline = load_kg_data()
    if not kg_data:
        return
    
    # Analyze MMC entities and text node 133
    mmc_entities, scenario_entities = analyze_mmc_entities(kg_data)
    text_133_exists = analyze_text_node_133(kg_data)
    
    if not text_133_exists:
        print("❌ Cannot proceed - text node 133 not found")
        return
    
    # Initialize components using pipeline
    try:
        print("\n🔧 INITIALIZING COMPONENTS USING PIPELINE")
        print("=" * 30)
        
        # Setup models via pipeline
        pipeline.setup_models()
        print("✅ Pipeline models initialized")
        
        # Store data for retriever use
        pipeline.data = kg_data
        
        # Initialize HippoRAG2
        pipeline.initialize_hipporag2()
        print("✅ HippoRAG2 retriever initialized via pipeline")
        
        # Get the retriever for detailed debugging
        retriever = pipeline.hipporag2_retriever
        
    except Exception as e:
        print(f"❌ Component initialization failed: {e}")
        return
    
    # Test queries
    test_queries = [
        "how to create scenarios in MMC?",
        "MMC scenario creation",
        "Market Maker Cockpit scenarios",
        "scenario MMC"
    ]
    
    for query in test_queries:
        print(f"\n{'='*60}")
        print(f"DEBUGGING QUERY: '{query}'")
        print(f"{'='*60}")
        
        # Step-by-step debugging
        matched_entities = debug_entity_matching(retriever, query)
        top_edges, edge_scores, mmc_edges, scenario_edges = debug_query2edge_process(retriever, query)
        pr_scores, text_scores = debug_pagerank_propagation(retriever, query)
        contents, passage_ids = run_full_retrieval_debug(retriever, query)
        
        print(f"\n📊 QUERY SUMMARY FOR: '{query}'")
        print("-" * 40)
        print(f"Matched entities: {len(matched_entities)}")
        print(f"MMC-related edges in top 20: {len(mmc_edges)}")
        print(f"Scenario-related edges in top 20: {len(scenario_edges)}")
        print(f"Retrieved passages: {len(contents) if contents else 0}")
        
        # Check if any retrieved content is MMC-related
        mmc_content_found = False
        if contents:
            for content in contents:
                if 'mmc' in content.lower() or 'market maker cockpit' in content.lower():
                    mmc_content_found = True
                    break
        
        print(f"MMC content in results: {'✅ Yes' if mmc_content_found else '❌ No'}")

if __name__ == "__main__":
    main()