#!/usr/bin/env python3
"""
Debug PageRank Score Propagation

Investigates why MMC entities with high similarity scores aren't 
propagating their scores to connected text passages in HippoRAG2.

This addresses the core issue: entities are found and connected,
but PageRank isn't properly reaching the final text results.
"""

import numpy as np
from hipporag2_pipeline import HippoRAG2Pipeline
from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever

def debug_query_entity_matching(pipeline, query="how to create scenarios in MMC?"):
    """Debug which entities are matched by the query embedding."""
    print("🔍 DEBUGGING ENTITY MATCHING")
    print("="*50)
    
    # Get query embedding
    query_embedding = pipeline.sentence_encoder.encode([query])
    print(f"Query: {query}")
    print(f"Query embedding shape: {query_embedding.shape}")
    
    # Find top entity matches using cosine similarity
    from sklearn.metrics.pairwise import cosine_similarity
    
    # Get entity embeddings (node embeddings)
    node_embeddings = pipeline.data["node_embeddings"]
    node_list = pipeline.data["node_list"]
    
    # Compute similarities
    similarities = cosine_similarity(query_embedding, node_embeddings)[0]
    
    # Get top matches
    top_indices = np.argsort(similarities)[::-1][:20]  # Top 20
    
    print(f"\n📊 TOP ENTITY MATCHES:")
    mmc_entities_found = []
    
    for i, idx in enumerate(top_indices):
        if idx < len(node_list):
            node_id = node_list[idx]
            similarity = similarities[idx]
            
            # Get node data
            node_data = pipeline.data["KG"].nodes.get(node_id, {})
            node_name = node_data.get('name', node_id)
            node_type = node_data.get('type', 'unknown')
            
            print(f"   {i+1:2d}. Score: {similarity:.4f} | Type: {node_type:8s} | {node_name}")
            
            # Check if MMC-related
            if any(term in str(node_name).lower() for term in ['mmc', 'market maker cockpit', 'cockpit']):
                mmc_entities_found.append((node_id, node_name, similarity))
                print(f"       🎯 MMC ENTITY DETECTED!")
    
    print(f"\n📈 MMC ENTITY SUMMARY:")
    print(f"   Found {len(mmc_entities_found)} MMC entities in top 20 matches")
    
    return mmc_entities_found

def debug_entity_connections(pipeline, mmc_entities):
    """Debug connections from MMC entities to text passages."""
    print(f"\n🔗 DEBUGGING ENTITY-TEXT CONNECTIONS")
    print("="*50)
    
    edge_list = pipeline.data["edge_list"]
    graph = pipeline.data["KG"]
    
    for entity_id, entity_name, similarity in mmc_entities[:5]:  # Top 5 MMC entities
        print(f"\n🎯 Entity: {entity_name} (Score: {similarity:.4f})")
        
        # Find outgoing edges from this entity
        connected_passages = []
        for edge in edge_list:
            source, target = edge
            if source == entity_id:
                target_data = graph.nodes.get(target, {})
                target_type = target_data.get('type', 'unknown')
                target_name = target_data.get('name', target)
                
                if target_type in ['passage', 'text']:
                    connected_passages.append((target, target_name, target_type))
        
        print(f"   Connected to {len(connected_passages)} text passages:")
        for passage_id, passage_name, passage_type in connected_passages[:3]:  # Show first 3
            passage_preview = str(passage_name)[:100] + "..." if len(str(passage_name)) > 100 else str(passage_name)
            print(f"      → {passage_type}: {passage_preview}")
    
    return True

def debug_pagerank_propagation(pipeline, query="how to create scenarios in MMC?"):
    """Debug the PageRank propagation process."""
    print(f"\n📊 DEBUGGING PAGERANK PROPAGATION")
    print("="*50)
    
    try:
        # Get the HippoRAG2 retriever
        retriever = pipeline.hipporag2_retriever
        
        # Access internal retrieval steps (if possible)
        print("🔄 Executing detailed retrieval analysis...")
        
        # Step 1: Get query embedding
        query_embedding = pipeline.sentence_encoder.encode([query])
        print(f"   Query embedding: {query_embedding.shape}")
        
        # Step 2: Find entity matches
        print("   Finding entity matches...")
        # This would require accessing HippoRAG2 internal methods
        
        # Step 3: Run PersonalizedPageRank
        print("   Running PersonalizedPageRank...")
        # This would require accessing the NetworkX graph and PPR results
        
        # For now, let's use the standard retrieve method and analyze results
        results = retriever.retrieve(query)
        
        print(f"\n📋 RETRIEVAL PIPELINE RESULTS:")
        print(f"   Total results: {len(results)}")
        
        for i, result in enumerate(results[:3]):
            print(f"\n   Result {i+1}:")
            
            # Handle different result formats
            if isinstance(result, dict):
                score = result.get('score', 'N/A')
                content = result.get('content', result.get('text', 'No content'))
            elif isinstance(result, list) and len(result) >= 2:
                score = result[0] if isinstance(result[0], (int, float)) else 'N/A'
                content = result[1] if len(result) > 1 else str(result)
            else:
                content = str(result)
                score = 'N/A'
            
            content_preview = str(content)[:150] + "..." if len(str(content)) > 150 else str(content)
            print(f"      Score: {score}")
            print(f"      Content: {content_preview}")
            
            # Analyze content type
            content_lower = str(content).lower()
            if any(term in content_lower for term in ['mmc', 'market maker cockpit', 'cockpit']):
                print(f"      ✅ MMC-relevant content")
            elif any(term in content_lower for term in ['fix protocol', 'fix message']):
                print(f"      ⚠️  FIX protocol content")
            else:
                print(f"      ❓ Other content")
        
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ PageRank debugging failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_text_dictionary(pipeline):
    """Debug the text dictionary to see what text is available."""
    print(f"\n📖 DEBUGGING TEXT DICTIONARY")
    print("="*50)
    
    text_dict = pipeline.data["text_dict"]
    print(f"   Total text entries: {len(text_dict)}")
    
    # Look for MMC-related text
    mmc_texts = []
    for node_id, text_content in text_dict.items():
        if any(term in str(text_content).lower() for term in ['mmc', 'market maker cockpit', 'cockpit']):
            mmc_texts.append((node_id, text_content))
    
    print(f"   MMC-related text entries: {len(mmc_texts)}")
    
    print(f"\n📋 SAMPLE MMC TEXT ENTRIES:")
    for i, (node_id, text_content) in enumerate(mmc_texts[:3]):
        text_preview = str(text_content)[:200] + "..." if len(str(text_content)) > 200 else str(text_content)
        print(f"   {i+1}. Node {node_id}: {text_preview}")
    
    return len(mmc_texts) > 0

def main():
    """Main debugging function."""
    print("🔬 PAGERANK PROPAGATION DEBUG")
    print("="*80)
    
    try:
        # Initialize pipeline
        pipeline = HippoRAG2Pipeline()
        data = pipeline.load_existing_data()
        pipeline.setup_models()
        pipeline.data = data
        pipeline.initialize_hipporag2()
        
        query = "how to create scenarios in MMC?"
        
        # Debug entity matching
        mmc_entities = debug_query_entity_matching(pipeline, query)
        
        # Debug entity connections
        if mmc_entities:
            debug_entity_connections(pipeline, mmc_entities)
        
        # Debug text dictionary
        has_mmc_text = debug_text_dictionary(pipeline)
        
        # Debug PageRank propagation
        retrieval_success = debug_pagerank_propagation(pipeline, query)
        
        # Summary
        print(f"\n" + "="*80)
        print(f"🏁 DEBUG SUMMARY")
        print(f"="*80)
        print(f"   MMC entities found in query matching: {len(mmc_entities)}")
        print(f"   MMC text available in dictionary: {'✅' if has_mmc_text else '❌'}")
        print(f"   Retrieval execution: {'✅' if retrieval_success else '❌'}")
        
        if len(mmc_entities) > 0 and has_mmc_text and retrieval_success:
            print(f"\n🤔 DIAGNOSIS: All components working, issue likely in PageRank propagation")
            print(f"   MMC entities are found and connected, but scores aren't reaching text passages")
        elif len(mmc_entities) == 0:
            print(f"\n🤔 DIAGNOSIS: Query not matching MMC entities effectively")
        elif not has_mmc_text:
            print(f"\n🤔 DIAGNOSIS: MMC text content not available in text dictionary")
        else:
            print(f"\n🤔 DIAGNOSIS: Retrieval system issue")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)