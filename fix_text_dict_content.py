#!/usr/bin/env python3
"""
Fix text_dict to contain actual passage text instead of node IDs.
"""

import os
import pickle
import networkx as nx
from pathlib import Path
from tqdm import tqdm

# Set environment variables
os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'

def fix_text_dict():
    """Fix text_dict to contain actual passage text."""
    
    print("=" * 70)
    print("🔧 Fixing text_dict Content")
    print("=" * 70)
    
    # Load GraphML
    graph_file = Path("import/pdf_dataset/kg_graphml/pdf_dataset_graph.graphml")
    
    if not graph_file.exists():
        print(f"❌ GraphML not found: {graph_file}")
        return False
    
    print(f"\n📊 Loading GraphML...")
    with open(graph_file, 'rb') as f:
        KG = nx.read_graphml(f)
    
    print(f"   Loaded {len(KG.nodes)} nodes")
    
    # Find passage nodes and extract text
    print("\n🔍 Finding passage nodes...")
    
    text_dict = {}
    passage_count = 0
    sample_texts = []
    
    for node_id in tqdm(KG.nodes, desc="Processing nodes"):
        node_data = KG.nodes[node_id]
        
        # Check if this is a passage node
        if "passage" in node_data.get("type", "").lower():
            passage_count += 1
            
            # Get the actual text content
            # Try different possible attribute names
            text_content = None
            
            # Try 'content' attribute
            if "content" in node_data:
                text_content = node_data["content"]
            # Try 'text' attribute
            elif "text" in node_data:
                text_content = node_data["text"]
            # Try 'id' attribute (might contain text for passage nodes)
            elif "id" in node_data:
                # Check if id looks like text (longer than typical hash)
                if len(node_data["id"]) > 100:
                    text_content = node_data["id"]
            
            if text_content:
                text_dict[node_id] = text_content
                
                if len(sample_texts) < 5:
                    sample_texts.append((node_id, text_content))
            else:
                # Store node_id as fallback
                text_dict[node_id] = node_id
    
    print(f"\n✅ Found {passage_count} passage nodes")
    print(f"   Text extracted for {len(text_dict)} nodes")
    
    # Show samples
    if sample_texts:
        print("\n📄 Sample passage texts:")
        for i, (node_id, text) in enumerate(sample_texts, 1):
            print(f"\n{i}. Node: {node_id[:50]}...")
            preview = text[:300] + "..." if len(text) > 300 else text
            print(f"   Text: {preview}")
    
    # Search for specific terms
    print("\n🔍 Checking for specific content:")
    search_terms = ["resting order", "Supersonic", "OCO", "risk reversal", "Market Maker"]
    
    for term in search_terms:
        count = 0
        for text in text_dict.values():
            if isinstance(text, str) and term.lower() in text.lower():
                count += 1
        print(f"  '{term}': {count} passages")
    
    # Save the corrected text_dict
    if len(text_dict) > 0:
        output_file = Path("import/pdf_dataset/precompute/pdf_dataset_corrected_text_dict.pkl")
        print(f"\n💾 Saving corrected text_dict to {output_file}")
        
        with open(output_file, 'wb') as f:
            pickle.dump(text_dict, f)
        
        print("✅ Corrected text_dict saved successfully")
        
        # Also check if we need to find text from other CSV files
        print("\n🔍 Checking for text CSV files...")
        csv_dir = Path("import/pdf_dataset/triples_csv")
        if csv_dir.exists():
            text_csv = csv_dir / "text_nodes__from_json.csv"
            if text_csv.exists():
                print(f"   Found: {text_csv}")
                
                import pandas as pd
                df = pd.read_csv(text_csv)
                print(f"   CSV has {len(df)} rows")
                print(f"   Columns: {list(df.columns)}")
                
                if len(df) > 0:
                    print("\n   Sample row:")
                    print(df.iloc[0])
        
        return True
    else:
        print("❌ No text content found in GraphML")
        return False

def main():
    """Main function."""
    success = fix_text_dict()
    
    if success:
        print("\n✅ Text dict fixed! You may need to update scripts to use the corrected version.")
    else:
        print("\n❌ Failed to fix text dict. Manual investigation needed.")

if __name__ == "__main__":
    main()