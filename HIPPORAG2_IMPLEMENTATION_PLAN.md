# HippoRAG2 Implementation Plan for AutoSchemaKG

## Executive Summary
Implement HippoRAG2 integration for the AutoSchemaKG project using existing PDF extraction and concept generation outputs. The implementation will use OLLAMA backend with qwen3:30b-a3b-instruct-2507-q4_K_M and Qwen3-Embedding-4B, targeting the autoschemakg Neo4j database.

## Current State Analysis ✅

### Validated Existing Components:
1. **Pipeline Outputs** (import/pdf_dataset/):
   - ✅ GraphML files: `knowledge_graph.graphml`, `knowledge_graph.pkl`
   - ✅ CSV files: Triple nodes/edges, text nodes/edges, concept mappings
   - ✅ Embeddings: Node, edge, and text embeddings with FAISS indexes
   - ✅ Vector indexes: FAISS indexes for efficient similarity search

2. **Configuration** (config.ini):
   - ✅ OLLAMA_BASE_URL: http://localhost:11434
   - ✅ OLLAMA_MODEL: qwen3:30b-a3b-instruct-2507-q4_K_M  
   - ✅ EMBEDDING_MODEL: Qwen/Qwen3-Embedding-4B
   - ✅ Neo4j: autoschemakg database at bolt://localhost:7687

3. **Available Components**:
   - ✅ OLLAMA generator: _OllamaGenerator in pdf_kg_extraction_pipeline_robust.py
   - ✅ Embedding setup: setup_qwen_embedding_model() in setup_embedding_model.py
   - ✅ HippoRAG2Retriever: atlas_rag.retriever.hipporag2
   - ✅ Graph indexing: create_embeddings_and_index()

## Implementation Steps

### Phase 1: Setup Functions
**Task**: Create OLLAMA-compatible setup functions that match the existing Gemini interface.

**Files to Create**:
1. `setup_ollama_llm_generator.py` - OLLAMA LLM generator setup
2. `hipporag2_pipeline.py` - Main integration script

**Requirements**:
- Use existing _OllamaGenerator from pdf_kg_extraction_pipeline_robust.py
- Ensure compatibility with LLMGenerator interface expected by HippoRAG2Retriever
- Configure from config.ini settings

### Phase 2: Data Preparation
**Task**: Prepare existing pipeline outputs for HippoRAG2 compatibility.

**Input Files** (import/pdf_dataset/):
- `kg_graphml/knowledge_graph.graphml` - NetworkX graph structure
- `vector_index/*.npy` - Embedding arrays
- `vector_index/*.index` - FAISS indexes
- `triples_csv/*.csv` - Node and edge CSV files

**Output Structure Expected by HippoRAG2**:
```python
{
    "KG": networkx_graph,           # From knowledge_graph.graphml
    "node_embeddings": np.array,    # From vector_index/
    "edge_embeddings": np.array,    # From vector_index/
    "text_embeddings": np.array,    # From vector_index/
    "node_list": list,              # From CSV files
    "edge_list": list,              # From CSV files  
    "edge_faiss_index": faiss_index, # From vector_index/
    "text_dict": dict               # From text CSV files
}
```

### Phase 3: HippoRAG2 Integration
**Task**: Create the main HippoRAG2 pipeline script.

**Core Functionality**:
1. **Data Loading**: Load existing GraphML, embeddings, and FAISS indexes
2. **Model Setup**: Initialize OLLAMA LLM and Qwen3-Embedding-4B
3. **HippoRAG2 Initialization**: Configure HippoRAG2Retriever with loaded data
4. **Query Interface**: Implement Q&A interface for knowledge graph interrogation

**Key Methods**:
- `load_existing_data()` - Load pipeline outputs into HippoRAG2 format
- `setup_models()` - Configure OLLAMA and embedding models
- `initialize_hipporag2()` - Setup HippoRAG2Retriever
- `query_knowledge_graph()` - Interactive Q&A interface

### Phase 4: Testing & Validation
**Task**: Comprehensive testing using existing test patterns.

**Test Categories**:
1. **Unit Tests**: Individual component functionality
2. **Integration Tests**: End-to-end pipeline testing
3. **Performance Tests**: Query response time and accuracy
4. **Configuration Tests**: OLLAMA and embedding model setup

**Test Files to Create**:
- `test_hipporag2_integration.py` - Integration-specific tests
- `test_ollama_setup.py` - OLLAMA configuration tests

## Detailed Implementation

### File 1: setup_ollama_llm_generator.py
```python
"""
OLLAMA LLM Generator Setup for HippoRAG2 Integration
Compatible with HippoRAG2Retriever interface requirements.
"""

from pdf_kg_extraction_pipeline_robust import _OllamaGenerator
from configparser import ConfigParser
from typing import Optional
import os

def setup_ollama_llm_generator() -> _OllamaGenerator:
    # Load config and setup OLLAMA generator
    # Return configured generator compatible with HippoRAG2
```

### File 2: hipporag2_pipeline.py  
```python
"""
HippoRAG2 Pipeline for AutoSchemaKG
Integrates existing PDF extraction outputs with HippoRAG2 for knowledge graph Q&A.
"""

from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
from atlas_rag.vectorstore.create_graph_index import create_embeddings_and_index
from setup_ollama_llm_generator import setup_ollama_llm_generator
from setup_embedding_model import setup_qwen_embedding_model
import networkx as nx
import numpy as np
import os

class HippoRAG2Pipeline:
    def __init__(self, data_directory: str = "import/pdf_dataset"):
        # Initialize pipeline with existing data
        
    def load_existing_data(self) -> dict:
        # Load GraphML, embeddings, FAISS indexes from existing outputs
        
    def setup_models(self):
        # Setup OLLAMA LLM and Qwen3-Embedding-4B
        
    def initialize_hipporag2(self):
        # Configure HippoRAG2Retriever with loaded data
        
    def query_knowledge_graph(self, question: str, topN: int = 3):
        # Interactive Q&A interface
        
    def run_interactive_mode(self):
        # Command-line interface for knowledge graph queries
```

### File 3: test_hipporag2_integration.py
```python
"""
Integration tests for HippoRAG2 pipeline with OLLAMA backend.
"""

import pytest
from hipporag2_pipeline import HippoRAG2Pipeline
from setup_ollama_llm_generator import setup_ollama_llm_generator

class TestHippoRAG2Integration:
    def test_pipeline_initialization(self):
        # Test pipeline setup with existing data
        
    def test_ollama_llm_setup(self):
        # Test OLLAMA generator configuration
        
    def test_data_loading(self):
        # Test loading of existing pipeline outputs
        
    def test_hipporag2_query(self):
        # Test end-to-end query functionality
```

## Success Criteria

### Functional Requirements:
1. ✅ Load existing PDF extraction outputs (GraphML, embeddings, FAISS indexes)
2. ✅ Configure OLLAMA backend with qwen3:30b-a3b-instruct-2507-q4_K_M
3. ✅ Setup Qwen3-Embedding-4B for embeddings
4. ✅ Initialize HippoRAG2Retriever with AutoSchemaKG data
5. ✅ Provide interactive Q&A interface for knowledge graph queries
6. ✅ Support multi-hop reasoning via personalized PageRank
7. ✅ Maintain compatibility with existing AutoSchemaKG architecture

### Performance Requirements:
1. Query response time: < 10 seconds for complex multi-hop queries
2. Embedding compatibility: Consistent with existing vector indexes
3. Memory efficiency: Handle large knowledge graphs (1M+ nodes/edges)
4. Configuration flexibility: Easy switching between backends

### Testing Requirements:
1. Unit test coverage: > 90% for new components
2. Integration test coverage: All major workflows
3. Performance benchmarks: Query latency and accuracy metrics
4. Configuration validation: All config.ini settings verified

## Risk Mitigation

### Technical Risks:
1. **OLLAMA Compatibility**: Extensive testing with existing _OllamaGenerator
2. **Data Format Compatibility**: Validation of existing pipeline outputs
3. **Memory Constraints**: Batch processing for large embeddings
4. **Model Performance**: Fallback to smaller models if needed

### Implementation Risks:
1. **Interface Compatibility**: Follow existing HippoRAG2Retriever patterns
2. **Configuration Management**: Comprehensive config.ini validation
3. **Error Handling**: Robust error handling and recovery mechanisms
4. **Documentation**: Clear usage instructions and examples

## Next Steps

1. **Immediate**: Create setup_ollama_llm_generator.py
2. **Phase 1**: Implement hipporag2_pipeline.py core functionality
3. **Phase 2**: Add data loading and model setup methods
4. **Phase 3**: Implement HippoRAG2 integration and query interface
5. **Phase 4**: Create comprehensive test suite
6. **Phase 5**: Performance optimization and validation

This implementation leverages all existing AutoSchemaKG components while adding OLLAMA backend support and HippoRAG2 integration as requested.