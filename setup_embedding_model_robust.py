"""
Robust setup script for embedding models with fallback options.
This script handles memory issues and device compatibility problems.
"""

import os
import torch
from sentence_transformers import SentenceTransformer
from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
from configparser import ConfigParser
import gc

def setup_robust_embedding_model():
    """
    Initialize embedding model with robust fallback strategy.
    Uses the main setup_embedding_model() function with additional robustness.
    
    Returns:
        SentenceEmbedding: Configured embedding model wrapper
    """
    
    print("🔧 Setting up robust embedding model...")
    
    # Try the main setup function first
    try:
        from setup_embedding_model import setup_embedding_model
        sentence_encoder = setup_embedding_model()
        print("✅ Main embedding model setup successful")
        return sentence_encoder
    except Exception as e:
        print(f"⚠️  Main embedding model setup failed: {e}")
        print("🔄 Falling back to robust implementation...")
    
    # Fallback to robust implementation
    # Load configuration
    config = ConfigParser()
    config.read('config.ini')
    
    # Get embedding model name from config
    embedding_model_name = config.get('settings', 'EMBEDDING_MODEL', fallback='all-MiniLM-L6-v2')
    print(f"Target embedding model: {embedding_model_name}")
    
    # Determine device with fallback strategy
    device = determine_best_device()
    print(f"Using device: {device}")
    
    # Try multiple embedding models in order of preference
    embedding_models = [
        embedding_model_name,  # Primary choice from config
        'all-MiniLM-L6-v2',    # Lightweight fallback
        'all-MiniLM-L12-v2',   # Medium fallback
        'sentence-transformers/all-mpnet-base-v2'  # Good quality fallback
    ]
    
    for model_name in embedding_models:
        try:
            print(f"🔄 Attempting to load: {model_name}")
            
            # Clear memory before loading
            gc.collect()
            if device == "cuda":
                torch.cuda.empty_cache()
            elif device == "mps":
                torch.mps.empty_cache()
            
            # Try to load the model
            sentence_model = SentenceTransformer(
                model_name,
                trust_remote_code=True,
                device=device,
                cache_folder=None  # Use default cache
            )
            
            print(f"✅ Successfully loaded {model_name}")
            print(f"   Max sequence length: {sentence_model.max_seq_length}")
            print(f"   Model dimension: {sentence_model.get_sentence_embedding_dimension()}")
            
            # Wrap with SentenceEmbedding class
            sentence_encoder = SentenceEmbedding(sentence_model)
            
            # Test encoding to verify model works
            test_text = "This is a test sentence for embedding."
            try:
                test_embedding = sentence_encoder.encode([test_text])
                print(f"✅ Test encoding successful. Shape: {test_embedding.shape}")
                return sentence_encoder
            except Exception as e:
                print(f"⚠️  Test encoding failed for {model_name}: {e}")
                continue
                
        except Exception as e:
            print(f"❌ Failed to load {model_name}: {str(e)}")
            continue
    
    # If all models failed, raise an error
    raise RuntimeError("Failed to load any embedding model. Please check your system resources and internet connection.")

def determine_best_device():
    """Determine the best device for the current system with fallbacks."""
    
    if torch.cuda.is_available():
        print("🔍 CUDA available, checking memory...")
        try:
            # Test CUDA memory
            torch.cuda.empty_cache()
            device_count = torch.cuda.device_count()
            current_device = torch.cuda.current_device()
            memory_info = torch.cuda.mem_get_info(current_device)
            free_memory_gb = memory_info[0] / (1024**3)
            
            print(f"   CUDA devices: {device_count}")
            print(f"   Free GPU memory: {free_memory_gb:.1f} GB")
            
            if free_memory_gb > 2.0:  # Need at least 2GB for embedding models
                return "cuda"
            else:
                print("   ⚠️  Insufficient GPU memory, falling back to CPU")
                return "cpu"
        except Exception as e:
            print(f"   ⚠️  CUDA error: {e}, falling back to CPU")
            return "cpu"
    
    elif torch.backends.mps.is_available():
        print("🔍 MPS (Apple Silicon) available, testing compatibility...")
        try:
            # Test MPS compatibility with a small tensor operation
            test_tensor = torch.randn(10, 10, device="mps")
            test_result = torch.matmul(test_tensor, test_tensor.T)
            print("   ✅ MPS compatibility test passed")
            return "mps"
        except Exception as e:
            print(f"   ⚠️  MPS compatibility issue: {e}, falling back to CPU")
            return "cpu"
    
    else:
        print("🔍 Using CPU device")
        return "cpu"

def test_embedding_model_robust(sentence_encoder):
    """
    Test the embedding model with sample texts, handling potential errors.
    
    Args:
        sentence_encoder: The configured embedding model
    """
    print("\n🧪 Testing embedding model robustly...")
    
    try:
        # Test texts related to knowledge graph extraction
        test_texts = [
            "Apple Inc. is a technology company founded by Steve Jobs.",
            "Knowledge graphs represent information as entities and relationships."
        ]
        
        # Generate embeddings with smaller batch size for robustness
        embeddings = sentence_encoder.encode(test_texts, batch_size=1, show_progress_bar=True)
        
        print(f"✅ Generated embeddings for {len(test_texts)} texts")
        print(f"   Embedding shape: {embeddings.shape}")
        print(f"   Embedding dimension: {embeddings.shape[1]}")
        
        # Test similarity computation
        from sklearn.metrics.pairwise import cosine_similarity
        import numpy as np
        
        if len(embeddings) >= 2:
            similarity_matrix = cosine_similarity(embeddings)
            print(f"   Similarity between texts: {similarity_matrix[0][1]:.4f}")
        
        print("✅ Embedding model test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Embedding model test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Setting up robust embedding model for AutoSchemaKG...")
    
    try:
        # Setup the embedding model
        sentence_encoder = setup_robust_embedding_model()
        
        # Test the model
        success = test_embedding_model_robust(sentence_encoder)
        
        if success:
            print("\n✅ Robust embedding model setup completed successfully!")
            print("The sentence_encoder object is ready for use in your knowledge graph extraction pipeline.")
        else:
            print("\n⚠️  Embedding model setup completed but tests failed.")
            
    except Exception as e:
        print(f"\n❌ Robust embedding model setup failed: {e}")
        import traceback
        traceback.print_exc()