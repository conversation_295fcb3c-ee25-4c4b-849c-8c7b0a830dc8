#!/usr/bin/env python3
"""
Check what's actually in the GraphML text nodes.
"""

import networkx as nx
from pathlib import Path
from tqdm import tqdm

def check_graphml_text_nodes():
    """Check GraphML text node content."""
    
    print("=" * 70)
    print("🔍 Checking GraphML Text Nodes")
    print("=" * 70)
    
    # Load GraphML
    graph_file = Path("import/pdf_dataset/kg_graphml/pdf_dataset_graph.graphml")
    
    if not graph_file.exists():
        print(f"❌ GraphML not found: {graph_file}")
        return
    
    print(f"\n📊 Loading GraphML...")
    with open(graph_file, 'rb') as f:
        KG = nx.read_graphml(f)
    
    print(f"   Total nodes: {len(KG.nodes)}")
    
    # Find text/passage nodes
    text_nodes = []
    for node_id in KG.nodes:
        node_data = KG.nodes[node_id]
        if "passage" in node_data.get("type", "").lower():
            text_nodes.append((node_id, node_data))
    
    print(f"   Found {len(text_nodes)} passage nodes")
    
    # Check structure of first few text nodes
    print("\n📄 Sample text node structures:")
    for i, (node_id, node_data) in enumerate(text_nodes[:3]):
        print(f"\n{i+1}. Node ID: {node_id[:50]}...")
        print(f"   Attributes: {list(node_data.keys())}")
        
        # Check each attribute
        for key, value in node_data.items():
            if isinstance(value, str):
                preview = value[:100] + "..." if len(value) > 100 else value
                print(f"   {key}: {preview}")
            else:
                print(f"   {key}: {value}")
    
    # Look for actual text content
    print("\n🔍 Analyzing 'id' field content:")
    
    has_real_text = 0
    hash_only = 0
    samples_with_text = []
    
    for node_id, node_data in text_nodes:
        id_value = node_data.get("id", "")
        
        # Check if it looks like actual text (has spaces, punctuation, etc.)
        if len(id_value) > 100 and (" " in id_value or "." in id_value or "," in id_value):
            has_real_text += 1
            if len(samples_with_text) < 3:
                samples_with_text.append((node_id, id_value))
        elif len(id_value) == 64 and id_value.isalnum():
            hash_only += 1
    
    print(f"   Nodes with real text in 'id': {has_real_text}")
    print(f"   Nodes with hash only in 'id': {hash_only}")
    
    if samples_with_text:
        print("\n📄 Samples with real text:")
        for i, (node_id, text) in enumerate(samples_with_text, 1):
            print(f"\n{i}. Node: {node_id[:50]}...")
            preview = text[:300] + "..." if len(text) > 300 else text
            print(f"   Text: {preview}")
    
    # Search for specific terms
    print("\n🔍 Searching for specific terms in 'id' field:")
    terms = ["resting order", "Supersonic", "OCO", "risk reversal"]
    
    for term in terms:
        count = 0
        sample = None
        for node_id, node_data in text_nodes:
            id_value = node_data.get("id", "")
            if term.lower() in id_value.lower():
                count += 1
                if not sample:
                    sample = (node_id, id_value)
        
        print(f"\n'{term}': {count} passages")
        if sample:
            node_id, text = sample
            idx = text.lower().index(term.lower())
            start = max(0, idx - 50)
            end = min(len(text), idx + len(term) + 50)
            print(f"  Sample: ...{text[start:end]}...")

def main():
    """Main function."""
    check_graphml_text_nodes()

if __name__ == "__main__":
    main()