#!/usr/bin/env python3
"""
Regenerate GraphML file with passage nodes included.
This uses AutoSchemaKG's csvs_to_graphml function to properly include text nodes.
"""

import os
import sys
from pathlib import Path
from atlas_rag.kg_construction.utils.csv_processing.csv_to_graphml import csvs_to_graphml

def regenerate_graphml_for_dataset(dataset_dir: str = "import/360t_guide_direct_api_v2"):
    """Regenerate GraphML with all node types including passages."""
    
    print("🔧 Regenerating GraphML with passage nodes...")
    print("=" * 60)
    
    base_dir = Path(dataset_dir)
    
    # Check if required CSV files exist
    required_files = {
        "triple_nodes": base_dir / "triples_csv/triple_nodes__from_json_without_emb.csv",
        "text_nodes": base_dir / "triples_csv/text_nodes__from_json.csv",
        "concept_nodes": base_dir / "concept_csv/concept_nodes__from_json_with_concept.csv",
        "triple_edges": base_dir / "concept_csv/triple_edges__from_json_with_concept.csv",
        "text_edges": base_dir / "triples_csv/text_edges__from_json.csv",
        "concept_edges": base_dir / "concept_csv/concept_edges__from_json_with_concept.csv"
    }
    
    # Verify all files exist
    print("📁 Checking required CSV files...")
    all_exist = True
    for name, path in required_files.items():
        if path.exists():
            size = path.stat().st_size / 1024 / 1024  # MB
            print(f"  ✅ {name}: {size:.2f} MB")
        else:
            print(f"  ❌ {name}: Missing!")
            all_exist = False
    
    if not all_exist:
        print("❌ Some required files are missing!")
        return False
    
    # Create output directory if needed
    output_dir = base_dir / "kg_graphml"
    output_dir.mkdir(exist_ok=True)
    
    # Output file path - using standard naming
    output_file = output_dir / "_graph.graphml"
    
    print("\n🚀 Converting CSVs to GraphML...")
    print(f"  Output: {output_file}")
    
    try:
        # Use AutoSchemaKG's function to create proper GraphML
        csvs_to_graphml(
            triple_node_file=str(required_files["triple_nodes"]),
            text_node_file=str(required_files["text_nodes"]),
            concept_node_file=str(required_files["concept_nodes"]),
            triple_edge_file=str(required_files["triple_edges"]),
            text_edge_file=str(required_files["text_edges"]),
            concept_edge_file=str(required_files["concept_edges"]),
            output_file=str(output_file)
        )
        
        # Verify the output
        if output_file.exists():
            size = output_file.stat().st_size / 1024 / 1024  # MB
            print(f"\n✅ GraphML generated successfully: {size:.2f} MB")
            
            # Quick check for passage nodes
            print("\n🔍 Verifying passage nodes in GraphML...")
            with open(output_file, 'r') as f:
                content = f.read(10000)  # Read first 10KB
                if 'type="passage"' in content:
                    print("  ✅ Passage nodes found in GraphML!")
                else:
                    # Check more of the file
                    f.seek(0)
                    full_content = f.read()
                    passage_count = full_content.count('type="passage"')
                    if passage_count > 0:
                        print(f"  ✅ Found {passage_count} passage nodes in GraphML!")
                    else:
                        print("  ⚠️  No passage nodes found - checking for text nodes...")
                        text_count = full_content.count('<node id="')
                        entity_count = full_content.count('type="entity"')
                        concept_count = full_content.count('type="concept"')
                        print(f"     Total nodes: {text_count}")
                        print(f"     Entity nodes: {entity_count}")
                        print(f"     Concept nodes: {concept_count}")
            
            return True
        else:
            print("❌ GraphML file was not created!")
            return False
            
    except Exception as e:
        print(f"❌ Error during GraphML generation: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to regenerate GraphML."""
    
    print("🎯 AutoSchemaKG GraphML Regeneration")
    print("=" * 70)
    print("This script properly regenerates GraphML with all node types,")
    print("including passage nodes that contain actual text content.")
    print()
    
    # Regenerate for 360t_guide_direct_api_v2 dataset
    success = regenerate_graphml_for_dataset("import/360t_guide_direct_api_v2")
    
    if success:
        print("\n🎉 GraphML regeneration complete!")
        print("📝 Next steps:")
        print("  1. Use create_embeddings_and_index() to load this GraphML")
        print("  2. Initialize HippoRAG2Retriever with the loaded data")
        print("  3. Test retrieval to verify passage content is returned")
    else:
        print("\n❌ GraphML regeneration failed!")
        print("💡 Check that all required CSV files exist in the dataset directory")

if __name__ == "__main__":
    main()