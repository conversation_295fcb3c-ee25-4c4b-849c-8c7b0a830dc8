#!/usr/bin/env python3
"""
Debug script to diagnose model selection issues.
Run this to understand what's happening with ollama model selection.
"""

def debug_model_selection():
    print("🔍 DEBUGGING MODEL SELECTION ISSUE")
    print("="*60)
    
    # Test 1: Import and global state
    print("1. Testing imports and global state:")
    try:
        from ollama_model_manager import OLLAMA_AVAILABLE, OllamaModelManager
        print(f"   ✅ Import successful")
        print(f"   ✅ OLLAMA_AVAILABLE: {OLLAMA_AVAILABLE}")
    except Exception as e:
        print(f"   ❌ Import failed: {e}")
        return
    
    # Test 2: Manager instance creation
    print("\n2. Testing manager instance:")
    try:
        mgr = OllamaModelManager()
        print(f"   ✅ Manager created")
        print(f"   ✅ mgr.available: {mgr.available}")
        print(f"   ✅ mgr.is_ollama_available(): {mgr.is_ollama_available()}")
    except Exception as e:
        print(f"   ❌ Manager creation failed: {e}")
        return
    
    # Test 3: Direct ollama test
    print("\n3. Testing direct ollama access:")
    try:
        import ollama
        client = ollama.Client()
        response = client.list()
        print(f"   ✅ Direct ollama works")
        print(f"   ✅ Found {len(response.models)} models")
    except Exception as e:
        print(f"   ❌ Direct ollama failed: {e}")
    
    # Test 4: Model manager methods
    print("\n4. Testing model manager methods:")
    try:
        models = mgr.get_available_models()
        print(f"   ✅ get_available_models(): {len(models)} models")
        
        names = mgr.get_model_names()
        print(f"   ✅ get_model_names(): {len(names)} names")
        
        if names:
            print(f"   ✅ Sample models: {names[:3]}")
    except Exception as e:
        print(f"   ❌ Model methods failed: {e}")
    
    # Test 5: Interactive selection setup (without actual user input)
    print("\n5. Testing interactive selection setup:")
    try:
        # Test the conditions that interactive_model_selection checks
        if not mgr.available:
            print("   ❌ mgr.available is False - this would cause 'Ollama package not available'")
        elif not mgr.is_ollama_available():
            print("   ❌ mgr.is_ollama_available() is False - this would cause 'Ollama server not available'")
        else:
            print("   ✅ All conditions pass - interactive selection should work")
            
            # Test model display without user interaction
            model_list = mgr.display_available_models('qwen3:30b-a3b-thinking-2507-q4_K_M')
            print(f"   ✅ Model display works: {len(model_list)} models listed")
            
    except Exception as e:
        print(f"   ❌ Interactive selection setup failed: {e}")
    
    print("\n" + "="*60)
    print("🎯 DIAGNOSIS:")
    if mgr.available and mgr.is_ollama_available():
        print("✅ Model selection SHOULD work - the issue might be:")
        print("   • Running different Python interpreter/environment")
        print("   • Cached/stale bytecode files")
        print("   • Different version of ollama_model_manager.py")
        print("   • Terminal/IDE environment differences")
    else:
        print("❌ Found the issue - model manager state is inconsistent")

if __name__ == "__main__":
    debug_model_selection()