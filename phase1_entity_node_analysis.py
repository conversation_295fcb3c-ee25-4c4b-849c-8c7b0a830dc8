#!/usr/bin/env python3
"""
Phase 1: Entity Node Analysis - Examine KG for financial entities
"""

import os
import sys
import pickle
import json
from pathlib import Path
from collections import defaultdict

# Set environment and add path
os.environ["TOKENIZERS_PARALLELISM"] = "false"
sys.path.insert(0, str(Path(__file__).parent))

def main():
    print("🔍 PHASE 1: Entity Node Analysis")
    print("="*60)
    print("Examining KG structure for financial entities vs relationship edges")
    
    # Load data
    print("📊 Loading complete data...")
    data_file = Path("import/pdf_dataset/complete_data.pkl")
    with open(data_file, 'rb') as f:
        complete_data = pickle.load(f)
    
    # Get components
    kg = complete_data.get('KG')
    text_dict = complete_data.get('text_dict', {})
    node_list = complete_data.get('node_list', [])
    edge_list = complete_data.get('edge_list', [])
    
    print(f"📊 KG Overview:")
    print(f"   • Total KG nodes: {len(kg.nodes)}")
    print(f"   • Total KG edges: {len(kg.edges)}")
    print(f"   • Node list: {len(node_list)}")
    print(f"   • Edge list: {len(edge_list)}")
    
    # Analyze node types
    print(f"\n🔍 Node Type Analysis:")
    entity_nodes = []  # Short names (likely entities)
    text_nodes = []    # Long hash IDs (text passages)
    
    for node_id in kg.nodes:
        if len(node_id) > 50:  # Likely text node (hash)
            text_nodes.append(node_id)
        else:  # Likely entity node (short name)
            entity_nodes.append(node_id)
    
    print(f"   • Entity nodes (short names): {len(entity_nodes)}")
    print(f"   • Text nodes (hash IDs): {len(text_nodes)}")
    
    # Search for financial entities
    print(f"\n🎯 Financial Entity Search:")
    financial_keywords = [
        'Risk Reversal', 'risk reversal', 'RISK REVERSAL',
        'FX Option', 'fx option', 'FX OPTION',
        'Strategy Option', 'strategy option', 'STRATEGY OPTION',
        'Zero Cost', 'zero cost', 'ZERO COST',
        'Bear Spread', 'bear spread', 'BEAR SPREAD',
        'Delta Hedge', 'delta hedge', 'DELTA HEDGE',
        'SEF', 'NDF', 'NDS', 'MMC',
        'Market Maker', 'market maker', 'MARKET MAKER',
        'Trading', 'trading', 'TRADING',
        'Option', 'option', 'OPTIONS'
    ]
    
    found_financial_entities = []
    
    for keyword in financial_keywords:
        matching_entities = [node for node in entity_nodes if keyword in node]
        if matching_entities:
            found_financial_entities.extend(matching_entities)
            print(f"   ✅ Found '{keyword}': {matching_entities[:3]}...")
        else:
            print(f"   ❌ Missing '{keyword}'")
    
    # Remove duplicates
    found_financial_entities = list(set(found_financial_entities))
    print(f"\n📊 Total unique financial entities found: {len(found_financial_entities)}")
    
    # Display sample entity nodes
    print(f"\n🔍 Sample Entity Nodes (first 20):")
    for i, entity in enumerate(entity_nodes[:20], 1):
        node_data = kg.nodes.get(entity, {})
        node_type = node_data.get('type', 'unknown')
        print(f"   #{i:2d}: '{entity}' (type: {node_type})")
    
    # Check what current query2edge method is actually searching
    print(f"\n🔍 Current Edge Analysis:")
    sample_edges = edge_list[:10] if edge_list else []
    
    print(f"Sample edges from edge_list:")
    for i, (source, target) in enumerate(sample_edges, 1):
        edge_data = kg.edges.get((source, target), {})
        relation = edge_data.get('relation', 'unknown')
        print(f"   #{i}: {source[:20]}... --{relation}--> {target[:20]}...")
    
    # Analyze what HippoRAG2 should be searching instead
    print(f"\n🎯 Recommended Entity-Based Approach:")
    print("Instead of searching edge embeddings, we should:")
    print("1. Extract entities from query: ['Risk Reversal', 'strategy', 'FX', 'option']")
    print("2. Find matching entity nodes using cosine similarity")
    print("3. Run PageRank from matched entity nodes")
    print("4. Return connected text passages")
    
    # Test query entity extraction simulation
    print(f"\n🧪 Query Entity Extraction Simulation:")
    test_query = "what is a risk reversal option strategy and how to create one in Bridge?"
    
    # Simple keyword extraction (should be replaced with LLM-based extraction)
    extracted_entities = []
    query_words = test_query.lower().split()
    
    for entity in found_financial_entities:
        entity_words = entity.lower().split()
        if any(word in query_words for word in entity_words):
            extracted_entities.append(entity)
    
    print(f"Query: '{test_query}'")
    print(f"Potentially matching entities: {extracted_entities[:5]}")
    
    # Check node embeddings vs edge embeddings
    node_embeddings = complete_data.get('node_embeddings', [])
    edge_embeddings = complete_data.get('edge_embeddings', [])
    
    print(f"\n📊 Embedding Analysis:")
    print(f"   • Node embeddings: {len(node_embeddings)}")
    print(f"   • Edge embeddings: {len(edge_embeddings)}")
    print(f"   • Node list (for embeddings): {len(node_list)}")
    
    if len(node_list) > 0 and len(node_embeddings) > 0:
        print(f"   • Node embedding dimension: {node_embeddings[0].shape if hasattr(node_embeddings[0], 'shape') else 'unknown'}")
        
        # Check if financial entities have node embeddings
        financial_with_embeddings = []
        for entity in found_financial_entities[:5]:
            if entity in node_list:
                idx = node_list.index(entity)
                if idx < len(node_embeddings):
                    financial_with_embeddings.append(entity)
        
        print(f"   • Financial entities with embeddings: {len(financial_with_embeddings)}")
        print(f"     Examples: {financial_with_embeddings[:3]}")
    
    # Summary and recommendations
    print(f"\n{'='*60}")
    print("🎯 ANALYSIS SUMMARY:")
    print(f"• Found {len(found_financial_entities)} financial entities in KG")
    print(f"• Current approach searches {len(edge_embeddings)} edge embeddings")
    print(f"• Should search {len(node_embeddings)} node embeddings instead")
    print(f"• Need to implement LLM-based entity extraction from queries")
    print(f"• Need to switch from edge-based to entity-based retrieval")
    
    if len(found_financial_entities) > 0:
        print("✅ Financial entities exist - implementation fix should work!")
    else:
        print("❌ No financial entities found - may need to check triple extraction process")

if __name__ == "__main__":
    main()