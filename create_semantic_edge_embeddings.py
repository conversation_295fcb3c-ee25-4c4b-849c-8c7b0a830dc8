#!/usr/bin/env python3
"""
Create Semantic-Aware Edge Embeddings

Addresses the core issue: existing edge embeddings don't semantically match
natural language queries like "how to create scenarios in MMC?" even though
the content exists in the knowledge graph.

This script creates improved edge embeddings that:
1. Use natural language descriptions instead of structured triples
2. Include contextual information from connected text passages
3. Generate query-friendly representations for better semantic matching
"""

import numpy as np
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import pickle

from hipporag2_pipeline import HippoRAG2Pipeline

class SemanticEdgeEmbeddingCreator:
    """
    Creates semantic-aware edge embeddings for better query matching.
    """
    
    def __init__(self, data_directory="import/pdf_dataset"):
        self.data_directory = Path(data_directory)
        self.vector_dir = self.data_directory / "vector_index"
        self.pipeline = None
        self.enhanced_edges = {}
        self.semantic_descriptions = {}
        
    def initialize_pipeline(self):
        """Initialize the HippoRAG2 pipeline."""
        print("🚀 Initializing HippoRAG2 Pipeline for semantic edge creation...")
        
        self.pipeline = HippoRAG2Pipeline(data_directory=str(self.data_directory))
        data = self.pipeline.load_existing_data()
        self.pipeline.setup_models()
        self.pipeline.data = data
        
        print(f"✅ Pipeline initialized")
        print(f"   Graph: {len(data['KG'].nodes):,} nodes, {len(data['KG'].edges):,} edges")
        print(f"   Current edge embeddings: {data['edge_embeddings'].shape}")
        
        return data
    
    def analyze_edge_types(self, data):
        """Analyze different types of edges and their semantic patterns."""
        print(f"\n🔍 ANALYZING EDGE SEMANTIC PATTERNS")
        print("="*50)
        
        graph = data["KG"]
        edge_patterns = defaultdict(list)
        
        # Sample edges by type for analysis
        sample_count = 0
        max_samples = 1000  # Analyze first 1000 edges
        
        for edge in graph.edges(data=True):
            if sample_count >= max_samples:
                break
                
            source, target, edge_data = edge
            source_data = graph.nodes[source]
            target_data = graph.nodes[target]
            
            source_type = source_data.get('type', 'unknown')
            target_type = target_data.get('type', 'unknown')
            relation = edge_data.get('relation', 'connected_to')
            
            source_name = source_data.get('name', source)
            target_name = target_data.get('name', target)
            
            edge_type = f"{source_type}->{target_type}"
            
            # Store edge information for semantic enhancement
            edge_info = {
                'source': source,
                'target': target,
                'source_name': source_name,
                'target_name': target_name,
                'source_type': source_type,
                'target_type': target_type,
                'relation': relation,
                'original_desc': f"{source_name} {relation} {target_name}"
            }
            
            edge_patterns[edge_type].append(edge_info)
            sample_count += 1
        
        print(f"Analyzed {sample_count:,} edges")
        print(f"Edge type patterns found:")
        for edge_type, edges in edge_patterns.items():
            print(f"   {edge_type}: {len(edges):,} edges")
        
        return edge_patterns
    
    def create_semantic_descriptions(self, edge_patterns, data):
        """Create semantic descriptions for different edge types."""
        print(f"\n🧠 CREATING SEMANTIC DESCRIPTIONS")
        print("="*50)
        
        graph = data["KG"]
        text_dict = data["text_dict"]
        
        semantic_edges = []
        
        # Focus on key edge types that connect to text
        priority_types = [
            'entity->text',
            'event->text', 
            'entity->entity',
            'event->entity',
            'event->event'
        ]
        
        for edge_type in priority_types:
            if edge_type not in edge_patterns:
                continue
                
            print(f"\n📝 Processing {edge_type} edges...")
            edges = edge_patterns[edge_type]
            
            for i, edge_info in enumerate(edges[:100]):  # Process first 100 of each type
                try:
                    semantic_desc = self.generate_semantic_description(edge_info, graph, text_dict)
                    
                    if semantic_desc:
                        enhanced_edge = {
                            **edge_info,
                            'semantic_description': semantic_desc,
                            'edge_id': (edge_info['source'], edge_info['target'])
                        }
                        semantic_edges.append(enhanced_edge)
                        
                        # Store for later use
                        edge_key = (edge_info['source'], edge_info['target'])
                        self.semantic_descriptions[edge_key] = semantic_desc
                        
                except Exception as e:
                    print(f"   Error processing edge {i}: {e}")
                    continue
        
        print(f"\n✅ Created {len(semantic_edges):,} semantic edge descriptions")
        return semantic_edges
    
    def generate_semantic_description(self, edge_info, graph, text_dict):
        """Generate a semantic description for an edge."""
        source_name = edge_info['source_name']
        target_name = edge_info['target_name']
        source_type = edge_info['source_type']
        target_type = edge_info['target_type']
        relation = edge_info['relation']
        
        # Base semantic description
        if relation == 'source':
            if target_type in ['text', 'passage']:
                # Get actual text content for context
                text_content = text_dict.get(edge_info['target'], '')
                text_preview = text_content[:200] if text_content else ''
                
                if any(term in source_name.lower() for term in ['mmc', 'market maker cockpit']):
                    if any(term in text_preview.lower() for term in ['scenario', 'configuration', 'setup']):
                        return f"How to configure {source_name}: {text_preview}"
                    else:
                        return f"{source_name} documentation and setup guide"
                elif any(term in source_name.lower() for term in ['scenario', 'scenarios']):
                    return f"How to create and configure {source_name}: setup instructions and guidelines"
                else:
                    return f"{source_name} information and documentation"
                    
        elif relation == 'is participated by':
            if source_type == 'event' and target_type == 'entity':
                return f"How {target_name} is involved in {source_name}"
                
        elif relation == 'as a result':
            if source_type == 'event' and target_type == 'event':
                return f"When {source_name}, then {target_name}"
                
        elif relation == 'illustrates' or relation == 'shows':
            return f"{source_name} shows how to use {target_name}"
            
        elif relation == 'contains':
            return f"{source_name} includes {target_name} functionality"
            
        # Enhanced descriptions for MMC and scenario-related edges
        if any(term in source_name.lower() for term in ['mmc', 'market maker cockpit']):
            if any(term in target_name.lower() for term in ['scenario', 'configuration', 'setup']):
                return f"Market Maker Cockpit (MMC) {target_name} configuration and setup guide"
            else:
                return f"Market Maker Cockpit (MMC) {relation} {target_name}"
                
        if any(term in source_name.lower() for term in ['scenario', 'scenarios']):
            return f"How to create and manage {source_name} in trading systems"
            
        # Default enhanced description
        return f"{source_name} {relation} {target_name}: technical documentation and setup information"
    
    def generate_enhanced_embeddings(self, semantic_edges):
        """Generate embeddings for the semantic descriptions."""
        print(f"\n🔄 GENERATING SEMANTIC EMBEDDINGS")
        print("="*50)
        
        if not self.pipeline or not self.pipeline.sentence_encoder:
            raise RuntimeError("Pipeline not initialized")
        
        descriptions = []
        edge_mappings = []
        
        print(f"Processing {len(semantic_edges):,} semantic descriptions...")
        
        # Prepare descriptions for batch encoding
        for edge in semantic_edges:
            descriptions.append(edge['semantic_description'])
            edge_mappings.append(edge['edge_id'])
        
        # Generate embeddings in batches
        batch_size = 100
        all_embeddings = []
        
        for i in range(0, len(descriptions), batch_size):
            batch_desc = descriptions[i:i+batch_size]
            
            print(f"   Processing batch {i//batch_size + 1}/{(len(descriptions)-1)//batch_size + 1}")
            
            # Use edge query type for consistency with existing system
            batch_embeddings = self.pipeline.sentence_encoder.encode(batch_desc, query_type="edge")
            all_embeddings.append(batch_embeddings)
        
        # Combine all embeddings
        semantic_embeddings = np.vstack(all_embeddings)
        
        print(f"✅ Generated semantic embeddings: {semantic_embeddings.shape}")
        
        return semantic_embeddings, edge_mappings
    
    def integrate_semantic_embeddings(self, semantic_embeddings, edge_mappings, data):
        """Integrate semantic embeddings with existing edge embeddings."""
        print(f"\n🔗 INTEGRATING SEMANTIC EMBEDDINGS")
        print("="*50)
        
        # Create mapping from edge tuples to indices in existing embeddings
        existing_edge_to_idx = {}
        for i, edge in enumerate(data["edge_list"]):
            existing_edge_to_idx[edge] = i
        
        # Find which semantic edges correspond to existing edges
        matching_indices = []
        semantic_indices = []
        
        for i, edge_id in enumerate(edge_mappings):
            if edge_id in existing_edge_to_idx:
                existing_idx = existing_edge_to_idx[edge_id]
                matching_indices.append(existing_idx)
                semantic_indices.append(i)
        
        print(f"Found {len(matching_indices):,} semantic embeddings matching existing edges")
        
        if len(matching_indices) > 0:
            # Replace corresponding embeddings with semantic versions
            enhanced_embeddings = data["edge_embeddings"].copy()
            
            for existing_idx, semantic_idx in zip(matching_indices, semantic_indices):
                enhanced_embeddings[existing_idx] = semantic_embeddings[semantic_idx]
            
            print(f"✅ Enhanced {len(matching_indices):,} edge embeddings with semantic versions")
            
            # Save enhanced embeddings
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            enhanced_path = self.vector_dir / f"enhanced_edge_embeddings_{timestamp}.npy"
            np.save(enhanced_path, enhanced_embeddings)
            
            # Save mapping information
            mapping_path = self.vector_dir / f"semantic_edge_mapping_{timestamp}.pkl"
            mapping_info = {
                'edge_mappings': edge_mappings,
                'matching_indices': matching_indices,
                'semantic_indices': semantic_indices,
                'total_enhanced': len(matching_indices),
                'original_shape': data["edge_embeddings"].shape,
                'enhanced_shape': enhanced_embeddings.shape
            }
            
            with open(mapping_path, 'wb') as f:
                pickle.dump(mapping_info, f)
            
            print(f"\n📁 FILES CREATED:")
            print(f"   Enhanced embeddings: {enhanced_path.name}")
            print(f"   Mapping info: {mapping_path.name}")
            
            return enhanced_embeddings, mapping_info
        else:
            print("⚠️  No matching edges found for integration")
            return None, None
    
    def test_semantic_improvements(self, enhanced_embeddings, data):
        """Test if semantic embeddings improve query matching."""
        print(f"\n🧪 TESTING SEMANTIC IMPROVEMENTS")
        print("="*50)
        
        test_queries = [
            "how to create scenarios in MMC?",
            "MMC Market Maker Cockpit setup",
            "configure market maker cockpit",
            "scenario configuration guide"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Query: '{query}'")
            
            # Test with enhanced embeddings
            query_emb = self.pipeline.sentence_encoder.encode([query], query_type="edge")
            
            # Compute similarities with enhanced embeddings
            similarities = enhanced_embeddings @ query_emb[0].T
            top_idx = np.argmax(similarities)
            top_score = similarities[top_idx]
            
            if top_idx < len(data["edge_list"]):
                edge = data["edge_list"][top_idx]
                source_name = data["KG"].nodes[edge[0]].get('name', edge[0])
                target_name = data["KG"].nodes[edge[1]].get('name', edge[1])
                relation = data["KG"].edges[edge].get('relation', 'connected_to')
                
                print(f"   Enhanced top result: {top_score:.4f}")
                print(f"   Edge: {source_name[:50]}... {relation} {target_name[:50]}...")
                
                # Check for MMC/scenario relevance
                full_desc = f"{source_name} {relation} {target_name}".lower()
                if any(term in full_desc for term in ['mmc', 'market maker cockpit', 'scenario']):
                    print(f"   ✅ Relevant content found!")
                else:
                    print(f"   ❌ Still not finding relevant content")
    
    def run_semantic_enhancement(self):
        """Run the complete semantic enhancement process."""
        print("🧠 SEMANTIC EDGE EMBEDDING ENHANCEMENT")
        print("="*80)
        
        try:
            # Initialize pipeline
            data = self.initialize_pipeline()
            
            # Analyze edge patterns
            edge_patterns = self.analyze_edge_types(data)
            
            # Create semantic descriptions
            semantic_edges = self.create_semantic_descriptions(edge_patterns, data)
            
            # Generate enhanced embeddings
            semantic_embeddings, edge_mappings = self.generate_enhanced_embeddings(semantic_edges)
            
            # Integrate with existing embeddings
            enhanced_embeddings, mapping_info = self.integrate_semantic_embeddings(
                semantic_embeddings, edge_mappings, data
            )
            
            if enhanced_embeddings is not None:
                # Test improvements
                self.test_semantic_improvements(enhanced_embeddings, data)
                
                print(f"\n{'='*80}")
                print(f"🎉 SEMANTIC ENHANCEMENT COMPLETE!")
                print(f"{'='*80}")
                print(f"   Enhanced {mapping_info['total_enhanced']:,} edge embeddings")
                print(f"   Original embedding shape: {mapping_info['original_shape']}")
                print(f"   Enhanced embedding shape: {mapping_info['enhanced_shape']}")
                print(f"\n📝 Next steps:")
                print(f"   1. Replace edge embeddings in HippoRAG2Pipeline")
                print(f"   2. Test MMC query retrieval")
                print(f"   3. Validate cross-domain performance")
                
                return True
            else:
                print(f"\n❌ Semantic enhancement failed - no matching edges")
                return False
                
        except Exception as e:
            print(f"❌ Semantic enhancement failed: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main function."""
    creator = SemanticEdgeEmbeddingCreator()
    success = creator.run_semantic_enhancement()
    return success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)