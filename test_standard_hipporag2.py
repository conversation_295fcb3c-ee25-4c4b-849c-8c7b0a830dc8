#!/usr/bin/env python3
"""
Test HippoRAG2 with properly loaded AutoSchemaKG data and Ollama LLM.
This script demonstrates the complete working pipeline using proven AutoSchemaKG methods.
"""

import os
import sys
from pathlib import Path
from sentence_transformers import SentenceTransformer
from atlas_rag.vectorstore.embedding_model import Sentence<PERSON>mbedding
from atlas_rag.vectorstore.create_graph_index import create_embeddings_and_index
from atlas_rag.retriever import HippoRAG2Retriever
from atlas_rag.llm_generator import LLMGenerator
from openai import OpenAI
from ollama_model_manager import OllamaModelManager
import time

# Configuration - Change this to use a different Ollama model
DEFAULT_OLLAMA_MODEL = "qwen3:30b-a3b-thinking-2507-q4_K_M"  # Edit this to use your preferred model

class StandardHippoRAG2Pipeline:
    """Standard HippoRAG2 pipeline using AutoSchemaKG's proven methods."""
    
    def __init__(self, dataset_dir: str = "import/360t_guide_direct_api_v2", 
                 use_ollama: bool = True,
                 ollama_model: str = DEFAULT_OLLAMA_MODEL):
        """Initialize the pipeline with AutoSchemaKG data loading."""
        
        self.dataset_dir = dataset_dir
        self.use_ollama = use_ollama
        self.ollama_model = ollama_model
        self.data = None
        self.sentence_encoder = None
        self.retriever = None
        self.llm_generator = None
        
    def load_data(self):
        """Load data using AutoSchemaKG's create_embeddings_and_index()."""
        
        print("📊 Loading data with AutoSchemaKG...")
        
        # Initialize embedding model
        encoder_model_name = "sentence-transformers/all-mpnet-base-v2"
        sentence_model = SentenceTransformer(encoder_model_name)
        self.sentence_encoder = SentenceEmbedding(sentence_model)
        
        # Load data with AutoSchemaKG method
        self.data = create_embeddings_and_index(
            sentence_encoder=self.sentence_encoder,
            model_name=encoder_model_name,
            working_directory=self.dataset_dir,
            keyword="",  # Empty for filename pattern
            include_concept=True,
            include_events=True,
            normalize_embeddings=True,
            text_batch_size=64,
            node_and_edge_batch_size=256
        )
        
        print(f"✅ Loaded {len(self.data['node_list'])} nodes, {len(self.data['text_dict'])} passages")
        
    def setup_llm(self):
        """Setup LLM generator (Ollama or fallback)."""
        
        if self.use_ollama:
            print(f"🤖 Setting up Ollama with {self.ollama_model}...")
            
            # Ollama client configuration
            client = OpenAI(
                api_key="EMPTY",
                base_url="http://localhost:11434/v1"
            )
            
            self.llm_generator = LLMGenerator(
                client=client,
                model_name=self.ollama_model
            )
            
            # Test Ollama connection
            try:
                # Simple test to check if model is available
                test_response = client.chat.completions.create(
                    model=self.ollama_model,
                    messages=[{"role": "user", "content": "test"}],
                    max_tokens=10,
                    temperature=0.1
                )
                print("✅ Ollama connection successful")
            except Exception as e:
                print(f"⚠️  Ollama connection failed: {str(e)}")
                print("   Make sure Ollama is running with the model loaded")
                print(f"   Run: ollama run {self.ollama_model}")
                self.use_ollama = False
        
        if not self.use_ollama:
            print("📝 Using mock LLM for testing (replace with actual LLM)")
            # Mock client for testing
            client = OpenAI(api_key="EMPTY", base_url="http://localhost:8000/v1")
            self.llm_generator = LLMGenerator(client=client, model_name="mock")
    
    def initialize_retriever(self):
        """Initialize HippoRAG2Retriever with loaded data."""
        
        print("🔍 Initializing HippoRAG2Retriever...")
        
        self.retriever = HippoRAG2Retriever(
            llm_generator=self.llm_generator,
            sentence_encoder=self.sentence_encoder,
            data=self.data
        )
        
        print("✅ Retriever initialized")
    
    def ask(self, question: str, topN: int = 5, verbose: bool = True):
        """Ask a question and get an answer using HippoRAG2 + LLM."""
        
        if verbose:
            print(f"\n❓ Question: {question}")
            print("-" * 60)
        
        # Retrieve relevant passages
        start_time = time.time()
        passages, node_ids, scores = self.retriever.retrieve(question, topN=topN)
        retrieval_time = time.time() - start_time
        
        if verbose:
            print(f"📊 Retrieved {len(passages)} passages in {retrieval_time:.2f}s")
            print(f"   Top scores: {[f'{s:.4f}' for s in scores[:3]]}")
        
        # Show retrieved content
        if verbose and passages:
            print("\n📄 Top retrieved passage:")
            preview = passages[0][:400] + "..." if len(passages[0]) > 400 else passages[0]
            print(f"   {preview}")
        
        # Generate answer with LLM
        if passages:
            context = "\n\n".join(passages)
            
            if verbose:
                print(f"\n💬 Generating answer with {self.ollama_model}...")
            
            try:
                answer = self.llm_generator.generate_with_context(
                    question=question,
                    context=context,
                    max_new_tokens=512,
                    temperature=0.7
                )
                
                if verbose:
                    print(f"\n💡 Answer:")
                    print(f"   {answer}")
                
                return answer, passages, scores
                
            except Exception as e:
                error_msg = f"Error generating answer: {str(e)}"
                if verbose:
                    print(f"❌ {error_msg}")
                return error_msg, passages, scores
        else:
            if verbose:
                print("❌ No relevant passages found")
            return "No relevant information found.", [], []
    
    def interactive_qa(self):
        """Interactive Q&A session."""
        
        print("\n🎯 Interactive HippoRAG2 Q&A")
        print("=" * 60)
        print("Type 'exit' to quit, 'help' for options")
        print()
        
        while True:
            try:
                question = input("❓ Your question: ").strip()
                
                if question.lower() == 'exit':
                    print("👋 Goodbye!")
                    break
                elif question.lower() == 'help':
                    print("\nOptions:")
                    print("  - Type any question about the documents")
                    print("  - 'exit' to quit")
                    print("  - 'test' to run test queries")
                    print()
                    continue
                elif question.lower() == 'test':
                    self.run_test_queries()
                    continue
                elif question:
                    self.ask(question)
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {str(e)}")
    
    def run_test_queries(self):
        """Run a set of test queries."""
        
        print("\n🧪 Running Test Queries")
        print("=" * 60)
        
        test_queries = [
            "What is an OCO order and how does it work?",
            "How do I configure risk reversal in the trading system?",
            "What are the main features of Market Maker Cockpit?",
            "Explain the FIX protocol implementation for market takers",
            "What are Bank Baskets and how are they configured?"
        ]
        
        for query in test_queries:
            self.ask(query, verbose=True)
            print("\n" + "=" * 60)
            time.sleep(1)  # Small delay between queries

def main():
    """Main function to run the standard HippoRAG2 pipeline."""
    
    print("🚀 Standard HippoRAG2 Pipeline with AutoSchemaKG")
    print("=" * 70)
    print("This implementation uses AutoSchemaKG's proven methods:")
    print("  1. GraphML with passage nodes")
    print("  2. create_embeddings_and_index() for data loading")
    print("  3. Standard HippoRAG2Retriever")
    print("  4. Ollama LLM for answer generation")
    print()
    
    # Initialize pipeline
    pipeline = StandardHippoRAG2Pipeline(
        dataset_dir="import/360t_guide_direct_api_v2",
        use_ollama=True,
        ollama_model="qwen3:30b-a3b-thinking-2507-q4_K_M"
    )
    
    # Load data
    print("Step 1: Loading data...")
    pipeline.load_data()
    
    # Setup LLM
    print("\nStep 2: Setting up LLM...")
    pipeline.setup_llm()
    
    # Initialize retriever
    print("\nStep 3: Initializing retriever...")
    pipeline.initialize_retriever()
    
    # Run interactive Q&A or test queries
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        pipeline.run_test_queries()
    else:
        pipeline.interactive_qa()

if __name__ == "__main__":
    main()