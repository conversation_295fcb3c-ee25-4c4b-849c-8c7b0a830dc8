"""
Quick validation script for HippoRAG2 implementation.
This script performs a minimal validation to ensure all components work together.
"""

import sys
from pathlib import Path
from hipporag2_pipeline import HippoRAG2Pipeline

def validate_hipporag2():
    """Run validation tests for HippoRAG2 implementation."""
    
    print("🔍 HippoRAG2 Implementation Validation")
    print("=" * 50)
    
    try:
        # Step 1: Initialize pipeline
        print("\n📋 Step 1: Initializing pipeline...")
        pipeline = HippoRAG2Pipeline(
            data_directory="import/pdf_dataset",
            config_file="config.ini"
        )
        print("✅ Pipeline initialized successfully")
        
        # Step 2: Load data
        print("\n📂 Step 2: Loading existing data...")
        data = pipeline.load_existing_data()
        
        # Validate data structure
        required_keys = ["KG", "node_embeddings", "edge_embeddings", "text_embeddings", 
                        "edge_faiss_index", "node_list", "edge_list", "text_dict"]
        
        missing_keys = [key for key in required_keys if key not in data]
        if missing_keys:
            print(f"❌ Missing required data keys: {missing_keys}")
            return False
        
        print(f"✅ Data loaded successfully")
        print(f"   Graph: {len(data['KG'].nodes)} nodes, {len(data['KG'].edges)} edges")
        print(f"   Node embeddings: {data['node_embeddings'].shape}")
        print(f"   Edge embeddings: {data['edge_embeddings'].shape}")
        print(f"   Text embeddings: {data['text_embeddings'].shape}")
        
        # Step 3: Test model setup (just initialization, not full loading)
        print("\n🤖 Step 3: Testing model setup...")
        
        # Test OLLAMA setup
        from setup_ollama_llm_generator import setup_ollama_llm_generator
        print("   Testing OLLAMA generator setup...")
        llm_generator = setup_ollama_llm_generator()
        print("   ✅ OLLAMA generator setup successful")
        
        # Test a simple generation
        print("   Testing basic generation...")
        simple_response = llm_generator.generate_response(
            "Hello, respond with just 'Hi'",
            max_new_tokens=10,
            temperature=0.1
        )
        print(f"   Response: {simple_response[:50]}...")
        print("   ✅ Basic generation test passed")
        
        # Step 4: Test HippoRAG2 compatibility
        print("\n🧠 Step 4: Testing HippoRAG2 compatibility...")
        
        # Check if HippoRAG2Retriever can be imported
        from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
        from atlas_rag.retriever.inference_config import InferenceConfig
        print("   ✅ HippoRAG2 imports successful")
        
        # Create inference config
        inference_config = InferenceConfig()
        print("   ✅ Inference config created")
        
        print("\n🎉 All validation tests passed!")
        print("\nYour HippoRAG2 implementation is ready to use!")
        print("\nNext steps:")
        print("1. Run: python hipporag2_pipeline.py (for interactive mode)")
        print("2. Or run: python hipporag2_pipeline.py --test (for automated testing)")
        print("3. Or run: python -m pytest test_hipporag2_integration.py -v (for unit tests)")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = validate_hipporag2()
    sys.exit(0 if success else 1)