#!/usr/bin/env python3
"""
Safe embedding generator for pdf_dataset.
This script computes embeddings in a safe, incremental way to avoid segmentation faults.
"""

import os
import sys
import gc
import pickle
import numpy as np
import faiss
from pathlib import Path
from tqdm import tqdm
import networkx as nx
from sentence_transformers import SentenceTransformer
from atlas_rag.vectorstore.embedding_model import <PERSON><PERSON><PERSON>Embedding

def safe_build_faiss_index(embeddings, index_type="flat", batch_size=1):
    """Build FAISS index safely with small batches and error handling."""
    
    dimension = len(embeddings[0])
    X = np.array(embeddings).astype('float32')
    
    # Normalize vectors
    faiss.normalize_L2(X)
    
    if index_type == "flat":
        # Use simpler flat index (no HNSW)
        print("  Using IndexFlatIP (simpler, more stable)")
        faiss_index = faiss.IndexFlatIP(dimension)
        
        # Add in very small batches to avoid memory issues
        for i in tqdm(range(0, X.shape[0], batch_size), desc="Adding to index"):
            try:
                faiss_index.add(X[i:i+batch_size])
            except Exception as e:
                print(f"  Warning: Failed to add batch {i}, error: {e}")
                # Try adding one by one
                for j in range(i, min(i+batch_size, X.shape[0])):
                    try:
                        faiss_index.add(X[j:j+1])
                    except:
                        print(f"  Skipping vector {j}")
    else:
        # Try HNSW but with smaller M value
        print("  Using IndexHNSWFlat with M=32 (reduced connections)")
        faiss_index = faiss.IndexHNSWFlat(dimension, 32, faiss.METRIC_INNER_PRODUCT)
        
        for i in tqdm(range(0, X.shape[0], batch_size), desc="Adding to index"):
            try:
                faiss_index.add(X[i:i+batch_size])
            except Exception as e:
                print(f"  Warning: HNSW failed, falling back to flat index")
                # Fall back to flat index
                return safe_build_faiss_index(embeddings, index_type="flat", batch_size=batch_size)
    
    return faiss_index

def compute_embeddings_safe(items, sentence_encoder, batch_size=1):
    """Compute embeddings safely in small batches."""
    
    embeddings = []
    
    for i in tqdm(range(0, len(items), batch_size), desc="Computing embeddings"):
        batch = items[i:i+batch_size]
        try:
            batch_embeddings = sentence_encoder.encode(batch, normalize_embeddings=True, show_progress_bar=False, batch_size=1, num_workers=0)
            embeddings.extend(batch_embeddings)
        except Exception as e:
            print(f"  Warning: Batch {i} failed: {e}")
            # Try one by one
            for item in batch:
                try:
                    emb = sentence_encoder.encode([item], normalize_embeddings=True, show_progress_bar=False, batch_size=1, num_workers=0)
                    embeddings.extend(emb)
                except:
                    print(f"  Skipping item: {item[:50]}...")
                    # Add zero embedding as placeholder
                    embeddings.append(np.zeros(768))
        
        # Periodic garbage collection
        if i % 1000 == 0:
            gc.collect()
    
    return embeddings

def generate_pdf_dataset_embeddings():
    """Generate embeddings for pdf_dataset safely."""
    
    print("🔧 Safe Embedding Generation for pdf_dataset")
    print("=" * 70)
    
    base_dir = Path("import/pdf_dataset")
    precompute_dir = base_dir / "precompute"
    precompute_dir.mkdir(exist_ok=True)
    
    # Load graph
    print("\n📊 Loading GraphML...")
    graph_file = base_dir / "kg_graphml" / "pdf_dataset_graph.graphml"
    
    if not graph_file.exists():
        print(f"❌ GraphML not found: {graph_file}")
        return False
    
    with open(graph_file, 'rb') as f:
        KG = nx.read_graphml(f)
    
    print(f"  Loaded graph with {len(KG.nodes)} nodes")
    
    # Initialize encoder
    print("\n🔤 Initializing encoder...")
    encoder_model_name = "sentence-transformers/all-mpnet-base-v2"
    sentence_model = SentenceTransformer(encoder_model_name)
    sentence_encoder = SentenceEmbedding(sentence_model)
    
    # Prepare node and edge lists
    print("\n📝 Preparing node and edge lists...")
    
    node_list = list(KG.nodes)
    text_list = [node for node in tqdm(node_list, desc="Finding text nodes") 
                 if "passage" in KG.nodes[node].get("type", "")]
    
    # Filter for entity/event/concept nodes
    node_list = [node for node in tqdm(node_list, desc="Filtering nodes") 
                 if any(t in KG.nodes[node].get("type", "") 
                       for t in ["entity", "event", "concept"])]
    
    print(f"  Entity/Event/Concept nodes: {len(node_list)}")
    print(f"  Text nodes: {len(text_list)}")
    
    # Get node strings
    node_list_string = [KG.nodes[node].get("id", str(node)) for node in node_list]
    
    # Get edges
    edge_list = list(KG.edges)
    node_set = set(node_list)
    edge_list = [(u, v) for u, v in tqdm(edge_list, desc="Filtering edges") 
                 if u in node_set and v in node_set]
    
    edge_list_string = []
    for u, v in edge_list:
        u_id = KG.nodes[u].get("id", str(u))
        v_id = KG.nodes[v].get("id", str(v))
        relation = KG.edges[(u, v)].get("relation", "related")
        edge_list_string.append(f"{u_id} {relation} {v_id}")
    
    print(f"  Edges: {len(edge_list)}")
    
    # Save lists
    print("\n💾 Saving lists...")
    
    node_list_file = precompute_dir / "pdf_dataset_eventTrueconceptTrue_node_list.pkl"
    edge_list_file = precompute_dir / "pdf_dataset_eventTrueconceptTrue_edge_list.pkl"
    
    with open(node_list_file, 'wb') as f:
        pickle.dump(node_list, f)
    with open(edge_list_file, 'wb') as f:
        pickle.dump(edge_list, f)
    
    print("  ✅ Lists saved")
    
    # Compute node embeddings
    print("\n🧮 Computing node embeddings (this may take a while)...")
    node_embeddings_file = precompute_dir / f"pdf_dataset_eventTrueconceptTrue_{encoder_model_name.split('/')[-1]}_node_embeddings.pkl"
    
    if node_embeddings_file.exists():
        print("  ✅ Node embeddings already exist, loading...")
        with open(node_embeddings_file, 'rb') as f:
            node_embeddings = pickle.load(f)
    else:
        node_embeddings = compute_embeddings_safe(node_list_string, sentence_encoder, batch_size=20)
        
        # Save embeddings
        with open(node_embeddings_file, 'wb') as f:
            pickle.dump(node_embeddings, f)
        print(f"  ✅ Saved {len(node_embeddings)} node embeddings")
    
    # Compute edge embeddings
    print("\n🧮 Computing edge embeddings (this may take longer)...")
    edge_embeddings_file = precompute_dir / f"pdf_dataset_eventTrueconceptTrue_{encoder_model_name.split('/')[-1]}_edge_embeddings.pkl"
    
    if edge_embeddings_file.exists():
        print("  ✅ Edge embeddings already exist, loading...")
        with open(edge_embeddings_file, 'rb') as f:
            edge_embeddings = pickle.load(f)
    else:
        edge_embeddings = compute_embeddings_safe(edge_list_string, sentence_encoder, batch_size=20)
        
        # Save embeddings
        with open(edge_embeddings_file, 'wb') as f:
            pickle.dump(edge_embeddings, f)
        print(f"  ✅ Saved {len(edge_embeddings)} edge embeddings")
    
    # Build FAISS indices
    print("\n🔨 Building FAISS indices...")
    
    # Node index
    node_index_file = precompute_dir / f"pdf_dataset_eventTrueconceptTrue_{encoder_model_name.split('/')[-1]}_node_faiss.index"
    
    if node_index_file.exists():
        print("  ✅ Node index already exists")
    else:
        print("  Building node index...")
        node_index = safe_build_faiss_index(node_embeddings, index_type="flat", batch_size=1)
        faiss.write_index(node_index, str(node_index_file))
        print(f"  ✅ Saved node index")
    
    # Edge index
    edge_index_file = precompute_dir / f"pdf_dataset_eventTrueconceptTrue_{encoder_model_name.split('/')[-1]}_edge_faiss.index"
    
    if edge_index_file.exists():
        print("  ✅ Edge index already exists")
    else:
        print("  Building edge index...")
        edge_index = safe_build_faiss_index(edge_embeddings, index_type="flat", batch_size=1)
        faiss.write_index(edge_index, str(edge_index_file))
        print(f"  ✅ Saved edge index")
    
    print("\n🎉 Embedding generation complete!")
    print("  All embeddings and indices have been safely generated and saved.")
    print("  You can now run the interactive script without segmentation faults.")
    
    return True

def main():
    """Main function."""
    success = generate_pdf_dataset_embeddings()
    
    if success:
        print("\n✅ Success! You can now run:")
        print("   python hipporag2_interactive_debug.py")
    else:
        print("\n❌ Failed to generate embeddings")

if __name__ == "__main__":
    main()