#!/usr/bin/env python3
"""
Quick-start script for HippoRAG2 Q&A with pdf_dataset.
Simple interactive Q&A without all the debug complexity.
"""

import sys
from pathlib import Path
from sentence_transformers import SentenceTransformer
from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
from atlas_rag.vectorstore.create_graph_index import create_embeddings_and_index
from atlas_rag.retriever import HippoRAG2Retriever
from atlas_rag.llm_generator import LLMGenerator
from openai import OpenAI
from ollama_model_manager import OllamaModelManager

# Configuration - Change this to use a different Ollama model
OLLAMA_MODEL = "qwen3:30b-a3b-thinking-2507-q4_K_M"  # Edit this to use your preferred model

def setup_pipeline():
    """Setup the complete pipeline."""
    
    print("🚀 Setting up HippoRAG2 Q&A Pipeline")
    print("=" * 70)
    
    # 1. Load embeddings
    print("📊 Loading pdf_dataset (1,207 passages)...")
    sentence_encoder = SentenceEmbedding(
        SentenceTransformer("sentence-transformers/all-mpnet-base-v2")
    )
    
    data = create_embeddings_and_index(
        sentence_encoder=sentence_encoder,
        model_name="sentence-transformers/all-mpnet-base-v2",
        working_directory="import/pdf_dataset",
        keyword="pdf_dataset",
        include_concept=True,
        include_events=True,
        normalize_embeddings=True,
        text_batch_size=64,
        node_and_edge_batch_size=256
    )
    
    print(f"✅ Loaded {len(data['text_dict'])} passages")
    
    # 2. Setup LLM
    print("\n🤖 Setting up Ollama LLM...")
    client = OpenAI(
        api_key="EMPTY",
        base_url="http://localhost:11434/v1"
    )
    
    llm_generator = LLMGenerator(
        client=client,
        model_name="qwen3:30b-a3b-thinking-2507-q4_K_M"
    )
    
    # Test connection
    try:
        client.chat.completions.create(
            model="qwen3:30b-a3b-thinking-2507-q4_K_M",
            messages=[{"role": "user", "content": "test"}],
            max_tokens=10
        )
        print("✅ Ollama connected")
    except:
        print("⚠️  Ollama not running. Please run:")
        print("   ollama run qwen3:30b-a3b-thinking-2507-q4_K_M")
        print("\n   Using mock responses for now.")
    
    # 3. Initialize retriever
    print("\n🔍 Initializing retriever...")
    retriever = HippoRAG2Retriever(
        llm_generator=llm_generator,
        sentence_encoder=sentence_encoder,
        data=data
    )
    
    print("✅ Ready for Q&A!")
    return retriever, llm_generator

def ask_question(retriever, llm_generator, question, show_passages=True):
    """Ask a question and get answer."""
    
    print(f"\n❓ {question}")
    print("-" * 70)
    
    # Retrieve
    passages, node_ids, scores = retriever.retrieve(question, topN=5)
    
    print(f"📊 Retrieved {len(passages)} passages")
    print(f"   Scores: {[f'{s:.4f}' for s in scores]}")
    
    # Show passages if requested
    if show_passages:
        print("\n📄 Top passages:")
        for i, (passage, score) in enumerate(zip(passages[:3], scores[:3]), 1):
            preview = passage[:300] + "..." if len(passage) > 300 else passage
            print(f"\n  {i}. (Score {score:.4f})")
            print(f"     {preview}")
    
    # Generate answer
    context = "\n\n---\n\n".join(passages)
    print(f"\n💬 Generating answer...")
    
    try:
        answer = llm_generator.generate_with_context(
            question=question,
            context=context,
            max_new_tokens=512,
            temperature=0.7
        )
        
        print(f"\n💡 Answer:")
        print(answer)
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main interactive Q&A loop."""
    
    print("=" * 70)
    print("HippoRAG2 Interactive Q&A")
    print("Using pdf_dataset with 1,207 passages")
    print("=" * 70)
    
    # Setup
    retriever, llm_generator = setup_pipeline()
    
    # Interactive loop
    print("\n" + "=" * 70)
    print("Ready! Type your questions (or 'exit' to quit)")
    print("Commands: 'test' for test queries, 'help' for options")
    print("=" * 70)
    
    while True:
        try:
            question = input("\n❓ Your question: ").strip()
            
            if question.lower() == 'exit':
                print("👋 Goodbye!")
                break
                
            elif question.lower() == 'help':
                print("\nCommands:")
                print("  'test' - Run test queries")
                print("  'short' - Toggle passage display")
                print("  'exit' - Quit")
                continue
                
            elif question.lower() == 'test':
                test_queries = [
                    "What is an OCO order?",
                    "How does risk reversal work?",
                    "What is Market Maker Cockpit?"
                ]
                for q in test_queries:
                    ask_question(retriever, llm_generator, q, show_passages=False)
                    print("\n" + "=" * 70)
                continue
                
            elif question:
                ask_question(retriever, llm_generator, question)
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()