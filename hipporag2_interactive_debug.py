#!/usr/bin/env python3
"""
Interactive HippoRAG2 Q&A with Full Debug Visibility
This script provides complete transparency into the retrieval process with adjustable parameters.
"""

import os
import sys
import json
import time
from pathlib import Path
from typing import Dict, List, Tuple, Any
from sentence_transformers import SentenceTransformer
from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
from atlas_rag.vectorstore.create_graph_index import create_embeddings_and_index
from atlas_rag.retriever import HippoRAG2Retriever
from atlas_rag.llm_generator import LLMGenerator
from openai import OpenAI
import numpy as np

# Import the new model manager for dynamic model selection
from ollama_model_manager import OllamaModelManager

class InteractiveHippoRAG2Debug:
    """Interactive HippoRAG2 with full debug visibility and parameter control."""
    
    def __init__(self):
        """Initialize with default parameters."""
        
        # Configurable parameters
        self.params = {
            'dataset_dir': 'import/pdf_dataset',  # Using the CORRECT dataset
            'topN': 5,
            'damping_factor': 0.85,
            'max_hops': 2,
            'use_ollama': True,
            'ollama_model': 'qwen3:30b-a3b-thinking-2507-q4_K_M',
            'temperature': 0.7,
            'max_tokens': 2048,  # Increased from 512 to reduce truncation
            'detail_level': 'full',  # minimal, normal, full, debug
            'show_graph_traversal': True,
            'show_pagerank_scores': True,
            'show_context': True,
            'show_embeddings': False,
            'response_mode': 'detailed',  # concise, balanced, detailed, comprehensive
            'system_prompt_template': 'detailed',  # detailed, concise, balanced, custom
            'custom_system_prompt': 'As an advanced reading comprehension assistant, your task is to analyze text passages and corresponding questions meticulously. If the information is not enough, you can use your own knowledge to answer the question. Your response start after "Thought: ", where you will methodically break down the reasoning process, illustrating how you arrive at conclusions. Conclude with "Answer: " followed by a comprehensive, detailed response with full explanations and context.',
            'show_system_prompt': True
        }
        
        # Components
        self.data = None
        self.sentence_encoder = None
        self.retriever = None
        self.llm_generator = None
        
        # Debug data
        self.last_query_debug = {}
        
        # Model manager for dynamic selection
        self.model_manager = OllamaModelManager()
        
    def build_system_prompt(self):
        """Build custom system prompt based on current parameters."""
        
        # Base instruction template
        base_instruction = ('As an advanced reading comprehension assistant, your task is to analyze text passages and corresponding questions meticulously. '
                          'If the information is not enough, you can use your own knowledge to answer the question. '
                          'Your response start after "Thought: ", where you will methodically break down the reasoning process, illustrating how you arrive at conclusions. ')
        
        # Build response instruction based on response_mode/system_prompt_template
        if self.params['system_prompt_template'] == 'custom':
            return self.params['custom_system_prompt']
        elif self.params['system_prompt_template'] == 'concise':
            conclusion_instruction = 'Conclude with "Answer: " followed by a brief, direct response as a noun phrase, no elaborations.'
        elif self.params['system_prompt_template'] == 'balanced':
            conclusion_instruction = 'Conclude with "Answer: " followed by a clear, balanced response in 2-3 sentences with key explanations.'
        elif self.params['system_prompt_template'] == 'detailed':
            conclusion_instruction = 'Conclude with "Answer: " followed by a comprehensive, detailed response with full explanations and context.'
        else:
            # Fallback to current response_mode logic
            if self.params['response_mode'] == 'concise':
                conclusion_instruction = 'Conclude with "Answer: " followed by a brief, direct response.'
            elif self.params['response_mode'] == 'balanced':
                conclusion_instruction = 'Conclude with "Answer: " followed by a clear answer in 2-3 sentences.'
            elif self.params['response_mode'] == 'detailed':
                conclusion_instruction = 'Conclude with "Answer: " followed by a detailed answer with explanations.'
            else:  # comprehensive
                conclusion_instruction = 'Conclude with "Answer: " followed by a comprehensive answer with full details and examples.'
        
        return base_instruction + conclusion_instruction
    
    def print_params(self):
        """Display current parameters."""
        print("\n📊 Current Parameters:")
        print("-" * 50)
        for key, value in self.params.items():
            print(f"  {key:20s}: {value}")
        print("-" * 50)
    
    def modify_params(self):
        """Interactive parameter modification."""
        print("\n⚙️  Parameter Modification")
        print("-" * 50)
        print("Available parameters:")
        for i, (key, value) in enumerate(self.params.items(), 1):
            print(f"  {i:2d}. {key:20s} = {value}")
        print("  0. Back to main menu")
        
        try:
            choice = input("\nSelect parameter to modify (0-{}): ".format(len(self.params)))
            choice = int(choice)
            
            if choice == 0:
                return
            
            if 1 <= choice <= len(self.params):
                param_name = list(self.params.keys())[choice - 1]
                current_value = self.params[param_name]
                
                print(f"\nCurrent value of '{param_name}': {current_value}")
                
                # Special handling for different parameter types
                if param_name == 'ollama_model':
                    # Use interactive model selection for Ollama models
                    print("🔍 Discovering available Ollama models...")
                    new_value = self.model_manager.interactive_model_selection(current_value)
                    if new_value is None:  # User cancelled or error
                        print("❌ Model selection cancelled")
                        return
                    # Skip the generic input and type conversion
                    self.params[param_name] = new_value
                    print(f"✅ Updated {param_name} to {self.params[param_name]}")
                    return
                elif param_name == 'detail_level':
                    print("Options: minimal, normal, full, debug")
                elif param_name == 'response_mode':
                    print("Options: concise, balanced, detailed, comprehensive")
                elif param_name == 'system_prompt_template':
                    print("Options: detailed, concise, balanced, custom")
                elif param_name == 'custom_system_prompt':
                    print("Enter your custom system prompt (can be multiple lines):")
                    print("Current prompt:")
                    print(f"  {current_value[:200]}..." if len(current_value) > 200 else f"  {current_value}")
                    print("\nEnter new prompt (or press Enter to keep current):")
                    new_prompt = input().strip()
                    if new_prompt:
                        self.params[param_name] = new_prompt
                        print(f"✅ Updated custom system prompt ({len(new_prompt)} characters)")
                    else:
                        print("✅ Keeping current system prompt")
                    return
                elif isinstance(current_value, bool):
                    print("Options: true, false")
                elif isinstance(current_value, int):
                    print("Enter an integer value")
                elif isinstance(current_value, float):
                    print("Enter a float value")
                
                new_value = input(f"New value for '{param_name}': ").strip()
                
                # Type conversion
                if isinstance(current_value, bool):
                    self.params[param_name] = new_value.lower() in ['true', '1', 'yes']
                elif isinstance(current_value, int):
                    self.params[param_name] = int(new_value)
                elif isinstance(current_value, float):
                    self.params[param_name] = float(new_value)
                else:
                    self.params[param_name] = new_value
                
                print(f"✅ Updated {param_name} to {self.params[param_name]}")
                
        except (ValueError, IndexError) as e:
            print(f"❌ Invalid input: {e}")
    
    def offer_model_selection(self) -> bool:
        """Offer model selection at startup.
        
        Returns:
            True if user made a selection (or skipped), False if there was an error
        """
        try:
            if not self.model_manager.is_ollama_available():
                print("⚠️  Ollama is not available - using default model")
                return True
            
            print("\n🔧 Optional: Select Ollama Model")
            print("=" * 70)
            
            current_model = self.params.get('ollama_model')
            available_models = self.model_manager.get_model_names()
            
            if not available_models:
                print("❌ No models available")
                return True
            
            print(f"Current model: {current_model}")
            
            # Quick validation of current model
            if current_model not in available_models:
                print("⚠️  Current model not found in available models!")
                print("   You may want to select a different model.")
            
            choice = input("\nWould you like to select a different model? (y/N): ").strip().lower()
            
            if choice in ['y', 'yes']:
                selected_model = self.model_manager.interactive_model_selection(current_model)
                if selected_model and selected_model != current_model:
                    self.params['ollama_model'] = selected_model
                    print(f"✅ Updated model to: {selected_model}")
                    return True
                elif selected_model == current_model:
                    print("✅ Keeping current model")
                    return True
                else:
                    print("❌ Model selection cancelled - keeping current model")
                    return True
            else:
                print("✅ Using current model")
                return True
                
        except Exception as e:
            print(f"⚠️  Error during model selection: {e}")
            print("   Continuing with current model")
            return True
    
    def load_data(self):
        """Load data with debug output."""
        print("\n📊 Loading Data")
        print("=" * 70)
        print(f"Dataset: {self.params['dataset_dir']}")
        print(f"Expected passages: ~1207 (pdf_dataset)")
        
        # Initialize embedding model
        encoder_model_name = "sentence-transformers/all-mpnet-base-v2"
        print(f"Embedding model: {encoder_model_name}")
        
        self.sentence_encoder = SentenceEmbedding(
            SentenceTransformer(encoder_model_name)
        )
        
        # Load data
        print("\n⏳ Loading graph and embeddings...")
        start_time = time.time()
        
        self.data = create_embeddings_and_index(
            sentence_encoder=self.sentence_encoder,
            model_name=encoder_model_name,
            working_directory=self.params['dataset_dir'],
            keyword="pdf_dataset",
            include_concept=True,
            include_events=True,
            normalize_embeddings=True,
            text_batch_size=64,
            node_and_edge_batch_size=256
        )
        
        load_time = time.time() - start_time
        
        print(f"\n✅ Data loaded in {load_time:.2f}s")
        print(f"  - Graph nodes: {len(self.data['KG'].nodes)}")
        print(f"  - Entity/Event/Concept nodes: {len(self.data['node_list'])}")
        print(f"  - Edges: {len(self.data['edge_list'])}")
        print(f"  - Text passages: {len(self.data['text_dict'])}")
        
        if len(self.data['text_dict']) < 1000:
            print(f"\n⚠️  WARNING: Only {len(self.data['text_dict'])} passages loaded!")
            print("  This might be the wrong dataset. Expected ~1207 for pdf_dataset.")
        
        return True  # Return True on successful load
    
    def setup_llm(self):
        """Setup LLM with debug output."""
        print("\n🤖 Setting up LLM")
        print("=" * 70)
        
        if self.params['use_ollama']:
            print(f"Using Ollama: {self.params['ollama_model']}")
            
            client = OpenAI(
                api_key="EMPTY",
                base_url="http://localhost:11434/v1"
            )
            
            self.llm_generator = LLMGenerator(
                client=client,
                model_name=self.params['ollama_model']
            )
            
            # Test connection
            try:
                print("Testing Ollama connection...")
                test_response = client.chat.completions.create(
                    model=self.params['ollama_model'],
                    messages=[{"role": "user", "content": "test"}],
                    max_tokens=10,
                    temperature=0.1
                )
                print("✅ Ollama connected successfully")
            except Exception as e:
                print(f"⚠️  Ollama connection failed: {e}")
                print(f"   Run: ollama run {self.params['ollama_model']}")
                self.params['use_ollama'] = False
        
        if not self.params['use_ollama']:
            print("Using mock LLM for testing")
            client = OpenAI(api_key="EMPTY", base_url="http://localhost:8000/v1")
            self.llm_generator = LLMGenerator(client=client, model_name="mock")
        
        return True  # Return True on successful setup
    
    def initialize_retriever(self):
        """Initialize retriever with debug hooks."""
        print("\n🔍 Initializing HippoRAG2Retriever")
        print("=" * 70)
        
        # Create custom retriever with debug capabilities
        class DebugHippoRAG2Retriever(HippoRAG2Retriever):
            def __init__(self, parent, *args, **kwargs):
                super().__init__(*args, **kwargs)
                self.parent = parent
            
            def retrieve_personalization_dict(self, query: str, topN: int = 30, weight_adjust: float = 0.05):
                """Override to capture debug info."""
                if self.parent.params['detail_level'] in ['full', 'debug']:
                    print(f"\n🔍 Query Embedding Phase")
                    print(f"  Query: '{query}'")
                    print(f"  Top-k for initial retrieval: {topN}")
                
                # Call parent method
                result = super().retrieve_personalization_dict(query, topN, weight_adjust)
                
                # Capture debug info
                self.parent.last_query_debug['personalization_dict'] = result
                
                if self.parent.params['show_graph_traversal']:
                    print(f"\n📊 Graph Traversal Results:")
                    print(f"  Nodes selected: {len(result)}")
                    if self.parent.params['detail_level'] == 'debug':
                        for node_id, score in list(result.items())[:5]:
                            print(f"    {node_id[:30]}...: {score:.6f}")
                
                return result
            
            def retrieve(self, query: str, topN: int = None):
                """Override to capture full debug info."""
                if topN is None:
                    topN = self.parent.params['topN']
                
                print(f"\n🎯 Retrieval Phase")
                print(f"  TopN: {topN}")
                
                # Time the retrieval
                start_time = time.time()
                passages, node_ids, scores = super().retrieve(query, topN)
                retrieval_time = time.time() - start_time
                
                print(f"  Retrieval time: {retrieval_time:.3f}s")
                
                # Capture debug info
                self.parent.last_query_debug['passages'] = passages
                self.parent.last_query_debug['node_ids'] = node_ids
                self.parent.last_query_debug['scores'] = scores
                
                if self.parent.params['show_pagerank_scores']:
                    print(f"\n📈 PageRank Scores:")
                    for i, (node_id, score) in enumerate(zip(node_ids, scores)):
                        print(f"  {i+1}. Node {node_id[:30]}...: {score:.6f}")
                
                return passages, node_ids, scores
        
        self.retriever = DebugHippoRAG2Retriever(
            parent=self,
            llm_generator=self.llm_generator,
            sentence_encoder=self.sentence_encoder,
            data=self.data
        )
        
        print("✅ Debug-enabled retriever initialized")
        return True  # Return True on successful initialization
    
    def ask_question(self, question: str = None):
        """Ask a question with full debug output."""
        
        if question is None:
            question = input("\n❓ Enter your question: ").strip()
            if not question:
                return
        
        print("\n" + "=" * 70)
        print(f"🔍 Processing: {question}")
        print("=" * 70)
        
        # Clear previous debug data
        self.last_query_debug = {'question': question}
        
        # Retrieve passages
        passages, node_ids, scores = self.retriever.retrieve(
            question, 
            topN=self.params['topN']
        )
        
        # Display retrieved passages based on detail level
        if self.params['detail_level'] != 'minimal':
            print(f"\n📄 Retrieved {len(passages)} Passages:")
            print("-" * 50)
            
            for i, (passage, node_id, score) in enumerate(zip(passages, node_ids, scores), 1):
                print(f"\n--- Passage {i} ---")
                print(f"Node ID: {node_id}")
                print(f"Score: {score:.6f}")
                
                # Show passage content
                if self.params['detail_level'] == 'debug':
                    print(f"Full content ({len(passage)} chars):")
                    print(passage)
                elif self.params['detail_level'] == 'full':
                    preview = passage[:500] + "..." if len(passage) > 500 else passage
                    print(f"Content preview:")
                    print(preview)
                else:  # normal
                    preview = passage[:200] + "..." if len(passage) > 200 else passage
                    print(f"Content: {preview}")
                
                # Check if it's actual text or entity
                if len(passage) < 100 and not any(c in passage for c in ['.', '!', '?', ',']):
                    print("⚠️  Warning: Looks like entity name, not passage text")
        
        # Prepare context for LLM
        context = "\n\n---\n\n".join(passages)
        
        # Show context sent to LLM
        if self.params['show_context']:
            print(f"\n📝 Context for LLM ({len(context)} chars):")
            print("-" * 50)
            if self.params['detail_level'] == 'debug':
                print(context)
            else:
                preview = context[:1000] + "..." if len(context) > 1000 else context
                print(preview)
        
        # Generate answer
        print(f"\n💬 Generating Answer")
        print(f"  Model: {self.params['ollama_model']}")
        print(f"  Temperature: {self.params['temperature']}")
        print(f"  Max tokens: {self.params['max_tokens']}")
        print(f"  Response mode: {self.params['response_mode']}")
        
        # Build custom system prompt based on current parameters
        custom_system_prompt = self.build_system_prompt()
        
        # Show system prompt if enabled
        if self.params['show_system_prompt'] and self.params['detail_level'] in ['full', 'debug']:
            print(f"\n🔧 System Prompt Template: {self.params['system_prompt_template']}")
            print(f"📝 Actual System Prompt Sent to LLM:")
            print("-" * 50)
            print(custom_system_prompt)
            print("-" * 50)
        
        # Show full query to LLM (now showing the REAL system prompt, not misleading summary)
        if self.params['detail_level'] in ['full', 'debug']:
            print(f"\n📤 Complete Query Structure:")
            print("-" * 50)
            print(f"[System Message]:\n{custom_system_prompt}")
            print(f"\n[User Message]:\n{context}\n\n{question}\nThought:")
        
        try:
            # Generate answer using the ACTUAL custom system prompt
            start_time = time.time()
            answer = self.llm_generator.generate_with_context(
                question=question,
                context=context,
                max_new_tokens=self.params['max_tokens'],
                temperature=self.params['temperature'],
                custom_system_instruction=custom_system_prompt
            )
            generation_time = time.time() - start_time
            
            print(f"\n✅ Answer generated in {generation_time:.2f}s")
            
            # Enhanced metadata display
            print(f"\n🤖 LLM Response Metadata:")
            print("-" * 50)
            print(f"  Model Used: {self.params['ollama_model']}")
            print(f"  Generation Time: {generation_time:.2f}s")
            print(f"  Response Length: {len(answer)} characters")
            print(f"  Max Tokens Limit: {self.params['max_tokens']}")
            print(f"  Temperature: {self.params['temperature']}")
            
            print("\n💡 Complete Answer:")
            print("=" * 70)
            
            # Check for potential truncation
            if len(answer) > (self.params['max_tokens'] * 3):  # Rough estimate: ~3 chars per token
                print("⚠️  Warning: Answer may be truncated due to token limit")
                print(f"   Consider increasing max_tokens (current: {self.params['max_tokens']})")
                print()
            
            print(answer)
            
            # Check if answer seems incomplete
            if answer.rstrip().endswith(('...', 'Thought:', 'thinking', 'think-hard')) or len(answer.strip()) < 50:
                print("\n⚠️  Answer appears incomplete or contains model reasoning.")
                print("   This may be due to:")
                print("   - Token limit too low (try increasing max_tokens)")
                print("   - Model showing internal reasoning (try different model)")
                print("   - Connection timeout or model issue")
            
            print("=" * 70)
            
            # Store in debug data
            self.last_query_debug['answer'] = answer
            self.last_query_debug['generation_time'] = generation_time
            
        except Exception as e:
            print(f"\n❌ Error generating answer: {e}")
            self.last_query_debug['error'] = str(e)
    
    def show_debug_info(self):
        """Display detailed debug information from last query."""
        if not self.last_query_debug:
            print("No debug data available. Run a query first.")
            return
        
        print("\n🔍 Debug Information from Last Query")
        print("=" * 70)
        
        if 'question' in self.last_query_debug:
            print(f"Question: {self.last_query_debug['question']}")
        
        if 'personalization_dict' in self.last_query_debug:
            pd = self.last_query_debug['personalization_dict']
            print(f"\nPersonalization Dict: {len(pd)} nodes")
            top_nodes = sorted(pd.items(), key=lambda x: x[1], reverse=True)[:10]
            for node_id, score in top_nodes:
                print(f"  {node_id[:50]}...: {score:.8f}")
        
        if 'scores' in self.last_query_debug:
            scores = self.last_query_debug['scores']
            print(f"\nScore Statistics:")
            print(f"  Min: {min(scores):.6f}")
            print(f"  Max: {max(scores):.6f}")
            print(f"  Mean: {np.mean(scores):.6f}")
            print(f"  Std: {np.std(scores):.6f}")
        
        if 'generation_time' in self.last_query_debug:
            print(f"\nTiming:")
            print(f"  Generation: {self.last_query_debug['generation_time']:.2f}s")
    
    def run_test_suite(self):
        """Run predefined test queries."""
        print("\n🧪 Running Test Suite")
        print("=" * 70)
        
        test_queries = [
            "What is an OCO order?",
            "How does risk reversal work in FX trading?",
            "What are the main features of Market Maker Cockpit?",
            "Explain the FIX protocol implementation",
            "How do Bank Baskets work?"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n[Test {i}/{len(test_queries)}]")
            self.ask_question(query)
            
            if i < len(test_queries):
                input("\nPress Enter for next test...")
    
    def interactive_menu(self):
        """Main interactive menu."""
        
        while True:
            print("\n" + "=" * 70)
            print("🎯 Interactive HippoRAG2 Debug Console")
            print("=" * 70)
            print("1. Ask a question")
            print("2. Modify parameters")
            print("3. Show current parameters")
            print("4. Run test suite")
            print("5. Show debug info from last query")
            print("6. Search for content in passages")
            print("7. View current system prompt")
            print("8. Edit system prompt template")
            print("9. Reset to default system prompt")
            print("0. Exit")
            
            try:
                choice = input("\nSelect option (0-9): ").strip()
                
                if choice == '0':
                    print("👋 Goodbye!")
                    break
                elif choice == '1':
                    self.ask_question()
                elif choice == '2':
                    self.modify_params()
                elif choice == '3':
                    self.print_params()
                elif choice == '4':
                    self.run_test_suite()
                elif choice == '5':
                    self.show_debug_info()
                elif choice == '6':
                    self.search_content()
                elif choice == '7':
                    self.view_system_prompt()
                elif choice == '8':
                    self.edit_system_prompt_template()
                elif choice == '9':
                    self.reset_system_prompt()
                else:
                    print("Invalid option")
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def search_content(self):
        """Search for specific content in passages."""
        keyword = input("\nEnter search term: ").strip()
        if not keyword:
            return
        
        print(f"\n🔍 Searching for '{keyword}'...")
        matches = []
        
        for node_id, text in self.data['text_dict'].items():
            if keyword.lower() in text.lower():
                matches.append((node_id, text))
        
        print(f"Found {len(matches)} passages containing '{keyword}'")
        
        if matches and self.params['detail_level'] != 'minimal':
            show_n = min(3, len(matches))
            print(f"\nShowing {show_n} samples:")
            
            for i, (node_id, text) in enumerate(matches[:show_n], 1):
                idx = text.lower().index(keyword.lower())
                start = max(0, idx - 100)
                end = min(len(text), idx + len(keyword) + 100)
                context = text[start:end]
                
                print(f"\n{i}. Node {node_id[:30]}...")
                print(f"   ...{context}...")

    def view_system_prompt(self):
        """Display the current system prompt that will be sent to LLM."""
        print("\n📝 Current System Prompt Configuration")
        print("=" * 70)
        
        current_prompt = self.build_system_prompt()
        
        print(f"🔧 Template Mode: {self.params['system_prompt_template']}")
        print(f"📊 Response Mode: {self.params['response_mode']}")
        print()
        print("📄 Actual System Prompt that will be sent to LLM:")
        print("-" * 50)
        print(current_prompt)
        print("-" * 50)
        print(f"📏 Length: {len(current_prompt)} characters")
        
    def edit_system_prompt_template(self):
        """Quick editor for system prompt template."""
        print("\n⚙️  System Prompt Template Editor")
        print("=" * 70)
        
        print("Current template:", self.params['system_prompt_template'])
        print("Current response mode:", self.params['response_mode'])
        print()
        
        print("Available templates:")
        print("1. detailed  - Comprehensive answers with full explanations")
        print("2. balanced  - Clear answers with key explanations (2-3 sentences)")  
        print("3. concise   - Brief, direct answers as noun phrases")
        print("4. custom    - Use your own custom system prompt")
        print("0. Cancel")
        
        choice = input("\nSelect template (0-4): ").strip()
        
        if choice == '1':
            self.params['system_prompt_template'] = 'detailed'
            print("✅ Set to detailed template")
        elif choice == '2':
            self.params['system_prompt_template'] = 'balanced'
            print("✅ Set to balanced template")
        elif choice == '3':
            self.params['system_prompt_template'] = 'concise'
            print("✅ Set to concise template")
        elif choice == '4':
            self.params['system_prompt_template'] = 'custom'
            print("\nEnter your custom system prompt:")
            custom_prompt = input().strip()
            if custom_prompt:
                self.params['custom_system_prompt'] = custom_prompt
                print(f"✅ Set custom prompt ({len(custom_prompt)} characters)")
            else:
                print("❌ Empty prompt - keeping current custom prompt")
        elif choice == '0':
            print("❌ Cancelled")
        else:
            print("❌ Invalid choice")
            
        # Show the resulting prompt
        if choice in ['1', '2', '3', '4']:
            print(f"\n📄 New system prompt preview:")
            print("-" * 30)
            print(self.build_system_prompt()[:200] + "..." if len(self.build_system_prompt()) > 200 else self.build_system_prompt())
            
    def reset_system_prompt(self):
        """Reset system prompt to default detailed template."""
        print("\n🔄 Resetting System Prompt")
        print("=" * 70)
        
        self.params['system_prompt_template'] = 'detailed'
        self.params['response_mode'] = 'detailed'
        self.params['custom_system_prompt'] = 'As an advanced reading comprehension assistant, your task is to analyze text passages and corresponding questions meticulously. If the information is not enough, you can use your own knowledge to answer the question. Your response start after "Thought: ", where you will methodically break down the reasoning process, illustrating how you arrive at conclusions. Conclude with "Answer: " followed by a comprehensive, detailed response with full explanations and context.'
        
        print("✅ Reset to default detailed template")
        print("📄 Current system prompt:")
        print("-" * 50)
        print(self.build_system_prompt())

def main():
    """Main function."""
    print("🚀 Interactive HippoRAG2 Debug Console")
    print("=" * 70)
    print("This console provides full visibility into the HippoRAG2 retrieval process")
    print("Using pdf_dataset with 1,207 passages (the CORRECT dataset)")
    print()
    
    # Initialize
    console = InteractiveHippoRAG2Debug()
    
    # Optional startup model selection
    if console.offer_model_selection():
        print("✅ Model selection completed")
    
    # Load data
    print("Step 1: Loading data...")
    console.load_data()
    
    # Setup LLM
    print("\nStep 2: Setting up LLM...")
    console.setup_llm()
    
    # Initialize retriever
    print("\nStep 3: Initializing retriever...")
    console.initialize_retriever()
    
    # Run interactive menu
    console.interactive_menu()

if __name__ == "__main__":
    main()