#!/usr/bin/env python3
"""
Regenerate Consistent GraphML from Original CSV Files

This script regenerates the GraphML file from the original large CSV files 
that match the embedding dimensions (62K nodes, 100K edges).

The goal is to create a GraphML file that is consistent with the existing
embedding arrays to fix the HippoRAG2 indexing issues.
"""

import os
import csv
import json
import networkx as nx
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def regenerate_graphml_from_csv(
    data_directory="/Users/<USER>/Documents/Trainings/AutoSchemaKG/AutoSchemaKG/import/pdf_dataset",
    output_filename="knowledge_graph.graphml"
):
    """
    Regenerate GraphML from the original large CSV files.
    
    Uses the CSV files created on Aug 2nd that match our embedding dimensions:
    - 62,157 nodes
    - 111,787 edges (will be limited to 100K in embeddings)
    - 1,207 text entries
    """
    
    data_dir = Path(data_directory)
    
    # Input files (from Aug 2nd 09:04)
    triple_nodes_file = data_dir / "triples_csv" / "triple_nodes__from_json_without_emb.csv"
    text_nodes_file = data_dir / "triples_csv" / "text_nodes__from_json.csv"
    triple_edges_file = data_dir / "triples_csv" / "triple_edges__from_json_without_emb.csv"
    text_edges_file = data_dir / "triples_csv" / "text_edges__from_json.csv"
    concept_edges_file = data_dir / "concept_csv" / "triple_edges__from_json_with_concept.csv"
    concept_nodes_file = data_dir / "concept_csv" / "concept_nodes.csv"
    
    # Output files
    output_graphml = data_dir / "kg_graphml" / output_filename
    output_pkl = data_dir / "kg_graphml" / "knowledge_graph.pkl"
    
    logger.info(f"🚀 Starting GraphML regeneration from original CSV files")
    logger.info(f"   Data directory: {data_dir}")
    logger.info(f"   Output GraphML: {output_graphml}")
    
    # Verify input files exist
    required_files = [triple_nodes_file, text_nodes_file, triple_edges_file, text_edges_file]
    for file_path in required_files:
        if not file_path.exists():
            raise FileNotFoundError(f"Required file not found: {file_path}")
        logger.info(f"   ✅ Found: {file_path.name}")
    
    # Create directed graph
    G = nx.DiGraph()
    
    # Track statistics
    stats = {
        'triple_nodes': 0,
        'text_nodes': 0,
        'concept_nodes': 0,
        'triple_edges': 0,
        'text_edges': 0,
        'concept_edges': 0,
        'total_nodes': 0,
        'total_edges': 0
    }
    
    logger.info("📊 Adding nodes to graph...")
    
    # Add triple nodes
    logger.info(f"   Adding triple nodes from {triple_nodes_file.name}")
    with open(triple_nodes_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            # CSV uses :ID format for Neo4j import
            node_id = str(row.get('name:ID', row.get(':ID', '')))
            if node_id:
                # Add node with all available attributes
                node_attrs = {
                    'type': str(row.get('type', 'entity')),
                    'name': str(node_id),  # Use the ID as name
                    'file_id': str(node_id),  # HippoRAG2 requirement
                    'id': str(node_id)  # Compatibility
                }
                
                # Add other available attributes
                for key, value in row.items():
                    if key not in ['name:ID', ':ID', 'name'] and value:
                        node_attrs[key] = str(value)
                
                G.add_node(node_id, **node_attrs)
                stats['triple_nodes'] += 1
    
    logger.info(f"      ✅ Added {stats['triple_nodes']} triple nodes")
    
    # Add text nodes
    logger.info(f"   Adding text nodes from {text_nodes_file.name}")
    with open(text_nodes_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            # CSV uses text_id:ID format
            node_id = str(row.get('text_id:ID', ''))
            if node_id:
                # Add node with text-specific attributes
                text_content = str(row.get('original_text', ''))
                node_attrs = {
                    'type': 'text',
                    'name': f'Text {node_id[:8]}...',  # Shortened name
                    'file_id': str(node_id),  # HippoRAG2 requirement
                    'id': str(node_id),  # Compatibility
                    'text': text_content  # Full text content
                }
                
                # Add other available attributes
                for key, value in row.items():
                    if key not in ['text_id:ID', 'original_text'] and value:
                        node_attrs[key] = str(value)
                
                G.add_node(node_id, **node_attrs)
                stats['text_nodes'] += 1
    
    logger.info(f"      ✅ Added {stats['text_nodes']} text nodes")
    
    # Add concept nodes if available
    if concept_nodes_file.exists():
        logger.info(f"   Adding concept nodes from {concept_nodes_file.name}")
        with open(concept_nodes_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                node_id = str(row.get('id', row.get('concept', '')))
                if node_id:
                    node_attrs = {
                        'type': 'concept',
                        'name': str(row.get('concept', row.get('name', node_id))),
                        'file_id': str(node_id),  # HippoRAG2 requirement
                        'id': str(node_id)  # Compatibility
                    }
                    
                    # Add other available attributes
                    for key, value in row.items():
                        if key not in ['id', 'name', 'concept'] and value:
                            node_attrs[key] = str(value)
                    
                    G.add_node(node_id, **node_attrs)
                    stats['concept_nodes'] += 1
    
    logger.info(f"      ✅ Added {stats['concept_nodes']} concept nodes")
    
    logger.info("🔗 Adding edges to graph...")
    
    # Add triple edges (relationships between entities)
    logger.info(f"   Adding triple edges from {triple_edges_file.name}")
    with open(triple_edges_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            # CSV uses Neo4j format: :START_ID, :END_ID
            source = str(row.get(':START_ID', ''))
            target = str(row.get(':END_ID', ''))
            relation = str(row.get('relation', 'related_to'))
            
            if source and target and source in G.nodes and target in G.nodes:
                edge_attrs = {
                    'relation': relation,
                    'type': str(row.get(':TYPE', 'relation')),
                    'file_id': f"{source}_{target}"  # Create file_id from source_target
                }
                
                # Add other available attributes
                for key, value in row.items():
                    if key not in [':START_ID', ':END_ID', 'relation', ':TYPE'] and value:
                        edge_attrs[key] = str(value)
                
                G.add_edge(source, target, **edge_attrs)
                stats['triple_edges'] += 1
    
    logger.info(f"      ✅ Added {stats['triple_edges']} triple edges")
    
    # Add text edges (connections between text and entities)
    logger.info(f"   Adding text edges from {text_edges_file.name}")
    with open(text_edges_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            # CSV uses Neo4j format: :START_ID, :END_ID, :TYPE
            source = str(row.get(':START_ID', ''))
            target = str(row.get(':END_ID', ''))
            edge_type = str(row.get(':TYPE', 'Source'))
            
            if source and target and source in G.nodes and target in G.nodes:
                edge_attrs = {
                    'relation': edge_type.lower(),  # Use type as relation
                    'type': edge_type,
                    'file_id': f"{source}_{target}"  # Create file_id from source_target
                }
                
                # Add other available attributes
                for key, value in row.items():
                    if key not in [':START_ID', ':END_ID', ':TYPE'] and value:
                        edge_attrs[key] = str(value)
                
                G.add_edge(source, target, **edge_attrs)
                stats['text_edges'] += 1
    
    logger.info(f"      ✅ Added {stats['text_edges']} text edges")
    
    # Add concept edges if available
    if concept_edges_file.exists():
        logger.info(f"   Adding concept edges from {concept_edges_file.name}")
        with open(concept_edges_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                source = str(row.get('source', ''))
                target = str(row.get('target', ''))
                relation = str(row.get('relation', 'related_to'))
                
                if source and target and source in G.nodes and target in G.nodes:
                    edge_attrs = {
                        'relation': relation,
                        'type': 'concept_relation',
                        'file_id': str(row.get('file_id', ''))
                    }
                    
                    # Add other available attributes
                    for key, value in row.items():
                        if key not in ['source', 'target', 'relation'] and value:
                            edge_attrs[key] = str(value)
                    
                    G.add_edge(source, target, **edge_attrs)
                    stats['concept_edges'] += 1
    
    logger.info(f"      ✅ Added {stats['concept_edges']} concept edges")
    
    # Final statistics
    stats['total_nodes'] = len(G.nodes)
    stats['total_edges'] = len(G.edges)
    
    logger.info("📊 GraphML Generation Complete!")
    logger.info(f"   Total Nodes: {stats['total_nodes']:,}")
    logger.info(f"     - Triple nodes: {stats['triple_nodes']:,}")
    logger.info(f"     - Text nodes: {stats['text_nodes']:,}")
    logger.info(f"     - Concept nodes: {stats['concept_nodes']:,}")
    logger.info(f"   Total Edges: {stats['total_edges']:,}")
    logger.info(f"     - Triple edges: {stats['triple_edges']:,}")
    logger.info(f"     - Text edges: {stats['text_edges']:,}")
    logger.info(f"     - Concept edges: {stats['concept_edges']:,}")
    
    # Verify we have the expected dimensions
    expected_nodes = 62157  # From embedding array
    if abs(stats['total_nodes'] - expected_nodes) > 100:  # Allow small variance
        logger.warning(f"⚠️  Node count mismatch! Expected ~{expected_nodes}, got {stats['total_nodes']}")
    else:
        logger.info(f"✅ Node count matches expected dimensions (~{expected_nodes})")
    
    # Save GraphML
    logger.info(f"💾 Saving GraphML to {output_graphml}")
    os.makedirs(output_graphml.parent, exist_ok=True)
    nx.write_graphml(G, str(output_graphml))
    
    # Save pickle for faster loading
    logger.info(f"💾 Saving pickle to {output_pkl}")
    import pickle
    with open(output_pkl, 'wb') as f:
        pickle.dump(G, f)
    
    logger.info("✅ GraphML regeneration completed successfully!")
    
    return G, stats

def main():
    """Main function to regenerate GraphML."""
    try:
        graph, stats = regenerate_graphml_from_csv()
        
        print("\n🎉 SUCCESS: Consistent GraphML regenerated!")
        print(f"   Nodes: {stats['total_nodes']:,} (should match 62,157 embedding array)")
        print(f"   Edges: {stats['total_edges']:,} (will be limited to 100K in embeddings)")
        print(f"   Output: knowledge_graph.graphml")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ GraphML regeneration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)