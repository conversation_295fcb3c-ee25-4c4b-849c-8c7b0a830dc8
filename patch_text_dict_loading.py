#!/usr/bin/env python3
"""
Patch the text_dict loading to use the corrected version with actual text.
"""

import os
import pickle
from pathlib import Path

def patch_load_data():
    """Update the precomputed text_dict file with corrected content."""
    
    print("=" * 70)
    print("🔧 Patching text_dict Loading")
    print("=" * 70)
    
    precompute_dir = Path("import/pdf_dataset/precompute")
    
    # Load corrected text_dict
    corrected_file = precompute_dir / "pdf_dataset_corrected_text_dict.pkl"
    original_file = precompute_dir / "pdf_dataset_original_text_dict_with_node_id.pkl"
    
    if not corrected_file.exists():
        print(f"❌ Corrected text_dict not found: {corrected_file}")
        print("   Run: python fix_text_dict_content.py first")
        return False
    
    # Backup original
    if original_file.exists():
        backup_file = precompute_dir / "pdf_dataset_original_text_dict_with_node_id.pkl.backup"
        if not backup_file.exists():
            print(f"\n📦 Backing up original to {backup_file.name}")
            import shutil
            shutil.copy2(original_file, backup_file)
    
    # Load corrected dict
    print(f"\n📂 Loading corrected text_dict...")
    with open(corrected_file, 'rb') as f:
        corrected_dict = pickle.load(f)
    
    print(f"   Loaded {len(corrected_dict)} entries")
    
    # Verify content
    print("\n✅ Verifying content:")
    search_terms = ["resting order", "Supersonic", "OCO"]
    for term in search_terms:
        count = sum(1 for text in corrected_dict.values() 
                   if isinstance(text, str) and term.lower() in text.lower())
        print(f"   '{term}': {count} passages")
    
    # Save as the main text_dict file
    print(f"\n💾 Updating main text_dict file...")
    with open(original_file, 'wb') as f:
        pickle.dump(corrected_dict, f)
    
    print("✅ Text dict patched successfully!")
    
    # Show sample
    print("\n📄 Sample entry:")
    for i, (key, value) in enumerate(list(corrected_dict.items())[:1]):
        print(f"Key: {key[:50]}...")
        if isinstance(value, str):
            preview = value[:300] + "..." if len(value) > 300 else value
            print(f"Text: {preview}")
    
    return True

def main():
    """Main function."""
    success = patch_load_data()
    
    if success:
        print("\n✅ SUCCESS! The text_dict now contains actual passage text.")
        print("You can now run the Q&A system and it should return real text:")
        print("  python hipporag2_interactive_debug.py")
    else:
        print("\n❌ Failed to patch text_dict")

if __name__ == "__main__":
    main()