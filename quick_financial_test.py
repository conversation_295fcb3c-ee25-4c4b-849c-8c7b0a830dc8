#!/usr/bin/env python3
"""
Quick test of known financial terms that should work well
"""

import os
import sys
from pathlib import Path

# Set environment and add path
os.environ["TOKENIZERS_PARALLELISM"] = "false"
sys.path.insert(0, str(Path(__file__).parent))

from test_entity_fix_simple import setup_ollama_llm
from setup_embedding_model import setup_embedding_model
from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
from atlas_rag.retriever.inference_config import InferenceConfig
import pickle

def main():
    print("🎯 Quick Test of Known Financial Terms")
    print("="*50)
    
    # Setup (same as working solution)
    embedding_model = setup_embedding_model()
    llm_generator = setup_ollama_llm()
    
    data_file = Path("import/pdf_dataset/complete_data.pkl")
    with open(data_file, 'rb') as f:
        complete_data = pickle.load(f)
    
    # Optimized config
    inference_config = InferenceConfig() 
    inference_config.topk_edges = 30
    inference_config.ppr_alpha = 0.15
    inference_config.ppr_max_iter = 200
    inference_config.weight_adjust = 0.1
    
    retriever = HippoRAG2Retriever(
        llm_generator=llm_generator,
        sentence_encoder=embedding_model,
        data=complete_data,
        inference_config=inference_config
    )
    
    # Test known working queries
    test_queries = [
        "MMC Market Maker Cockpit functionality",
        "Risk Reversal strategy", 
        "Bridge trading platform features",
        "SEF Market Maker configuration",
        "what is an OCO order type"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🎯 Test #{i}: '{query}'")
        
        try:
            passages, passage_ids = retriever.retrieve(query, topN=3)
            
            # Check for financial keywords
            financial_keywords = ['MMC', 'Market Maker', 'trading', 'Risk Reversal', 'Bridge', 'SEF', 'OCO', 'order', 'pricing', 'FX']
            found_any_financial = False
            
            for j, passage in enumerate(passages, 1):
                found_keywords = [kw for kw in financial_keywords if kw in passage]
                if found_keywords:
                    found_any_financial = True
                    print(f"   ✅ #{j}: Found keywords: {found_keywords[:3]}")
                    print(f"      {passage[:100]}...")
                else:
                    print(f"   #{j}: {passage[:80]}...")
            
            if found_any_financial:
                print(f"   🎉 SUCCESS: Found relevant financial content!")
            else:
                print(f"   ⚠️  No specific financial keywords found")
                
        except Exception as e:
            print(f"   ❌ Query failed: {e}")

if __name__ == "__main__":
    main()