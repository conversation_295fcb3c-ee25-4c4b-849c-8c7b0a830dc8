#!/usr/bin/env python3
"""
Safe loader for pdf_dataset that avoids segmentation faults.
This script loads pre-computed embeddings or falls back to safe computation.
"""

import os
import sys
import pickle
import faiss
import gc
from pathlib import Path
from sentence_transformers import SentenceTransformer
from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
import networkx as nx

def load_pdf_dataset_safe():
    """Load pdf_dataset data safely, using pre-computed embeddings if available."""
    
    print("🛡️ Safe PDF Dataset Loader")
    print("=" * 70)
    
    base_dir = Path("import/pdf_dataset")
    precompute_dir = base_dir / "precompute"
    
    # Initialize encoder
    print("🔤 Initializing encoder...")
    encoder_model_name = "sentence-transformers/all-mpnet-base-v2"
    sentence_model = SentenceTransformer(encoder_model_name)
    sentence_encoder = SentenceEmbedding(sentence_model)
    encoder_short = encoder_model_name.split('/')[-1]
    
    # Load graph
    print("\n📊 Loading GraphML...")
    graph_file = base_dir / "kg_graphml" / "pdf_dataset_graph.graphml"
    
    if not graph_file.exists():
        print(f"❌ GraphML not found: {graph_file}")
        print("   Run: python regenerate_graphml_with_passages.py")
        return None
    
    with open(graph_file, 'rb') as f:
        KG = nx.read_graphml(f)
    
    print(f"  ✅ Loaded graph with {len(KG.nodes)} nodes")
    
    # Check for pre-computed files
    required_files = {
        'node_list': precompute_dir / "pdf_dataset_eventTrueconceptTrue_node_list.pkl",
        'edge_list': precompute_dir / "pdf_dataset_eventTrueconceptTrue_edge_list.pkl",
        'node_embeddings': precompute_dir / f"pdf_dataset_eventTrueconceptTrue_{encoder_short}_node_embeddings.pkl",
        'edge_embeddings': precompute_dir / f"pdf_dataset_eventTrueconceptTrue_{encoder_short}_edge_embeddings.pkl",
        'node_index': precompute_dir / f"pdf_dataset_eventTrueconceptTrue_{encoder_short}_node_faiss.index",
        'edge_index': precompute_dir / f"pdf_dataset_eventTrueconceptTrue_{encoder_short}_edge_faiss.index",
        'text_embeddings': precompute_dir / f"pdf_dataset_{encoder_short}_text_embeddings.pkl",
        'text_index': precompute_dir / "pdf_dataset_text_faiss.index",
        'text_list': precompute_dir / "pdf_dataset_text_list.pkl",
        'text_dict': precompute_dir / "pdf_dataset_original_text_dict_with_node_id.pkl"
    }
    
    print("\n📁 Checking for pre-computed files...")
    missing_files = []
    for name, path in required_files.items():
        if path.exists():
            print(f"  ✅ {name}: Found")
        else:
            print(f"  ❌ {name}: Missing")
            missing_files.append(name)
    
    if missing_files:
        print(f"\n⚠️  Missing {len(missing_files)} files")
        print("   Run: python generate_pdf_embeddings_safe.py")
        print("   to generate missing embeddings safely")
        
        # Check if we can at least load partial data
        if 'text_dict' in missing_files:
            print("\n❌ Cannot proceed without text_dict")
            return None
    
    # Load all components
    print("\n📂 Loading data components...")
    data = {}
    
    try:
        # Load text components (usually these exist)
        if required_files['text_dict'].exists():
            with open(required_files['text_dict'], 'rb') as f:
                data['text_dict'] = pickle.load(f)
            print(f"  ✅ Loaded text_dict: {len(data['text_dict'])} passages")
        
        if required_files['text_embeddings'].exists():
            with open(required_files['text_embeddings'], 'rb') as f:
                data['text_embeddings'] = pickle.load(f)
            print(f"  ✅ Loaded text embeddings")
        
        if required_files['text_index'].exists():
            data['text_faiss_index'] = faiss.read_index(str(required_files['text_index']))
            print(f"  ✅ Loaded text FAISS index")
        
        # Load node/edge components if they exist
        if required_files['node_list'].exists():
            with open(required_files['node_list'], 'rb') as f:
                data['node_list'] = pickle.load(f)
            print(f"  ✅ Loaded node_list: {len(data['node_list'])} nodes")
        
        if required_files['edge_list'].exists():
            with open(required_files['edge_list'], 'rb') as f:
                data['edge_list'] = pickle.load(f)
            print(f"  ✅ Loaded edge_list: {len(data['edge_list'])} edges")
        
        if required_files['node_embeddings'].exists():
            with open(required_files['node_embeddings'], 'rb') as f:
                data['node_embeddings'] = pickle.load(f)
            print(f"  ✅ Loaded node embeddings")
        
        if required_files['edge_embeddings'].exists():
            with open(required_files['edge_embeddings'], 'rb') as f:
                data['edge_embeddings'] = pickle.load(f)
            print(f"  ✅ Loaded edge embeddings")
        
        if required_files['node_index'].exists():
            data['node_faiss_index'] = faiss.read_index(str(required_files['node_index']))
            print(f"  ✅ Loaded node FAISS index")
        
        if required_files['edge_index'].exists():
            data['edge_faiss_index'] = faiss.read_index(str(required_files['edge_index']))
            print(f"  ✅ Loaded edge FAISS index")
        
        # Add KG to data
        data['KG'] = KG
        
        print("\n✅ Data loaded successfully!")
        
        # Garbage collection
        gc.collect()
        
        return data
        
    except Exception as e:
        print(f"\n❌ Error loading data: {e}")
        print("   Try running: python generate_pdf_embeddings_safe.py")
        return None

def test_loaded_data():
    """Test that loaded data works correctly."""
    
    print("\n🧪 Testing Loaded Data")
    print("=" * 70)
    
    data = load_pdf_dataset_safe()
    
    if data is None:
        print("❌ Failed to load data")
        return False
    
    # Test retrieval
    print("\n🔍 Testing retrieval...")
    
    if 'text_dict' in data:
        # Search for specific terms
        search_terms = ["risk reversal", "OCO", "Market Maker"]
        
        for term in search_terms:
            count = sum(1 for text in data['text_dict'].values() 
                       if term.lower() in text.lower())
            print(f"  '{term}': {count} passages")
    
    print("\n✅ Data test complete!")
    return True

def main():
    """Main function."""
    test_loaded_data()

if __name__ == "__main__":
    main()