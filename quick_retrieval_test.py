#!/usr/bin/env python3
"""Quick test of HippoRAG2 with upgraded embeddings."""

import os
import sys
import pickle
from pathlib import Path

# Set environment and add path
os.environ["TOKENIZERS_PARALLELISM"] = "false"
sys.path.insert(0, str(Path(__file__).parent))

from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
from atlas_rag.llm_generator import LLMGenerator
from atlas_rag.retriever.inference_config import InferenceConfig
from setup_embedding_model import setup_embedding_model
from openai import OpenAI
import requests

def main():
    print("🔬 Quick HippoRAG2 Test")
    
    # Load data
    data_file = Path("import/pdf_dataset/complete_data.pkl")
    with open(data_file, 'rb') as f:
        complete_data = pickle.load(f)
    
    # Setup embedding model
    embedding_model = setup_embedding_model()
    
    # Setup LLM (simple mock for testing)
    llm_generator = None  # Will cause fallback to similarity
    
    # Enhanced inference config
    inference_config = InferenceConfig()
    inference_config.topk_edges = 20
    inference_config.simple_mode = True  # Use simple mode for testing
    
    # Initialize HippoRAG2
    retriever = HippoRAG2Retriever(
        llm_generator=llm_generator,
        sentence_encoder=embedding_model,
        data=complete_data,
        inference_config=inference_config
    )
    
    # Test single query
    query = "what is a risk reversal option strategy?"
    print(f"\n📝 Testing query: {query}")
    
    try:
        passages, passage_ids = retriever.retrieve(query)
        print(f"✅ Retrieved {len(passages)} results")
        
        for i, (content, passage_id) in enumerate(zip(passages[:3], passage_ids[:3]), 1):
            print(f"\n🔹 Result #{i} (ID: {passage_id})")
            print(f"   Content: {content[:200]}...")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()