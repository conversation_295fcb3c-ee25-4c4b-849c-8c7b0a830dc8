#!/usr/bin/env python3
"""
Enhanced HippoRAG2 Answer Generator with Multiple Response Modes

This module extends the original HippoRAG2 answer generation with:
1. Verbose mode to see exactly what's sent to the LLM
2. Multiple response modes (concise, detailed, balanced, custom)
3. Enhanced context formatting for better clarity
4. Improved debugging capabilities
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Any
import json
from dataclasses import dataclass
from openai import OpenAI
from ollama_model_manager import OllamaModelManager

# Configuration - Change this to use a different Ollama model
DEFAULT_OLLAMA_MODEL = "qwen3:30b-a3b-thinking-2507-q4_K_M"  # Edit this to use your preferred model

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from atlas_rag.llm_generator import LLMGenerator
from atlas_rag.llm_generator.prompt.rag_prompt import (
    cot_system_instruction,
    cot_system_instruction_kg,
    cot_system_instruction_no_doc
)


# Alternative system prompts for different response modes
DETAILED_SYSTEM_INSTRUCTION = (
    'As an advanced reading comprehension assistant, analyze the provided passages and question thoroughly. '
    'Provide a comprehensive answer that fully addresses all aspects of the question. '
    'Include relevant details, examples, and explanations from the context. '
    'Structure your response clearly with proper reasoning. '
    'Start with "Analysis: " for your detailed reasoning process, then "Answer: " for the complete response.'
)

BALANCED_SYSTEM_INSTRUCTION = (
    'As an advanced reading comprehension assistant, analyze the passages and question carefully. '
    'Provide a clear, informative answer in 2-3 sentences that addresses the key points. '
    'If the context is insufficient, you may use your knowledge but indicate this clearly. '
    'Start with "Thought: " for brief reasoning, then "Answer: " for a complete but concise response.'
)

COMPREHENSIVE_SYSTEM_INSTRUCTION = (
    'You are an expert assistant tasked with providing thorough, well-structured answers based on retrieved information. '
    'Analyze all provided passages carefully and synthesize a comprehensive response. '
    'Your answer should: 1) Address all aspects of the question, 2) Include specific details from the passages, '
    '3) Provide examples where relevant, 4) Be well-organized with clear explanations. '
    'Format: Begin with "Context Analysis: " to review the key information, '
    'then "Detailed Answer: " for your comprehensive response, '
    'and optionally "Additional Notes: " for any caveats or extra insights.'
)


@dataclass
class OllamaConfig:
    """Configuration for Ollama LLM generation with all tunable parameters."""
    
    # Model selection
    model_name: str = DEFAULT_OLLAMA_MODEL
    base_url: str = "http://localhost:11434/v1"
    
    # Generation parameters
    max_new_tokens: int = 2048
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 40
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    
    # Response format
    response_format: Dict = None
    
    # Advanced parameters
    seed: Optional[int] = None
    stop_sequences: List[str] = None
    reasoning_effort: Optional[str] = None
    
    # Retry configuration
    max_retries: int = 3
    timeout: int = 120
    
    def to_generation_kwargs(self) -> Dict[str, Any]:
        """Convert config to kwargs for LLM generation."""
        kwargs = {
            "max_new_tokens": self.max_new_tokens,
            "temperature": self.temperature,
        }
        
        if self.frequency_penalty != 0.0:
            kwargs["frequency_penalty"] = self.frequency_penalty
        
        if self.presence_penalty != 0.0:
            kwargs["presence_penalty"] = self.presence_penalty
            
        if self.response_format:
            kwargs["response_format"] = self.response_format
            
        if self.reasoning_effort:
            kwargs["reasoning_effort"] = self.reasoning_effort
            
        return kwargs


class EnhancedHippoRAG2AnswerGenerator:
    """
    Enhanced answer generation for HippoRAG2 with multiple response modes.
    
    Response modes:
    - 'concise': Original HippoRAG mode (noun phrase, no elaborations)
    - 'balanced': 2-3 sentence answers with key points
    - 'detailed': Comprehensive answers with full explanations
    - 'comprehensive': Most thorough mode with structured analysis
    - 'custom': Use a custom system prompt
    """
    
    def __init__(self, config: OllamaConfig = None):
        """Initialize with Ollama configuration."""
        self.config = config or OllamaConfig()
        self.llm_generator = self._setup_llm()
        
    def _setup_llm(self) -> Optional[LLMGenerator]:
        """Setup Ollama LLM connection."""
        try:
            import requests
            response = requests.get(
                f"{self.config.base_url.replace('/v1', '')}/api/tags",
                timeout=5
            )
            
            if response.status_code != 200:
                print(f"⚠️ Ollama not running at {self.config.base_url}")
                return None
                
            available_models = response.json().get("models", [])
            model_names = [m.get("name", "") for m in available_models]
            
            if not any(self.config.model_name in name for name in model_names):
                print(f"⚠️ Model {self.config.model_name} not found")
                print(f"   Available: {', '.join(model_names[:3])}")
                
            client = OpenAI(
                base_url=self.config.base_url,
                api_key="dummy-key",
            )
            
            llm = LLMGenerator(client=client, model_name=self.config.model_name)
            print(f"✅ Enhanced LLM initialized: {self.config.model_name}")
            return llm
            
        except Exception as e:
            print(f"❌ Failed to setup Ollama: {e}")
            return None
    
    def format_context_enhanced(self, passages: List[str], scores: List[float] = None) -> str:
        """
        Format context with clear structure and relevance indicators.
        
        Args:
            passages: List of retrieved passages
            scores: Optional relevance scores
            
        Returns:
            Formatted context string
        """
        if not passages:
            return "No relevant information retrieved."
        
        formatted_parts = ["Retrieved Information (sorted by relevance):", "="*60]
        
        for i, passage in enumerate(passages, 1):
            if scores and i-1 < len(scores):
                score = scores[i-1]
                if score < 0.001:
                    header = f"\n📄 Passage {i} [Relevance: {score:.2e}]:"
                else:
                    header = f"\n📄 Passage {i} [Relevance: {score:.4f}]:"
            else:
                header = f"\n📄 Passage {i}:"
            
            formatted_parts.append(header)
            formatted_parts.append("-" * 50)
            
            # Limit passage length for display
            if len(passage) > 1000:
                display_passage = passage[:1000] + "...[truncated]"
            else:
                display_passage = passage
                
            formatted_parts.append(display_passage)
            formatted_parts.append("")  # blank line
        
        return "\n".join(formatted_parts)
    
    def get_system_prompt(self, response_mode: str, custom_prompt: str = None) -> str:
        """
        Get the appropriate system prompt based on response mode.
        
        Args:
            response_mode: One of 'concise', 'balanced', 'detailed', 'comprehensive', 'custom'
            custom_prompt: Custom system prompt (only used if response_mode is 'custom')
            
        Returns:
            System prompt string
        """
        if response_mode == "detailed":
            return DETAILED_SYSTEM_INSTRUCTION
        elif response_mode == "balanced":
            return BALANCED_SYSTEM_INSTRUCTION
        elif response_mode == "comprehensive":
            return COMPREHENSIVE_SYSTEM_INSTRUCTION
        elif response_mode == "custom" and custom_prompt:
            return custom_prompt
        else:  # concise (default)
            return cot_system_instruction
    
    def format_user_message(self, context: str, question: str, response_mode: str) -> str:
        """
        Format user message based on response mode.
        
        Args:
            context: Formatted context
            question: User's question
            response_mode: Response mode
            
        Returns:
            Formatted user message
        """
        if response_mode == "detailed":
            return f"""{context}

Based on the above information, please answer the following question comprehensively:

❓ Question: {question}

Provide a detailed explanation covering all relevant aspects from the retrieved passages.
Analysis:"""
        
        elif response_mode == "balanced":
            return f"""{context}

❓ Question: {question}

Please provide a clear and informative answer based on the retrieved information.
Thought:"""
        
        elif response_mode == "comprehensive":
            return f"""{context}

❓ Question: {question}

Synthesize the information from all relevant passages to provide a thorough answer.
Context Analysis:"""
        
        else:  # concise (default HippoRAG format)
            return f"{context}\n\n{question}\nThought:"
    
    def generate_answer_from_passages(
        self,
        question: str,
        passages: List[str],
        passage_ids: List[str] = None,
        scores: List[float] = None,
        response_mode: str = "concise",
        custom_prompt: str = None,
        verbose: bool = False,
        use_enhanced_formatting: bool = True
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Generate answer from retrieved passages with configurable response modes.
        
        Args:
            question: User's query
            passages: List of retrieved text passages
            passage_ids: Optional passage IDs
            scores: Optional relevance scores
            response_mode: One of 'concise', 'balanced', 'detailed', 'comprehensive', 'custom'
            custom_prompt: Custom system prompt (only for 'custom' mode)
            verbose: If True, print debug information
            use_enhanced_formatting: If True, use enhanced context formatting
            
        Returns:
            Tuple of (answer, metadata)
        """
        if not self.llm_generator:
            return "LLM not available", {"error": "Ollama not configured"}
        
        # Format context
        if use_enhanced_formatting:
            context = self.format_context_enhanced(passages, scores)
        else:
            # Original formatting
            if scores:
                context_parts = []
                for i, (passage, score) in enumerate(zip(passages, scores)):
                    context_parts.append(f"[Relevance: {score:.4f}]\n{passage}")
                context = "\n\n".join(context_parts)
            else:
                context = "\n\n".join(passages)
        
        # Get appropriate system prompt
        system_prompt = self.get_system_prompt(response_mode, custom_prompt)
        
        # Format user message
        user_message = self.format_user_message(context, question, response_mode)
        
        # Debug output if verbose
        if verbose:
            print("\n" + "="*80)
            print("🔍 DEBUG: LLM INPUT")
            print("="*80)
            print(f"\n📋 Response Mode: {response_mode}")
            print(f"🌡️ Temperature: {self.config.temperature}")
            print(f"📏 Max Tokens: {self.config.max_new_tokens}")
            print(f"📚 Passages: {len(passages)}")
            
            print(f"\n🤖 SYSTEM PROMPT:")
            print("-"*40)
            print(system_prompt)
            
            print(f"\n👤 USER MESSAGE:")
            print("-"*40)
            # Limit display for readability
            if len(user_message) > 2000:
                print(user_message[:2000] + "\n...[truncated for display]...")
            else:
                print(user_message)
            
            print("\n" + "="*80)
            print("⏳ Generating response...")
            print("="*80)
        
        # Generate answer based on mode
        if response_mode == "concise":
            # Use original HippoRAG method for compatibility
            answer = self.llm_generator.generate_with_context(
                question=question,
                context=context if not use_enhanced_formatting else "\n\n".join(passages),
                max_new_tokens=self.config.max_new_tokens,
                temperature=self.config.temperature
            )
        else:
            # Use custom prompts for other modes
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ]
            answer = self.llm_generator.generate_response(
                messages,
                max_new_tokens=self.config.max_new_tokens,
                temperature=self.config.temperature
            )
        
        # Extract answer based on mode
        final_answer = self._extract_answer(answer, response_mode)
        
        # Debug output if verbose
        if verbose:
            print(f"\n🤖 RAW LLM RESPONSE:")
            print("-"*40)
            print(answer[:1000] + "..." if len(answer) > 1000 else answer)
            print("\n" + "="*80)
        
        metadata = {
            "model": self.config.model_name,
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_new_tokens,
            "num_passages": len(passages),
            "response_mode": response_mode,
            "enhanced_formatting": use_enhanced_formatting
        }
        
        return final_answer, metadata
    
    def _extract_answer(self, response: str, response_mode: str) -> str:
        """
        Extract final answer from response based on mode.
        
        Args:
            response: Raw LLM response
            response_mode: Response mode used
            
        Returns:
            Extracted answer
        """
        if response_mode in ["detailed", "comprehensive"]:
            # Look for different markers based on mode
            if "Detailed Answer:" in response:
                answer = response.split("Detailed Answer:")[-1].strip()
            elif "Answer:" in response:
                answer = response.split("Answer:")[-1].strip()
            else:
                answer = response.strip()
                
            # Remove any trailing markers
            for marker in ["Analysis:", "Context Analysis:", "Additional Notes:", "Thought:"]:
                if marker in answer:
                    answer = answer.split(marker)[0].strip()
                    
            return answer
            
        else:
            # Original extraction logic for concise/balanced modes
            if "Answer:" in response:
                answer = response.split("Answer:")[-1].strip()
                if "Thought:" in answer:
                    answer = answer.split("Thought:")[0].strip()
                return answer
            elif "answer:" in response.lower():
                parts = response.lower().split("answer:")
                if len(parts) > 1:
                    pos = response.lower().find("answer:") + 7
                    return response[pos:].strip()
            
            return response.strip()
    
    def print_response_modes(self):
        """Print available response modes with descriptions."""
        print("\n📚 AVAILABLE RESPONSE MODES:")
        print("="*60)
        print("\n1. 'concise' (default):")
        print("   • Original HippoRAG mode")
        print("   • Returns noun phrase, no elaborations")
        print("   • Best for: Evaluation, short factual answers")
        
        print("\n2. 'balanced':")
        print("   • 2-3 sentence answers")
        print("   • Includes key points and brief reasoning")
        print("   • Best for: General Q&A, moderate detail")
        
        print("\n3. 'detailed':")
        print("   • Comprehensive answers with explanations")
        print("   • Includes examples and context")
        print("   • Best for: Complex questions, learning")
        
        print("\n4. 'comprehensive':")
        print("   • Most thorough mode")
        print("   • Structured analysis with multiple sections")
        print("   • Best for: Research, complete understanding")
        
        print("\n5. 'custom':")
        print("   • Use your own system prompt")
        print("   • Full control over response format")
        print("   • Best for: Specific requirements")
        print("="*60)


def demonstrate_enhanced_generation():
    """Demonstrate the enhanced answer generation with different modes."""
    print("🎯 ENHANCED HIPPORAG2 ANSWER GENERATION DEMO")
    print("="*60)
    
    # Create generator
    config = OllamaConfig(
        model_name=DEFAULT_OLLAMA_MODEL,
        temperature=0.7,
        max_new_tokens=1024
    )
    
    generator = EnhancedHippoRAG2AnswerGenerator(config)
    
    # Print available modes
    generator.print_response_modes()
    
    # Sample data
    question = "What is an OCO order and how do you place it in Bridge?"
    
    passages = [
        "OCO (One-Cancels-Other) orders consist of two orders placed simultaneously. "
        "When one order is executed, the other is automatically cancelled. "
        "This order type is particularly useful for traders who want to set both "
        "profit targets and stop losses at the same time.",
        
        "To place an OCO order in Bridge, navigate to the Trading menu and select "
        "Orders, then New Order. From the order type dropdown, select OCO. "
        "You will need to enter two price levels: one for your limit order "
        "(profit target) and one for your stop order (loss protection). "
        "Both orders will be sent to the market simultaneously.",
        
        "Bridge supports various advanced order types including Market orders, "
        "Limit orders, Stop orders, and OCO orders. The platform provides "
        "real-time order status updates and execution confirmations. "
        "All orders can be monitored through the Order Management screen."
    ]
    
    scores = [0.8234, 0.7156, 0.5923]
    
    print(f"\n❓ Question: {question}")
    print(f"📚 Retrieved: {len(passages)} passages")
    
    # Test different modes
    modes_to_test = ["concise", "balanced", "detailed"]
    
    for mode in modes_to_test:
        print(f"\n\n{'='*60}")
        print(f"🔧 Testing Mode: {mode.upper()}")
        print(f"{'='*60}")
        
        answer, metadata = generator.generate_answer_from_passages(
            question=question,
            passages=passages,
            scores=scores,
            response_mode=mode,
            verbose=True,  # Show debug info
            use_enhanced_formatting=True
        )
        
        print(f"\n✨ FINAL ANSWER ({mode}):")
        print("-"*50)
        print(answer)
        print("-"*50)
        
        print(f"\n📊 Metadata:")
        for key, value in metadata.items():
            print(f"  • {key}: {value}")
    
    return generator


if __name__ == "__main__":
    generator = demonstrate_enhanced_generation()