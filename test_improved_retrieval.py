#!/usr/bin/env python3
"""
Test script to validate improved HippoRAG2 retrieval with upgraded embeddings.
"""

import os
import sys
import time
import json
import pickle
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent))

from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
from atlas_rag.llm_generator import LLMGenerator
from atlas_rag.retriever.inference_config import InferenceConfig
from setup_embedding_model import setup_embedding_model
from openai import OpenAI
import requests

class RetrievalTester:
    """Test retrieval quality with financial domain queries."""
    
    def __init__(self, data_dir: str = "import/pdf_dataset"):
        self.data_dir = Path(data_dir)
        self.results = []
        self.setup_retriever()
    
    def setup_retriever(self):
        """Setup HippoRAG2 with enhanced configuration."""
        print("🔧 Setting up HippoRAG2 retriever...")
        
        # Load complete data
        complete_data_path = self.data_dir / "complete_data.pkl"
        with open(complete_data_path, 'rb') as f:
            self.complete_data = pickle.load(f)
        
        # Setup embedding model
        self.embedding_model = setup_embedding_model()
        
        # Setup LLM with Ollama
        print("🤖 Setting up Ollama LLM...")
        try:
            # Check if Ollama is running
            ollama_url = "http://localhost:11434"
            model_name = "qwen3:30b-a3b-instruct-2507-q4_K_M"
            
            response = requests.get(f"{ollama_url}/api/tags", timeout=5)
            if response.status_code == 200:
                client = OpenAI(
                    base_url=f"{ollama_url}/v1",
                    api_key="dummy-key",
                )
                self.llm_generator = LLMGenerator(client=client, model_name=model_name)
                print(f"   ✅ Ollama LLM ready: {model_name}")
            else:
                raise Exception("Ollama not responding")
        except Exception as e:
            print(f"⚠️  Ollama not available: {e}")
            print("   Using mock LLM for testing")
            # Create a simple mock for testing
            self.llm_generator = None
        
        # Enhanced inference config for better financial content discovery
        inference_config = InferenceConfig()
        inference_config.topk_edges = 50
        inference_config.ppr_alpha = 0.1
        inference_config.ppr_max_iter = 150
        inference_config.simple_mode = False
        
        # Initialize HippoRAG2 with the complete data dictionary
        self.retriever = HippoRAG2Retriever(
            llm_generator=self.llm_generator,
            sentence_encoder=self.embedding_model,
            data=self.complete_data,
            inference_config=inference_config
        )
        
        print("✅ Retriever setup complete")
    
    def test_query(self, query: str, expected_keywords: List[str] = None) -> Dict[str, Any]:
        """Test a single query and analyze results."""
        print(f"\n{'='*80}")
        print(f"📝 QUERY: {query}")
        print(f"{'='*80}")
        
        start_time = time.time()
        
        try:
            # Get retrieval results (returns tuple of passages and passage_ids)
            passages, passage_ids = self.retriever.retrieve(query)
            retrieval_time = time.time() - start_time
            
            # Analyze results
            result_analysis = {
                'query': query,
                'num_results': len(passages),
                'retrieval_time': retrieval_time,
                'results': [],
                'keyword_matches': {},
                'max_score': 0.0
            }
            
            print(f"\n⏱️  Retrieval completed in {retrieval_time:.2f}s")
            print(f"📊 Found {len(passages)} results")
            
            # Process each result
            for i, (content, passage_id) in enumerate(zip(passages[:10], passage_ids[:10]), 1):  # Analyze top 10
                # Since we don't have explicit scores from HippoRAG2, use position as proxy
                score = 1.0 / i  # Higher score for higher rank
                metadata = {'passage_id': passage_id}
                
                if score > result_analysis['max_score']:
                    result_analysis['max_score'] = score
                
                # Check for expected keywords
                keyword_hits = []
                if expected_keywords:
                    content_lower = content.lower()
                    for keyword in expected_keywords:
                        if keyword.lower() in content_lower:
                            keyword_hits.append(keyword)
                            if keyword not in result_analysis['keyword_matches']:
                                result_analysis['keyword_matches'][keyword] = 0
                            result_analysis['keyword_matches'][keyword] += 1
                
                result_info = {
                    'rank': i,
                    'score': score,
                    'content_preview': content[:500] if content else "No content",
                    'keyword_hits': keyword_hits,
                    'metadata': metadata
                }
                
                result_analysis['results'].append(result_info)
                
                # Display result
                print(f"\n{'─'*40}")
                print(f"🔹 Result #{i} (Score: {score:.4f})")
                if keyword_hits:
                    print(f"   ✅ Keyword matches: {', '.join(keyword_hits)}")
                print(f"   📄 Content preview:")
                print(f"   {content[:300]}...")
            
            # Summary
            print(f"\n{'='*80}")
            print(f"📈 SUMMARY:")
            print(f"   • Max score: {result_analysis['max_score']:.4f}")
            print(f"   • Retrieval time: {retrieval_time:.2f}s")
            
            if expected_keywords:
                print(f"   • Keyword coverage:")
                for keyword in expected_keywords:
                    count = result_analysis['keyword_matches'].get(keyword, 0)
                    status = "✅" if count > 0 else "❌"
                    print(f"     {status} '{keyword}': {count} matches")
            
            return result_analysis
            
        except Exception as e:
            print(f"❌ Error during retrieval: {e}")
            import traceback
            traceback.print_exc()
            return {
                'query': query,
                'error': str(e),
                'retrieval_time': time.time() - start_time
            }
    
    def run_test_suite(self):
        """Run comprehensive test suite with financial queries."""
        print("\n" + "="*80)
        print("🚀 STARTING COMPREHENSIVE RETRIEVAL TEST SUITE")
        print("="*80)
        
        # Test queries with expected keywords
        test_cases = [
            {
                'query': "what is a risk reversal option strategy and how to create one in Bridge?",
                'keywords': ['risk', 'reversal', 'option', 'strategy', 'buy', 'sell', 'put', 'call']
            },
            {
                'query': "how to create scenarios in MMC market maker cockpit?",
                'keywords': ['scenario', 'MMC', 'market maker', 'cockpit', 'create', 'configuration']
            },
            {
                'query': "explain the FIX protocol message types for order execution",
                'keywords': ['FIX', 'protocol', 'message', 'order', 'execution', 'type']
            },
            {
                'query': "what are the trading limits and how to configure them?",
                'keywords': ['trading', 'limit', 'configure', 'threshold', 'restriction']
            },
            {
                'query': "describe the Bridge administration tools and user management",
                'keywords': ['Bridge', 'administration', 'user', 'management', 'tool', 'permission']
            }
        ]
        
        # Run tests
        for test_case in test_cases:
            result = self.test_query(
                query=test_case['query'],
                expected_keywords=test_case['keywords']
            )
            self.results.append(result)
            time.sleep(1)  # Brief pause between queries
        
        # Generate final report
        self.generate_report()
    
    def generate_report(self):
        """Generate comprehensive test report."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = f"retrieval_test_report_{timestamp}.json"
        
        # Calculate statistics
        total_queries = len(self.results)
        successful_queries = sum(1 for r in self.results if 'error' not in r)
        avg_retrieval_time = sum(r['retrieval_time'] for r in self.results) / total_queries
        avg_max_score = sum(r.get('max_score', 0) for r in self.results if 'error' not in r) / max(successful_queries, 1)
        
        report = {
            'timestamp': timestamp,
            'summary': {
                'total_queries': total_queries,
                'successful_queries': successful_queries,
                'avg_retrieval_time': avg_retrieval_time,
                'avg_max_score': avg_max_score,
                'embedding_model': 'sentence-transformers/all-mpnet-base-v2',
                'embedding_dim': 768,
                'llm_model': 'qwen3:30b-a3b-instruct-2507-q4_K_M'
            },
            'test_results': self.results
        }
        
        # Save report
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        print("\n" + "="*80)
        print("📊 FINAL REPORT")
        print("="*80)
        print(f"✅ Total queries tested: {total_queries}")
        print(f"✅ Successful queries: {successful_queries}")
        print(f"⏱️  Average retrieval time: {avg_retrieval_time:.2f}s")
        print(f"📈 Average max score: {avg_max_score:.4f}")
        print(f"💾 Report saved to: {report_path}")
        
        # Display improvement metrics
        print("\n🎯 IMPROVEMENT METRICS (vs all-MiniLM-L6-v2):")
        print(f"   • Embedding dimensions: 384 → 768 (2x increase)")
        print(f"   • Expected similarity improvement: ~20-30% for financial domain")
        print(f"   • Model: all-MiniLM-L6-v2 → all-mpnet-base-v2")
        
        return report_path

def main():
    """Main test execution."""
    print("🔬 HippoRAG2 Retrieval Quality Test")
    print("📦 Testing with upgraded all-mpnet-base-v2 embeddings (768-dim)")
    
    tester = RetrievalTester()
    tester.run_test_suite()

if __name__ == "__main__":
    main()