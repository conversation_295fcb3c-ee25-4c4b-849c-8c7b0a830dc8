#!/usr/bin/env python3
"""
Safe Interactive HippoRAG2 Q&A that avoids segmentation faults.
This version uses pre-computed embeddings and safe loading mechanisms.
"""

import os
import sys
import gc
import time
import pickle
import faiss
from pathlib import Path
from typing import Dict, List, Tuple, Any
from sentence_transformers import SentenceTransformer
from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
from atlas_rag.retriever import HippoRAG2Retriever
from atlas_rag.llm_generator import LLMGenerator
from openai import OpenAI
import numpy as np

# Import the safe loader
from load_pdf_dataset_safe import load_pdf_dataset_safe

# Import the model manager for dynamic model selection
from ollama_model_manager import OllamaModelManager

class SafeInteractiveHippoRAG2:
    """Safe Interactive HippoRAG2 that avoids segmentation faults."""
    
    def __init__(self):
        """Initialize with safe defaults."""
        
        # Parameters
        self.params = {
            'dataset_dir': 'import/pdf_dataset',
            'topN': 5,
            'use_ollama': True,
            'ollama_model': 'qwen3:30b-a3b-thinking-2507-q4_K_M',
            'temperature': 0.7,
            'max_tokens': 512,
            'detail_level': 'normal',  # minimal, normal, full
            'show_scores': True
        }
        
        # Components
        self.data = None
        self.sentence_encoder = None
        self.retriever = None
        self.llm_generator = None
        
        # Model manager for dynamic selection
        self.model_manager = OllamaModelManager()
    
    def offer_model_selection(self) -> bool:
        """Offer model selection at startup.
        
        Returns:
            True if user made a selection (or skipped), False if there was an error
        """
        try:
            if not self.model_manager.is_ollama_available():
                print("⚠️  Ollama is not available - using default model")
                return True
            
            print("\n🔧 Optional: Select Ollama Model")
            print("=" * 70)
            
            current_model = self.params.get('ollama_model')
            available_models = self.model_manager.get_model_names()
            
            if not available_models:
                print("❌ No models available")
                return True
            
            print(f"Current model: {current_model}")
            
            # Quick validation of current model
            if current_model not in available_models:
                print("⚠️  Current model not found in available models!")
                print("   You may want to select a different model.")
            
            choice = input("\nWould you like to select a different model? (y/N): ").strip().lower()
            
            if choice in ['y', 'yes']:
                selected_model = self.model_manager.interactive_model_selection(current_model)
                if selected_model and selected_model != current_model:
                    self.params['ollama_model'] = selected_model
                    print(f"✅ Updated model to: {selected_model}")
                    return True
                elif selected_model == current_model:
                    print("✅ Keeping current model")
                    return True
                else:
                    print("❌ Model selection cancelled - keeping current model")
                    return True
            else:
                print("✅ Using current model")
                return True
                
        except Exception as e:
            print(f"⚠️  Error during model selection: {e}")
            print("   Continuing with current model")
            return True
        
    def load_data_safely(self):
        """Load data using safe mechanism."""
        
        print("\n📊 Loading Data Safely")
        print("=" * 70)
        print("Using pre-computed embeddings to avoid segmentation faults")
        
        # Use the safe loader
        self.data = load_pdf_dataset_safe()
        
        if self.data is None:
            print("\n❌ Failed to load data")
            print("   Please run: python generate_pdf_embeddings_safe.py")
            print("   This will pre-compute embeddings safely")
            return False
        
        # Initialize encoder
        encoder_model_name = "sentence-transformers/all-mpnet-base-v2"
        self.sentence_encoder = SentenceEmbedding(
            SentenceTransformer(encoder_model_name)
        )
        
        # Verify we have the key components
        required = ['KG', 'text_dict']
        missing = [k for k in required if k not in self.data]
        
        if missing:
            print(f"❌ Missing required data: {missing}")
            return False
        
        print(f"\n✅ Data loaded successfully")
        print(f"  - Graph nodes: {len(self.data['KG'].nodes)}")
        print(f"  - Text passages: {len(self.data['text_dict'])}")
        
        if 'node_list' in self.data:
            print(f"  - Node list: {len(self.data['node_list'])}")
        if 'edge_list' in self.data:
            print(f"  - Edge list: {len(self.data['edge_list'])}")
        
        return True
    
    def setup_llm(self):
        """Setup LLM with error handling."""
        
        print("\n🤖 Setting up LLM")
        print("=" * 70)
        
        if self.params['use_ollama']:
            print(f"Using Ollama: {self.params['ollama_model']}")
            
            client = OpenAI(
                api_key="EMPTY",
                base_url="http://localhost:11434/v1"
            )
            
            self.llm_generator = LLMGenerator(
                client=client,
                model_name=self.params['ollama_model']
            )
            
            # Test connection
            try:
                print("Testing Ollama connection...")
                test_response = client.chat.completions.create(
                    model=self.params['ollama_model'],
                    messages=[{"role": "user", "content": "test"}],
                    max_tokens=10,
                    temperature=0.1
                )
                print("✅ Ollama connected")
                return True
            except Exception as e:
                print(f"⚠️  Ollama not available: {e}")
                print(f"   Run: ollama run {self.params['ollama_model']}")
                self.params['use_ollama'] = False
        
        if not self.params['use_ollama']:
            print("Using mock LLM")
            client = OpenAI(api_key="EMPTY", base_url="http://localhost:8000/v1")
            self.llm_generator = LLMGenerator(client=client, model_name="mock")
            return True
    
    def initialize_retriever(self):
        """Initialize retriever with available data."""
        
        print("\n🔍 Initializing Retriever")
        print("=" * 70)
        
        try:
            self.retriever = HippoRAG2Retriever(
                llm_generator=self.llm_generator,
                sentence_encoder=self.sentence_encoder,
                data=self.data
            )
            print("✅ Retriever initialized")
            return True
        except Exception as e:
            print(f"❌ Failed to initialize retriever: {e}")
            return False
    
    def ask_question(self, question: str = None):
        """Ask a question with safe retrieval."""
        
        if question is None:
            question = input("\n❓ Enter your question: ").strip()
            if not question:
                return
        
        print("\n" + "=" * 70)
        print(f"🔍 Processing: {question}")
        print("=" * 70)
        
        try:
            # Retrieve passages
            start_time = time.time()
            passages, node_ids, scores = self.retriever.retrieve(
                question, 
                topN=self.params['topN']
            )
            retrieval_time = time.time() - start_time
            
            print(f"\n📊 Retrieved {len(passages)} passages in {retrieval_time:.2f}s")
            
            if self.params['show_scores']:
                print(f"   Scores: {[f'{s:.4f}' for s in scores]}")
            
            # Display passages based on detail level
            if self.params['detail_level'] != 'minimal':
                print("\n📄 Retrieved Passages:")
                
                num_to_show = 3 if self.params['detail_level'] == 'normal' else len(passages)
                
                for i, (passage, score) in enumerate(zip(passages[:num_to_show], scores[:num_to_show]), 1):
                    print(f"\n--- Passage {i} (Score: {score:.4f}) ---")
                    
                    if self.params['detail_level'] == 'full':
                        print(passage)
                    else:
                        preview = passage[:400] + "..." if len(passage) > 400 else passage
                        print(preview)
            
            # Generate answer
            if passages:
                context = "\n\n---\n\n".join(passages)
                
                print(f"\n💬 Generating answer...")
                
                answer = self.llm_generator.generate_with_context(
                    question=question,
                    context=context,
                    max_new_tokens=self.params['max_tokens'],
                    temperature=self.params['temperature']
                )
                
                print(f"\n💡 Answer:")
                print("=" * 70)
                print(answer)
                print("=" * 70)
            else:
                print("❌ No relevant passages found")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            print("   This might be due to missing embeddings.")
            print("   Run: python generate_pdf_embeddings_safe.py")
    
    def print_params(self):
        """Display current parameters."""
        print("\n📊 Current Parameters:")
        print("-" * 50)
        for key, value in self.params.items():
            print(f"  {key:20s}: {value}")
    
    def modify_param(self):
        """Modify a parameter."""
        
        print("\n⚙️  Modify Parameters")
        print("-" * 50)
        print("Available parameters:")
        for i, (key, value) in enumerate(self.params.items(), 1):
            print(f"  {i}. {key:20s} = {value}")
        
        try:
            choice = int(input("\nSelect parameter (1-{}): ".format(len(self.params))))
            
            if 1 <= choice <= len(self.params):
                param_name = list(self.params.keys())[choice - 1]
                current = self.params[param_name]
                
                # Special handling for ollama_model
                if param_name == 'ollama_model':
                    print("🔍 Discovering available Ollama models...")
                    new_value = self.model_manager.interactive_model_selection(current)
                    if new_value is None:
                        print("❌ Model selection cancelled")
                        return
                    self.params[param_name] = new_value
                    print(f"✅ Updated {param_name} to {new_value}")
                    return
                elif param_name == 'detail_level':
                    print("Options: minimal, normal, full")
                elif isinstance(current, bool):
                    print("Options: true, false")
                
                new_value = input(f"New value for '{param_name}': ").strip()
                
                # Type conversion
                if isinstance(current, bool):
                    self.params[param_name] = new_value.lower() in ['true', '1', 'yes']
                elif isinstance(current, int):
                    self.params[param_name] = int(new_value)
                elif isinstance(current, float):
                    self.params[param_name] = float(new_value)
                else:
                    self.params[param_name] = new_value
                
                print(f"✅ Updated {param_name}")
                
        except (ValueError, IndexError) as e:
            print(f"❌ Invalid input")
    
    def search_content(self):
        """Search for content in passages."""
        
        keyword = input("\nEnter search term: ").strip()
        if not keyword:
            return
        
        print(f"\n🔍 Searching for '{keyword}'...")
        
        count = 0
        samples = []
        
        for node_id, text in self.data['text_dict'].items():
            if keyword.lower() in text.lower():
                count += 1
                if len(samples) < 3:
                    idx = text.lower().index(keyword.lower())
                    start = max(0, idx - 100)
                    end = min(len(text), idx + len(keyword) + 100)
                    samples.append(text[start:end])
        
        print(f"Found {count} passages containing '{keyword}'")
        
        if samples:
            print("\nSamples:")
            for i, sample in enumerate(samples, 1):
                print(f"\n{i}. ...{sample}...")
    
    def run_tests(self):
        """Run test queries."""
        
        print("\n🧪 Running Test Queries")
        print("=" * 70)
        
        test_queries = [
            "What is an OCO order?",
            "How does risk reversal work?",
            "What is Market Maker Cockpit?",
            "Explain Bank Baskets configuration"
        ]
        
        for query in test_queries:
            self.ask_question(query)
            print("\n" + "-" * 70)
    
    def interactive_menu(self):
        """Main menu."""
        
        while True:
            print("\n" + "=" * 70)
            print("🛡️ Safe HippoRAG2 Interactive Console")
            print("=" * 70)
            print("1. Ask a question")
            print("2. Modify parameters")
            print("3. Show parameters")
            print("4. Run test queries")
            print("5. Search content")
            print("0. Exit")
            
            try:
                choice = input("\nSelect option: ").strip()
                
                if choice == '0':
                    print("👋 Goodbye!")
                    break
                elif choice == '1':
                    self.ask_question()
                elif choice == '2':
                    self.modify_param()
                elif choice == '3':
                    self.print_params()
                elif choice == '4':
                    self.run_tests()
                elif choice == '5':
                    self.search_content()
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")

def main():
    """Main function."""
    
    print("🛡️ Safe HippoRAG2 Interactive Console")
    print("=" * 70)
    print("This version avoids segmentation faults by using:")
    print("  - Pre-computed embeddings")
    print("  - Safe loading mechanisms")
    print("  - Error handling throughout")
    print()
    
    # Initialize
    console = SafeInteractiveHippoRAG2()
    
    # Optional startup model selection
    if console.offer_model_selection():
        print("✅ Model selection completed")
    
    # Load data safely
    print("Step 1: Loading data safely...")
    if not console.load_data_safely():
        print("\n❌ Failed to load data")
        print("   Please run: python generate_pdf_embeddings_safe.py")
        print("   to pre-compute embeddings first")
        return
    
    # Setup LLM
    print("\nStep 2: Setting up LLM...")
    if not console.setup_llm():
        print("❌ Failed to setup LLM")
        return
    
    # Initialize retriever
    print("\nStep 3: Initializing retriever...")
    if not console.initialize_retriever():
        print("❌ Failed to initialize retriever")
        return
    
    # Garbage collection before starting
    gc.collect()
    
    # Run menu
    console.interactive_menu()

if __name__ == "__main__":
    main()