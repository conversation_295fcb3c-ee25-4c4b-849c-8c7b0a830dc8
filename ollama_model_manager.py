#!/usr/bin/env python3
"""
Ollama Model Manager - Dynamic model discovery and selection utilities.

This module provides functions to:
- Discover available Ollama models
- Validate model existence
- Format model information for user selection
- Handle Ollama connection errors gracefully
"""

import time
from typing import List, Dict, Optional, Tuple
from openai import OpenAI
from pathlib import Path
import json
import ollama  # Direct import since we know ollama is installed

class OllamaModelManager:
    """Manages discovery and selection of available Ollama models."""
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        """Initialize the model manager.
        
        Args:
            base_url: Ollama server URL (default: http://localhost:11434)
        """
        self.base_url = base_url
        self.client = None
        self._models_cache = None
        self._cache_timestamp = 0
        self._cache_duration = 300  # Cache for 5 minutes
        
    def _get_client(self) -> Optional[ollama.Client]:
        """Get or create Ollama client with error handling."""
        if self.client is None:
            try:
                self.client = ollama.Client(host=self.base_url)
                # Test connection
                self.client.list()
                return self.client
            except Exception as e:
                print(f"⚠️  Cannot connect to Ollama at {self.base_url}")
                print(f"   Error: {e}")
                return None
        return self.client
    
    def is_ollama_available(self) -> bool:
        """Check if Ollama server is available."""
        client = self._get_client()
        return client is not None
    
    def get_available_models(self, refresh_cache: bool = False) -> List[Dict]:
        """Get list of available Ollama models.
        
        Args:
            refresh_cache: Force refresh of cached model list
            
        Returns:
            List of model dictionaries with keys: name, size, modified_at, etc.
            Returns empty list if Ollama is unavailable.
        """
        current_time = time.time()
        
        # Use cache if valid and not refreshing
        if (not refresh_cache and 
            self._models_cache is not None and 
            current_time - self._cache_timestamp < self._cache_duration):
            return self._models_cache
        
        client = self._get_client()
        if client is None:
            return []
        
        try:
            response = client.list()
            # Convert Model objects to dictionaries for consistent interface
            models = []
            for model in response.models:
                model_dict = {
                    'name': model.model,
                    'size': model.size,
                    'modified_at': model.modified_at,
                    'digest': model.digest,
                    'details': model.details.__dict__ if hasattr(model.details, '__dict__') else {}
                }
                models.append(model_dict)
            
            # Update cache
            self._models_cache = models
            self._cache_timestamp = current_time
            
            return models
        except Exception as e:
            print(f"❌ Error fetching models: {e}")
            return []
    
    def get_model_names(self) -> List[str]:
        """Get list of model names only."""
        models = self.get_available_models()
        return [model.get('name', '') for model in models if model.get('name')]
    
    def validate_model(self, model_name: str) -> bool:
        """Check if a model exists and is available.
        
        Args:
            model_name: Name of the model to validate
            
        Returns:
            True if model exists, False otherwise
        """
        available_names = self.get_model_names()
        return model_name in available_names
    
    def get_model_info(self, model_name: str) -> Optional[Dict]:
        """Get detailed information about a specific model.
        
        Args:
            model_name: Name of the model
            
        Returns:
            Model information dictionary or None if not found
        """
        models = self.get_available_models()
        for model in models:
            if model.get('name') == model_name:
                return model
        return None
    
    def format_model_size(self, size_bytes: int) -> str:
        """Format model size in human-readable format."""
        if size_bytes < 1024**3:  # Less than 1 GB
            return f"{size_bytes / (1024**2):.1f} MB"
        else:  # 1 GB or more
            return f"{size_bytes / (1024**3):.1f} GB"
    
    def display_available_models(self, current_model: str = None) -> List[Tuple[int, str, Dict]]:
        """Display available models in a formatted list.
        
        Args:
            current_model: Name of currently selected model (will be marked)
            
        Returns:
            List of tuples: (index, model_name, model_info)
        """
        models = self.get_available_models()
        
        if not models:
            print("❌ No models available (Ollama may be offline)")
            return []
        
        print("\n🤖 Available Ollama Models:")
        print("=" * 70)
        
        model_list = []
        for i, model in enumerate(models, 1):
            name = model.get('name', 'Unknown')
            size = model.get('size', 0)
            size_str = self.format_model_size(size)
            
            # Check if this is the current model
            current_marker = " ← Current" if name == current_model else ""
            
            # Extract model family/type for display
            model_type = "Unknown"
            details = model.get('details', {})
            if 'families' in details:
                families = details['families']
                if families:
                    model_type = families[0].capitalize()
            
            print(f"{i:2d}. {name:<45} ({size_str:>8}) {model_type}{current_marker}")
            model_list.append((i, name, model))
        
        print("=" * 70)
        return model_list
    
    def interactive_model_selection(self, current_model: str = None) -> Optional[str]:
        """Interactive model selection interface.
        
        Args:
            current_model: Currently selected model name
            
        Returns:
            Selected model name or None if cancelled/error
        """
        if not self.is_ollama_available():
            print("❌ Ollama server is not available. Cannot select models.")
            print("   Make sure Ollama is running: ollama serve")
            return None
        
        model_list = self.display_available_models(current_model)
        
        if not model_list:
            return None
        
        while True:
            try:
                choice = input(f"\nSelect model (1-{len(model_list)}) or Enter to keep current: ").strip()
                
                if not choice:  # User pressed Enter
                    return current_model
                
                try:
                    choice_num = int(choice)
                    if 1 <= choice_num <= len(model_list):
                        selected_model = model_list[choice_num - 1][1]  # Get model name
                        print(f"✅ Selected: {selected_model}")
                        return selected_model
                    else:
                        print(f"❌ Please enter a number between 1 and {len(model_list)}")
                except ValueError:
                    print("❌ Please enter a valid number")
                    
            except KeyboardInterrupt:
                print("\n❌ Selection cancelled")
                return None
    
    def get_model_suggestions(self, use_case: str = "chat") -> List[str]:
        """Get model suggestions based on use case.
        
        Args:
            use_case: Type of use case ("chat", "instruct", "code", "embedding")
            
        Returns:
            List of recommended model names
        """
        models = self.get_available_models()
        suggestions = []
        
        use_case_lower = use_case.lower()
        
        for model in models:
            name = model.get('name', '').lower()
            
            # Simple heuristics for model recommendation
            if use_case_lower == "chat" and any(keyword in name for keyword in ['chat', 'instruct', 'thinking']):
                suggestions.append(model.get('name'))
            elif use_case_lower == "instruct" and 'instruct' in name:
                suggestions.append(model.get('name'))
            elif use_case_lower == "code" and any(keyword in name for keyword in ['code', 'coder', 'starcoder']):
                suggestions.append(model.get('name'))
            elif use_case_lower == "embedding" and any(keyword in name for keyword in ['embed', 'bge', 'e5']):
                suggestions.append(model.get('name'))
        
        return suggestions[:3]  # Return top 3 suggestions
    
    def save_model_preferences(self, preferences: Dict, config_file: str = "model_preferences.json"):
        """Save model preferences to file.
        
        Args:
            preferences: Dictionary of model preferences
            config_file: Path to config file
        """
        try:
            with open(config_file, 'w') as f:
                json.dump(preferences, f, indent=2)
            print(f"💾 Model preferences saved to {config_file}")
        except Exception as e:
            print(f"⚠️  Could not save preferences: {e}")
    
    def load_model_preferences(self, config_file: str = "model_preferences.json") -> Dict:
        """Load model preferences from file.
        
        Args:
            config_file: Path to config file
            
        Returns:
            Dictionary of model preferences or empty dict if file doesn't exist
        """
        try:
            if Path(config_file).exists():
                with open(config_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            print(f"⚠️  Could not load preferences: {e}")
        return {}

# Convenience functions for quick usage
def get_available_models() -> List[str]:
    """Quick function to get list of available model names."""
    manager = OllamaModelManager()
    return manager.get_model_names()

def validate_model(model_name: str) -> bool:
    """Quick function to validate if a model exists."""
    manager = OllamaModelManager()
    return manager.validate_model(model_name)

def interactive_select_model(current_model: str = None) -> Optional[str]:
    """Quick function for interactive model selection."""
    manager = OllamaModelManager()
    return manager.interactive_model_selection(current_model)

def is_ollama_available() -> bool:
    """Quick function to check if Ollama is available."""
    manager = OllamaModelManager()
    return manager.is_ollama_available()

# Test function
def main():
    """Test the model manager functionality."""
    print("🧪 Testing Ollama Model Manager")
    print("=" * 70)
    
    manager = OllamaModelManager()
    
    # Test connection
    if manager.is_ollama_available():
        print("✅ Ollama is available")
        
        # Show available models
        models = manager.get_available_models()
        print(f"📋 Found {len(models)} models")
        
        # Interactive selection test
        if models:
            print("\n🔧 Testing interactive selection:")
            selected = manager.interactive_model_selection()
            if selected:
                print(f"Selected model: {selected}")
    else:
        print("❌ Ollama is not available")

if __name__ == "__main__":
    main()