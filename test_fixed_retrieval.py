#!/usr/bin/env python3
"""
Test Fixed Retrieval Pipeline

Tests the MMC query with the fixed LLM filtering fallback mechanism
to verify if the PageRank propagation issue is resolved.
"""

from hipporag2_pipeline import HippoRAG2Pipeline

def test_fixed_mmc_query():
    """Test MMC query with the fixed HippoRAG2 pipeline."""
    print("🧪 Testing MMC Query with Fixed LLM Filtering")
    print("="*60)
    
    # Initialize pipeline
    print("🚀 Initializing HippoRAG2 Pipeline...")
    pipeline = HippoRAG2Pipeline()
    data = pipeline.load_existing_data()
    pipeline.setup_models()
    pipeline.data = data
    pipeline.initialize_hipporag2()
    
    print(f"\n🔍 Testing query: 'how to create scenarios in MMC?'")
    
    # Test the retrieval
    try:
        results = pipeline.hipporag2_retriever.retrieve("how to create scenarios in MMC?", topN=5)
        
        print(f"\n📊 RETRIEVAL RESULTS:")
        print(f"   Results type: {type(results)}")
        
        if isinstance(results, tuple) and len(results) == 2:
            contents, passage_ids = results
            print(f"   Retrieved passages: {len(contents)}")
            
            print(f"\n📋 TOP RESULTS:")
            for i, (content, passage_id) in enumerate(zip(contents, passage_ids)):
                print(f"\n   Result {i+1}:")
                print(f"   Passage ID: {passage_id}")
                
                content_preview = content[:200] + "..." if len(content) > 200 else content
                print(f"   Content: {content_preview}")
                
                # Check for MMC relevance
                content_lower = content.lower()
                if any(term in content_lower for term in ['mmc', 'market maker cockpit', 'cockpit']):
                    print(f"   ✅ MMC-relevant content detected!")
                elif any(term in content_lower for term in ['fix protocol', 'fix message']):
                    print(f"   ⚠️  FIX protocol content")
                else:
                    print(f"   ❓ Other content type")
        else:
            print(f"   Unexpected result format: {results}")
        
        # Test specific entity search to verify connectivity
        print(f"\n🔍 Testing entity connectivity...")
        
        # Find MMC entities in the graph
        mmc_entities = []
        for node_id in list(data["KG"].nodes())[:1000]:  # Check first 1000 nodes
            node_data = data["KG"].nodes[node_id]
            node_name = str(node_data.get('name', '')).lower()
            if 'mmc' in node_name and 'market maker cockpit' in node_name:
                mmc_entities.append((node_id, node_data.get('name', node_id)))
        
        print(f"   Found {len(mmc_entities)} MMC entities in sample")
        
        if mmc_entities:
            # Check connectivity for first MMC entity
            entity_id, entity_name = mmc_entities[0]
            print(f"   Testing connectivity for: '{entity_name}'")
            
            # Find connected text nodes
            connected_texts = []
            for neighbor in data["KG"].neighbors(entity_id):
                neighbor_data = data["KG"].nodes[neighbor]
                if neighbor_data.get('type') in ['passage', 'text']:
                    text_content = data["text_dict"].get(neighbor, "No text")
                    connected_texts.append((neighbor, text_content[:100]))
            
            print(f"   Connected to {len(connected_texts)} text passages")
            for i, (text_id, text_preview) in enumerate(connected_texts[:2]):
                print(f"      {i+1}. {text_preview}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Retrieval test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🧪 TESTING FIXED HippoRAG2 RETRIEVAL")
    print("="*80)
    
    try:
        success = test_fixed_mmc_query()
        
        print(f"\n{'='*80}")
        print(f"🏁 TEST RESULTS")
        print(f"{'='*80}")
        
        if success:
            print(f"✅ Retrieval test completed successfully")
            print(f"   Check results above for MMC content relevance")
        else:
            print(f"❌ Retrieval test failed")
        
        return success
        
    except Exception as e:
        print(f"🔥 Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)