#!/usr/bin/env python3
"""
Monitor Official Embedding Computation Progress

This script monitors the progress of the official create_embeddings_and_index()
workflow and runs the MMC query test once embeddings are complete.
"""

import os
import sys
import time
from pathlib import Path
from datetime import datetime

def check_embedding_progress():
    """Check the progress of official embedding computation."""
    precompute_dir = Path("import/pdf_dataset/precompute")
    
    if not precompute_dir.exists():
        return "not_started", "Precompute directory not found"
    
    # Check for completion indicators
    required_files = [
        "pdf_dataset_text_faiss.index", 
        "pdf_dataset_text_list.pkl",
        "pdf_dataset_original_text_dict_with_node_id.pkl"
    ]
    
    missing_files = []
    for req_file in required_files:
        file_path = precompute_dir / req_file
        if not file_path.exists():
            missing_files.append(req_file)
    
    if missing_files:
        return "computing", f"Missing: {', '.join(missing_files)}"
    
    # Check node and edge embedding files
    node_files = list(precompute_dir.glob("*node_embeddings.pkl"))
    edge_files = list(precompute_dir.glob("*edge_embeddings.pkl"))
    
    if len(node_files) > 0 and len(edge_files) > 0:
        return "complete", f"Found {len(node_files)} node files, {len(edge_files)} edge files"
    else:
        return "computing", f"Node files: {len(node_files)}, Edge files: {len(edge_files)}"

def monitor_embeddings(check_interval=30):
    """Monitor embedding progress with periodic checks."""
    print("🔍 Monitoring Official Embedding Computation")
    print("=" * 50)
    print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Check interval: {check_interval} seconds")
    print(f"Monitoring directory: import/pdf_dataset/precompute/")
    print()
    
    start_time = time.time()
    check_count = 0
    
    while True:
        check_count += 1
        elapsed = time.time() - start_time
        
        status, details = check_embedding_progress()
        
        print(f"[Check #{check_count:3d}] {datetime.now().strftime('%H:%M:%S')} | ", end="")
        print(f"Elapsed: {elapsed/60:.1f}m | ", end="")
        
        if status == "not_started":
            print("❌ Not Started - " + details)
        elif status == "computing":
            print("⏳ Computing - " + details)
        elif status == "complete":
            print("✅ Complete! - " + details)
            print(f"\n🎉 Official embeddings completed after {elapsed/60:.1f} minutes!")
            break
        
        time.sleep(check_interval)
    
    return True

def run_mmc_test_after_completion():
    """Run MMC test once embeddings are complete."""
    print(f"\n🧪 Running MMC Query Test with Official Data")
    print("-" * 40)
    
    try:
        # Import and run the quick test
        import subprocess
        result = subprocess.run([sys.executable, "quick_test_mmc_query.py"], 
                              capture_output=True, text=True, timeout=300)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ MMC test completed successfully!")
        else:
            print(f"⚠️  MMC test completed with return code: {result.returncode}")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ MMC test timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ Error running MMC test: {e}")
        return False

def main():
    """Main monitoring function."""
    print("🔍 Official Embedding Computation Monitor")
    print("=" * 60)
    
    # Check initial status
    status, details = check_embedding_progress()
    
    if status == "complete":
        print("✅ Embeddings already complete!")
        print(f"Details: {details}")
        print("\nRunning MMC test immediately...")
        success = run_mmc_test_after_completion()
        return success
    
    elif status == "not_started":
        print("❌ Official embedding computation hasn't started yet.")
        print("Please run the fixed HippoRAG2 pipeline first:")
        print("  python run_fixed_hipporag2.py")
        return False
    
    else:
        print(f"⏳ Embeddings currently computing: {details}")
        print("Starting monitoring...")
        print()
        
        # Start monitoring
        try:
            monitor_complete = monitor_embeddings(check_interval=30)
            
            if monitor_complete:
                print("\n" + "=" * 60)
                success = run_mmc_test_after_completion()
                return success
            else:
                return False
                
        except KeyboardInterrupt:
            print(f"\n\n👋 Monitoring interrupted by user")
            return False
        except Exception as e:
            print(f"\n❌ Monitoring error: {e}")
            return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)