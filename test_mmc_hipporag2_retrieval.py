#!/usr/bin/env python3
"""
Test HippoRAG2 retrieval for MMC scenario queries and implement targeted fixes.
This script will trace the retrieval process and implement entity-first retrieval improvements.
"""

import json
import logging
import time
import sys
from pathlib import Path
from hipporag2_pipeline import HippoRAG2Pipeline

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_current_retrieval():
    """Test current HippoRAG2 retrieval for MMC scenario queries."""
    print("🧪 TESTING CURRENT HIPPORAG2 RETRIEVAL")
    print("=" * 60)
    
    try:
        # Initialize pipeline
        pipeline = HippoRAG2Pipeline(data_directory="import/pdf_dataset")
        data = pipeline.load_existing_data()
        
        # Setup models and initialize HippoRAG2
        pipeline.setup_models()
        pipeline.data = data
        pipeline.initialize_hipporag2()
        
        retriever = pipeline.hipporag2_retriever
        
        # Test queries related to MMC scenarios
        test_queries = [
            "how to create scenarios in MMC?",
            "MMC scenario creation",
            "Market Maker Cockpit scenarios",
            "configure scenarios in Market Maker Cockpit",
            "pricing and risk management scenarios"
        ]
        
        results = {}
        
        for query in test_queries:
            print(f"\n🔍 Testing Query: '{query}'")
            print("-" * 40)
            
            start_time = time.time()
            
            try:
                # Run retrieval with detailed logging
                contents, passage_ids = retriever.retrieve(query, topN=10)
                retrieval_time = time.time() - start_time
                
                print(f"⏱️  Retrieval time: {retrieval_time:.2f}s")
                print(f"📄 Retrieved {len(contents)} passages")
                
                # Analyze results for MMC/scenario relevance
                mmc_relevant = 0
                scenario_relevant = 0
                mmc_scenario_relevant = 0
                
                for i, content in enumerate(contents):
                    content_lower = content.lower()
                    has_mmc = 'mmc' in content_lower or 'market maker cockpit' in content_lower
                    has_scenario = 'scenario' in content_lower
                    
                    if has_mmc and has_scenario:
                        mmc_scenario_relevant += 1
                        print(f"  ✅ {i+1}. MMC+Scenario relevant: {content[:100]}...")
                    elif has_mmc:
                        mmc_relevant += 1
                        print(f"  📈 {i+1}. MMC relevant: {content[:100]}...")
                    elif has_scenario:
                        scenario_relevant += 1
                        print(f"  🎯 {i+1}. Scenario relevant: {content[:100]}...")
                    else:
                        print(f"  ❌ {i+1}. Not relevant: {content[:100]}...")
                
                results[query] = {
                    'total_passages': len(contents),
                    'mmc_scenario_relevant': mmc_scenario_relevant,
                    'mmc_relevant': mmc_relevant,
                    'scenario_relevant': scenario_relevant,
                    'retrieval_time': retrieval_time,
                    'contents': contents,
                    'passage_ids': passage_ids
                }
                
                print(f"📊 Relevance: MMC+Scenario={mmc_scenario_relevant}, MMC={mmc_relevant}, Scenario={scenario_relevant}")
                
            except Exception as e:
                print(f"❌ Retrieval failed: {e}")
                results[query] = {'error': str(e)}
        
        return results
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        return {}

def analyze_retrieval_issues(results):
    """Analyze the issues with current retrieval results."""
    print(f"\n📊 ANALYZING RETRIEVAL ISSUES")
    print("=" * 60)
    
    total_queries = len(results)
    successful_queries = sum(1 for r in results.values() if 'error' not in r)
    
    print(f"Total queries tested: {total_queries}")
    print(f"Successful retrievals: {successful_queries}")
    
    if successful_queries == 0:
        print("❌ No successful retrievals to analyze")
        return
    
    # Analyze relevance across all queries
    total_mmc_scenario = 0
    total_mmc = 0
    total_scenario = 0
    total_passages = 0
    
    for query, result in results.items():
        if 'error' not in result:
            total_mmc_scenario += result['mmc_scenario_relevant']
            total_mmc += result['mmc_relevant']
            total_scenario += result['scenario_relevant']
            total_passages += result['total_passages']
    
    print(f"\n🎯 OVERALL RELEVANCE ANALYSIS:")
    print(f"Total passages retrieved: {total_passages}")
    print(f"MMC+Scenario relevant: {total_mmc_scenario} ({total_mmc_scenario/total_passages*100:.1f}%)")
    print(f"MMC only relevant: {total_mmc} ({total_mmc/total_passages*100:.1f}%)")
    print(f"Scenario only relevant: {total_scenario} ({total_scenario/total_passages*100:.1f}%)")
    print(f"Not relevant: {total_passages - total_mmc_scenario - total_mmc - total_scenario} ({(total_passages - total_mmc_scenario - total_mmc - total_scenario)/total_passages*100:.1f}%)")
    
    # Identify the core issues
    print(f"\n🔍 IDENTIFIED ISSUES:")
    if total_mmc_scenario < total_passages * 0.3:
        print("❌ Low MMC+Scenario relevance - entity matching or graph connectivity issues")
    
    if total_mmc + total_scenario < total_passages * 0.5:
        print("❌ Low overall relevance - fundamental retrieval algorithm issues")
    
    # Best performing query
    best_query = None
    best_score = 0
    for query, result in results.items():
        if 'error' not in result:
            score = result['mmc_scenario_relevant'] * 3 + result['mmc_relevant'] * 2 + result['scenario_relevant']
            if score > best_score:
                best_score = score
                best_query = query
    
    if best_query:
        print(f"🏆 Best performing query: '{best_query}' (score: {best_score})")

def implement_entity_first_retrieval_fix():
    """Implement an entity-first retrieval approach as a targeted fix."""
    print(f"\n🔧 IMPLEMENTING ENTITY-FIRST RETRIEVAL FIX")
    print("=" * 60)
    
    try:
        # Load data and setup pipeline
        pipeline = HippoRAG2Pipeline(data_directory="import/pdf_dataset")
        data = pipeline.load_existing_data()
        pipeline.setup_models()
        
        KG = data['KG']
        text_dict = data['text_dict']
        node_list = data['node_list']
        
        # Find key MMC and scenario entities
        mmc_entities = []
        scenario_entities = []
        
        for node_id in node_list:
            if node_id in KG.nodes:
                entity_name = KG.nodes[node_id].get('id', '').lower()
                
                if 'mmc' in entity_name or 'market maker cockpit' in entity_name:
                    mmc_entities.append(node_id)
                
                if 'scenario' in entity_name and ('pricing' in entity_name or 'risk' in entity_name):
                    scenario_entities.append(node_id)
        
        print(f"✅ Found {len(mmc_entities)} MMC entities and {len(scenario_entities)} scenario entities")
        
        def enhanced_mmc_retrieval(query, topN=10):
            """Enhanced retrieval that prioritizes MMC scenario content."""
            print(f"\n🔍 Enhanced MMC Retrieval for: '{query}'")
            
            # Step 1: Start from key entities
            starting_entities = set()
            
            # Add MMC entities for MMC-related queries
            query_lower = query.lower()
            if 'mmc' in query_lower or 'market maker cockpit' in query_lower:
                starting_entities.update(mmc_entities[:5])  # Top 5 MMC entities
                print(f"🎯 Added {len(mmc_entities[:5])} MMC starting entities")
            
            # Add scenario entities for scenario-related queries
            if 'scenario' in query_lower:
                starting_entities.update(scenario_entities[:3])  # Top 3 scenario entities
                print(f"🎯 Added {len(scenario_entities[:3])} scenario starting entities")
            
            # Step 2: Collect connected text passages
            connected_texts = set()
            entity_scores = {}
            
            for entity_id in starting_entities:
                if entity_id in KG.nodes:
                    neighbors = list(KG.neighbors(entity_id))
                    text_neighbors = [n for n in neighbors if n in text_dict]
                    
                    for text_id in text_neighbors:
                        connected_texts.add(text_id)
                        # Score based on entity importance
                        if text_id not in entity_scores:
                            entity_scores[text_id] = 0
                        entity_scores[text_id] += 1  # Simple count-based scoring
            
            print(f"🔗 Found {len(connected_texts)} directly connected text passages")
            
            # Step 3: Score passages based on content relevance
            passage_scores = {}
            
            for text_id in connected_texts:
                content = text_dict[text_id].lower()
                score = entity_scores.get(text_id, 0)
                
                # Boost score for content relevance
                if 'mmc' in content or 'market maker cockpit' in content:
                    score += 5
                if 'scenario' in content:
                    score += 3
                if 'create' in content or 'configure' in content:
                    score += 2
                if 'pricing' in content and 'risk' in content:
                    score += 2
                
                passage_scores[text_id] = score
            
            # Step 4: Select top passages
            sorted_passages = sorted(passage_scores.items(), key=lambda x: x[1], reverse=True)
            top_passages = sorted_passages[:topN]
            
            # Step 5: Return results
            contents = []
            passage_ids = []
            
            for text_id, score in top_passages:
                contents.append(text_dict[text_id])
                passage_ids.append(text_id)
                print(f"📄 Selected text {text_id} (score: {score}): {text_dict[text_id][:100]}...")
            
            return contents, passage_ids
        
        # Test the enhanced retrieval
        test_query = "how to create scenarios in MMC?"
        print(f"\n🧪 Testing Enhanced Retrieval:")
        
        enhanced_contents, enhanced_ids = enhanced_mmc_retrieval(test_query, topN=10)
        
        # Analyze enhanced results
        mmc_scenario_count = 0
        for content in enhanced_contents:
            content_lower = content.lower()
            if ('mmc' in content_lower or 'market maker cockpit' in content_lower) and 'scenario' in content_lower:
                mmc_scenario_count += 1
        
        print(f"\n✅ Enhanced retrieval results:")
        print(f"Total passages: {len(enhanced_contents)}")
        print(f"MMC+Scenario relevant: {mmc_scenario_count}")
        print(f"Relevance rate: {mmc_scenario_count/len(enhanced_contents)*100:.1f}%")
        
        return enhanced_contents, enhanced_ids
        
    except Exception as e:
        print(f"❌ Enhanced retrieval implementation failed: {e}")
        return [], []

def main():
    print("🚀 TESTING MMC HIPPORAG2 RETRIEVAL & IMPLEMENTING FIXES")
    print("=" * 70)
    
    # Step 1: Test current retrieval
    print("STEP 1: Testing current HippoRAG2 retrieval")
    current_results = test_current_retrieval()
    
    # Step 2: Analyze issues
    print("STEP 2: Analyzing retrieval issues")
    analyze_retrieval_issues(current_results)
    
    # Step 3: Implement targeted fix
    print("STEP 3: Implementing entity-first retrieval fix")
    enhanced_contents, enhanced_ids = implement_entity_first_retrieval_fix()
    
    # Step 4: Compare results
    print(f"\n📊 COMPARISON SUMMARY")
    print("=" * 30)
    
    if current_results:
        # Get results for main query
        main_query = "how to create scenarios in MMC?"
        if main_query in current_results and 'error' not in current_results[main_query]:
            current_mmc_scenario = current_results[main_query]['mmc_scenario_relevant']
            current_total = current_results[main_query]['total_passages']
            current_rate = current_mmc_scenario / current_total * 100 if current_total > 0 else 0
            
            enhanced_mmc_scenario = sum(1 for content in enhanced_contents 
                                     if ('mmc' in content.lower() or 'market maker cockpit' in content.lower()) 
                                     and 'scenario' in content.lower())
            enhanced_total = len(enhanced_contents)
            enhanced_rate = enhanced_mmc_scenario / enhanced_total * 100 if enhanced_total > 0 else 0
            
            print(f"Current HippoRAG2: {current_mmc_scenario}/{current_total} ({current_rate:.1f}%) MMC+Scenario relevant")
            print(f"Enhanced approach: {enhanced_mmc_scenario}/{enhanced_total} ({enhanced_rate:.1f}%) MMC+Scenario relevant")
            
            if enhanced_rate > current_rate:
                print(f"✅ Enhanced approach improves relevance by {enhanced_rate - current_rate:.1f}%")
            else:
                print(f"⚠️  Enhanced approach needs further refinement")
        else:
            print("❌ Cannot compare - current retrieval failed")
    
    print(f"\n🎯 RECOMMENDATIONS FOR FIXING HIPPORAG2:")
    print("1. Implement entity-first starting points for domain-specific queries")
    print("2. Boost PageRank personalization for directly connected text passages")
    print("3. Add content-based scoring to complement graph-based retrieval")
    print("4. Consider query expansion to include synonyms (MMC = Market Maker Cockpit)")

if __name__ == "__main__":
    main()