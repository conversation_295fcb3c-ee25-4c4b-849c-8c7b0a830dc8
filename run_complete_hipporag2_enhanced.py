#!/usr/bin/env python3
"""
Complete HippoRAG2 with Enhanced Answer Generation

This script provides the full HippoRAG2 pipeline with:
1. Query expansion and entity filtering
2. Retrieval with real PageRank scores
3. Enhanced LLM-based answer generation with multiple response modes
4. Debug visibility to see exactly what's sent to the LLM

You can now control answer verbosity:
- 'concise': Original HippoRAG (noun phrase only)
- 'balanced': 2-3 informative sentences
- 'detailed': Comprehensive explanations
- 'comprehensive': Most thorough with structured analysis
"""

import os
import sys
import pickle
from pathlib import Path
from datetime import datetime

# Setup environment
os.environ["TOKENIZERS_PARALLELISM"] = "false"
sys.path.insert(0, str(Path(__file__).parent))

# Import components
from enhanced_hipporag2_retriever_module import EnhancedHippoRAG2Retriever
from hipporag2_answer_generator_enhanced import EnhancedHippoRAG2AnswerGenerator, OllamaConfig
from ollama_model_manager import OllamaModelManager

# Configuration - Change this to use a different Ollama model
DEFAULT_OLLAMA_MODEL = "qwen3:30b-a3b-thinking-2507-q4_K_M"  # Edit this to use your preferred model
from atlas_rag.retriever.inference_config import InferenceConfig
from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
from sentence_transformers import SentenceTransformer
from openai import OpenAI
from atlas_rag.llm_generator import LLMGenerator


class CompleteEnhancedHippoRAG2System:
    """
    Complete HippoRAG2 system with enhanced answer generation.
    
    Features:
    1. Multiple response modes for different use cases
    2. Debug visibility into LLM prompts
    3. Enhanced context formatting
    4. Full configuration control
    """
    
    def __init__(self, data_directory="import/pdf_dataset", ollama_config=None, default_response_mode="balanced"):
        self.data_directory = data_directory
        self.ollama_config = ollama_config or OllamaConfig()
        self.default_response_mode = default_response_mode
        self.retriever = None
        self.answer_generator = None
        self.data = None
        
    def initialize(self):
        """Initialize all components."""
        print("🚀 INITIALIZING ENHANCED HIPPORAG2 SYSTEM")
        print("="*60)
        print("Features:")
        print("  ✅ Entity-based retrieval with real PageRank scores")
        print("  ✅ Multiple response modes (concise/balanced/detailed)")
        print("  ✅ Debug visibility for LLM prompts")
        print("  ✅ Enhanced context formatting")
        print()
        
        # Load data
        if not self._load_data():
            return False
            
        # Setup embedding model
        print("🔤 Setting up embedding model...")
        encoder_model_name = 'sentence-transformers/all-mpnet-base-v2'
        sentence_model = SentenceTransformer(encoder_model_name)
        sentence_encoder = SentenceEmbedding(sentence_model)
        print(f"   ✅ Using {encoder_model_name} (768-dim)")
        
        # Setup LLM for retriever (filtering)
        print("🤖 Setting up LLM for retriever...")
        llm_for_retriever = self._setup_retriever_llm()
        
        # Setup inference config
        inference_config = InferenceConfig()
        inference_config.topk_edges = 30
        inference_config.ppr_alpha = 0.15
        inference_config.ppr_max_iter = 200
        inference_config.weight_adjust = 0.1
        
        # Initialize enhanced retriever
        print("🔍 Initializing Enhanced HippoRAG2 Retriever...")
        try:
            self.retriever = EnhancedHippoRAG2Retriever(
                llm_generator=llm_for_retriever,
                sentence_encoder=sentence_encoder,
                data=self.data,
                inference_config=inference_config
            )
            print("   ✅ Retriever ready with query expansion")
        except Exception as e:
            print(f"   ❌ Failed to initialize retriever: {e}")
            return False
        
        # Initialize enhanced answer generator
        print("💬 Initializing Enhanced Answer Generator...")
        self.answer_generator = EnhancedHippoRAG2AnswerGenerator(self.ollama_config)
        if not self.answer_generator.llm_generator:
            print("   ⚠️ Answer generator LLM not available")
            print("   Retrieval will work but answer generation will be limited")
        else:
            print(f"   ✅ Enhanced answer generator ready")
            print(f"   📝 Default response mode: {self.default_response_mode}")
        
        print("\n✅ ENHANCED SYSTEM READY!")
        return True
    
    def _load_data(self):
        """Load the complete data structure."""
        print("📊 Loading data...")
        data_file = Path(self.data_directory) / "complete_data.pkl"
        
        if not data_file.exists():
            print(f"   ❌ Data file not found: {data_file}")
            return False
            
        try:
            with open(data_file, 'rb') as f:
                self.data = pickle.load(f)
            print(f"   ✅ Data loaded: {len(self.data['KG'].nodes)} nodes, {len(self.data['KG'].edges)} edges")
            return True
        except Exception as e:
            print(f"   ❌ Error loading data: {e}")
            return False
    
    def _setup_retriever_llm(self):
        """Setup LLM for retriever (used for filtering)."""
        try:
            client = OpenAI(
                base_url=self.ollama_config.base_url,
                api_key="dummy-key",
            )
            return LLMGenerator(
                client=client,
                model_name=self.ollama_config.model_name
            )
        except:
            print("   ⚠️ LLM for retriever not available (filtering disabled)")
            return None
    
    def query(
        self, 
        question, 
        top_k=5, 
        response_mode=None, 
        verbose=False,
        show_context=False,
        custom_prompt=None
    ):
        """
        Complete query pipeline with enhanced answer generation.
        
        Args:
            question: User's query
            top_k: Number of passages to retrieve
            response_mode: 'concise', 'balanced', 'detailed', 'comprehensive', or 'custom'
            verbose: Show debug information including full LLM prompt
            show_context: Show retrieved passages before generating answer
            custom_prompt: Custom system prompt (only for 'custom' mode)
            
        Returns:
            Dict with answer, passages, scores, and metadata
        """
        response_mode = response_mode or self.default_response_mode
        
        print(f"\n{'='*60}")
        print(f"🎯 QUERY: {question}")
        print(f"📝 Response Mode: {response_mode}")
        print(f"{'='*60}")
        
        # Step 1: Query expansion
        if hasattr(self.retriever, 'expand_query'):
            expanded = self.retriever.expand_query(question)
            if expanded != question:
                print(f"📝 Expanded query: {expanded[:100]}...")
        
        # Step 2: Retrieval
        print(f"\n🔍 Retrieving top {top_k} passages...")
        passages, passage_ids, scores = self.retriever.retrieve(question, topN=top_k)
        
        print(f"   ✅ Retrieved {len(passages)} passages")
        for i, (pid, score) in enumerate(zip(passage_ids[:3], scores[:3]), 1):
            if score < 0.001:
                print(f"      {i}. Score: {score:.2e} | ID: {pid}")
            else:
                print(f"      {i}. Score: {score:.6f} | ID: {pid}")
        
        # Show context if requested
        if show_context:
            print(f"\n📚 RETRIEVED CONTEXT:")
            print("-"*60)
            for i, (passage, score) in enumerate(zip(passages, scores), 1):
                print(f"\n📄 Passage {i} [Score: {score:.4f}]:")
                print(passage[:300] + "..." if len(passage) > 300 else passage)
            print("-"*60)
        
        # Step 3: Enhanced answer generation
        print(f"\n💬 Generating {response_mode} answer with {self.ollama_config.model_name}...")
        
        if self.answer_generator and self.answer_generator.llm_generator:
            answer, metadata = self.answer_generator.generate_answer_from_passages(
                question=question,
                passages=passages,
                passage_ids=passage_ids,
                scores=scores,
                response_mode=response_mode,
                custom_prompt=custom_prompt,
                verbose=verbose,
                use_enhanced_formatting=True
            )
            
            print(f"   ✅ Answer generated in {response_mode} mode")
        else:
            # Fallback
            answer = "LLM not available. Top retrieved passages:\n\n"
            for i, passage in enumerate(passages[:3], 1):
                answer += f"{i}. {passage[:200]}...\n\n"
            metadata = {"fallback": True}
        
        # Prepare result
        result = {
            "question": question,
            "answer": answer,
            "passages": passages,
            "passage_ids": passage_ids,
            "scores": scores,
            "metadata": metadata,
            "response_mode": response_mode,
            "timestamp": datetime.now().isoformat()
        }
        
        print(f"\n{'='*60}")
        print(f"✨ ANSWER ({response_mode}):")
        print(f"{'='*60}")
        
        # Display answer (truncate if very long in non-verbose mode)
        if not verbose and len(answer) > 1000:
            print(answer[:1000] + "\n...[truncated - use verbose=True to see full]")
        else:
            print(answer)
        
        print(f"{'='*60}\n")
        
        return result
    
    def interactive_session(self):
        """Run interactive Q&A session with enhanced features."""
        print("\n🎮 ENHANCED HIPPORAG2 INTERACTIVE SESSION")
        print("="*60)
        
        # Show available response modes
        if self.answer_generator:
            self.answer_generator.print_response_modes()
        
        print("\n📌 COMMANDS:")
        print("  • Type your question to get an answer")
        print("  • 'mode <name>' - Change response mode (concise/balanced/detailed/comprehensive)")
        print("  • 'verbose' - Toggle verbose mode (see LLM prompts)")
        print("  • 'context' - Toggle showing retrieved passages")
        print("  • 'test' - Run test queries")
        print("  • 'help' - Show this help")
        print("  • 'quit' - Exit")
        print("="*60)
        
        # Session state
        current_mode = self.default_response_mode
        verbose = False
        show_context = False
        
        print(f"\n📝 Current settings: Mode={current_mode}, Verbose={verbose}, Show Context={show_context}")
        
        while True:
            try:
                user_input = input(f"\n❓ [{current_mode}] Question: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                    
                elif user_input.lower() == 'help':
                    if self.answer_generator:
                        self.answer_generator.print_response_modes()
                    print("\nCommands: mode, verbose, context, test, help, quit")
                    continue
                    
                elif user_input.lower() == 'verbose':
                    verbose = not verbose
                    print(f"   Verbose mode: {'ON' if verbose else 'OFF'}")
                    continue
                    
                elif user_input.lower() == 'context':
                    show_context = not show_context
                    print(f"   Show context: {'ON' if show_context else 'OFF'}")
                    continue
                    
                elif user_input.lower().startswith('mode '):
                    new_mode = user_input[5:].strip()
                    valid_modes = ['concise', 'balanced', 'detailed', 'comprehensive', 'custom']
                    if new_mode in valid_modes:
                        current_mode = new_mode
                        print(f"   ✅ Response mode changed to: {current_mode}")
                    else:
                        print(f"   ❌ Invalid mode. Choose from: {', '.join(valid_modes)}")
                    continue
                    
                elif user_input.lower() == 'test':
                    self.run_test_queries(current_mode, verbose, show_context)
                    continue
                    
                elif not user_input:
                    continue
                
                # Process the query
                result = self.query(
                    user_input, 
                    top_k=5, 
                    response_mode=current_mode,
                    verbose=verbose,
                    show_context=show_context
                )
                
            except KeyboardInterrupt:
                print("\n👋 Session interrupted. Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                continue
    
    def run_test_queries(self, mode="balanced", verbose=False, show_context=False):
        """Run test queries with current settings."""
        print("\n🧪 RUNNING TEST QUERIES")
        print("="*60)
        
        test_queries = [
            "What is an OCO order and how to place it in Bridge?",
            "How to create a scenario in MMC Market Maker Cockpit?",
            "Explain Risk Reversal strategy for FX options"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📌 Test {i}/{len(test_queries)}")
            result = self.query(
                query, 
                top_k=3, 
                response_mode=mode,
                verbose=verbose,
                show_context=show_context
            )
        
        print("\n✅ Test queries completed!")


def main():
    """Main entry point."""
    print("🚀 COMPLETE HIPPORAG2 WITH ENHANCED ANSWER GENERATION")
    print("="*60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Configure Ollama
    ollama_config = OllamaConfig(
        model_name=DEFAULT_OLLAMA_MODEL,
        temperature=0.7,
        max_new_tokens=2048,
        top_p=0.9,
        reasoning_effort="medium"
    )
    
    print("📝 Configuration:")
    print(f"   Model: {ollama_config.model_name}")
    print(f"   Default mode: balanced (2-3 informative sentences)")
    print(f"   Temperature: {ollama_config.temperature}")
    print()
    
    # Initialize system with balanced as default (more useful than concise)
    system = CompleteEnhancedHippoRAG2System(
        ollama_config=ollama_config,
        default_response_mode="balanced"  # Changed from concise
    )
    
    if not system.initialize():
        print("❌ Failed to initialize system")
        return False
    
    # Run interactive session
    system.interactive_session()
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)