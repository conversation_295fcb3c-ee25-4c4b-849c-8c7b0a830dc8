#!/usr/bin/env python3
"""
HippoRAG2 Answer Generator with Ollama Integration

This module implements the complete answer generation pipeline following the
AutoSchemaKG pattern, using Ollama with configurable parameters.
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Any
import json
from dataclasses import dataclass
from openai import OpenAI
from ollama_model_manager import OllamaModelManager

# Configuration - Change this to use a different Ollama model
DEFAULT_OLLAMA_MODEL = "qwen3:30b-a3b-thinking-2507-q4_K_M"  # Edit this to use your preferred model

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from atlas_rag.llm_generator import LLMGenerator
from atlas_rag.llm_generator.prompt.rag_prompt import (
    cot_system_instruction,
    cot_system_instruction_kg,
    cot_system_instruction_no_doc
)


@dataclass
class OllamaConfig:
    """Configuration for Ollama LLM generation with all tunable parameters."""
    
    # Model selection
    model_name: str = DEFAULT_OLLAMA_MODEL  # Using thinking variant
    base_url: str = "http://localhost:11434/v1"
    
    # Generation parameters
    max_new_tokens: int = 2048          # Maximum tokens to generate
    temperature: float = 0.7             # Randomness (0.0 = deterministic, 2.0 = very random)
    top_p: float = 0.9                   # Nucleus sampling threshold
    top_k: int = 40                      # Top-k sampling
    frequency_penalty: float = 0.0       # Reduce repetition (-2.0 to 2.0)
    presence_penalty: float = 0.0        # Encourage new topics (-2.0 to 2.0)
    
    # Response format
    response_format: Dict = None         # {"type": "text"} or {"type": "json_object"}
    
    # Advanced parameters
    seed: Optional[int] = None           # For reproducible outputs
    stop_sequences: List[str] = None     # Stop generation at these sequences
    reasoning_effort: Optional[str] = None  # For thinking models: "low", "medium", "high"
    
    # Retry configuration
    max_retries: int = 3
    timeout: int = 120                   # Request timeout in seconds
    
    def to_generation_kwargs(self) -> Dict[str, Any]:
        """Convert config to kwargs for LLM generation."""
        kwargs = {
            "max_new_tokens": self.max_new_tokens,
            "temperature": self.temperature,
        }
        
        if self.frequency_penalty != 0.0:
            kwargs["frequency_penalty"] = self.frequency_penalty
        
        if self.presence_penalty != 0.0:
            kwargs["presence_penalty"] = self.presence_penalty
            
        if self.response_format:
            kwargs["response_format"] = self.response_format
            
        if self.reasoning_effort:
            kwargs["reasoning_effort"] = self.reasoning_effort
            
        return kwargs


class HippoRAG2AnswerGenerator:
    """
    Complete answer generation for HippoRAG2 following AutoSchemaKG pattern.
    
    This class handles:
    1. Context formatting (passages vs knowledge graph edges)
    2. Prompt selection based on retriever type
    3. LLM generation with Ollama
    4. Answer extraction and formatting
    """
    
    def __init__(self, config: OllamaConfig = None):
        """Initialize with Ollama configuration."""
        self.config = config or OllamaConfig()
        self.llm_generator = self._setup_llm()
        
    def _setup_llm(self) -> Optional[LLMGenerator]:
        """Setup Ollama LLM connection."""
        try:
            # Test Ollama connection
            import requests
            response = requests.get(
                f"{self.config.base_url.replace('/v1', '')}/api/tags",
                timeout=5
            )
            
            if response.status_code != 200:
                print(f"⚠️ Ollama not running at {self.config.base_url}")
                return None
                
            # Check if model is available
            available_models = response.json().get("models", [])
            model_names = [m.get("name", "") for m in available_models]
            
            if not any(self.config.model_name in name for name in model_names):
                print(f"⚠️ Model {self.config.model_name} not found in Ollama")
                print(f"   Available models: {', '.join(model_names)}")
                print(f"   Pull model with: ollama pull {self.config.model_name}")
                
            # Create OpenAI-compatible client for Ollama
            client = OpenAI(
                base_url=self.config.base_url,
                api_key="dummy-key",  # Ollama doesn't require API key
            )
            
            llm = LLMGenerator(client=client, model_name=self.config.model_name)
            print(f"✅ Ollama LLM initialized: {self.config.model_name}")
            return llm
            
        except Exception as e:
            print(f"❌ Failed to setup Ollama: {e}")
            print(f"   Ensure Ollama is running: ollama serve")
            return None
    
    def generate_answer_from_passages(
        self,
        question: str,
        passages: List[str],
        passage_ids: List[str] = None,
        scores: List[float] = None,
        use_cot: bool = True
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Generate answer from retrieved passages (following AutoSchemaKG pattern).
        
        Args:
            question: User's query
            passages: List of retrieved text passages
            passage_ids: Optional passage IDs
            scores: Optional relevance scores
            use_cot: Use Chain-of-Thought prompting
            
        Returns:
            Tuple of (answer, metadata)
        """
        if not self.llm_generator:
            return "LLM not available", {"error": "Ollama not configured"}
        
        # Format passages as context
        if scores:
            # Include scores in context for better prioritization
            context_parts = []
            for i, (passage, score) in enumerate(zip(passages, scores)):
                context_parts.append(f"[Relevance: {score:.4f}]\n{passage}")
            context = "\n\n".join(context_parts)
        else:
            context = "\n\n".join(passages)
        
        # Generate using AutoSchemaKG pattern
        if use_cot:
            # Use Chain-of-Thought system instruction (from original HippoRAG2)
            # Note: generate_with_context only accepts specific parameters
            answer = self.llm_generator.generate_with_context(
                question=question,
                context=context,
                max_new_tokens=self.config.max_new_tokens,
                temperature=self.config.temperature
            )
        else:
            # Direct generation without CoT
            messages = [
                {"role": "system", "content": "Answer the question based on the provided context."},
                {"role": "user", "content": f"Context:\n{context}\n\nQuestion: {question}\nAnswer:"}
            ]
            answer = self.llm_generator.generate_response(
                messages,
                **self.config.to_generation_kwargs()
            )
        
        # Extract answer from CoT format if present
        final_answer = self._extract_answer(answer)
        
        metadata = {
            "model": self.config.model_name,
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_new_tokens,
            "num_passages": len(passages),
            "use_cot": use_cot
        }
        
        return final_answer, metadata
    
    def generate_answer_from_kg(
        self,
        question: str,
        edges: List[str],
        edge_ids: List[str] = None,
        scores: List[float] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Generate answer from knowledge graph edges (following AutoSchemaKG pattern).
        
        Args:
            question: User's query
            edges: List of KG edges/triples
            edge_ids: Optional edge IDs
            scores: Optional relevance scores
            
        Returns:
            Tuple of (answer, metadata)
        """
        if not self.llm_generator:
            return "LLM not available", {"error": "Ollama not configured"}
        
        # Format edges as context
        if scores:
            context_parts = []
            for i, (edge, score) in enumerate(zip(edges, scores)):
                context_parts.append(f"[Relevance: {score:.4f}] {edge}")
            context = ". ".join(context_parts)
        else:
            context = ". ".join(edges)
        
        # Generate using KG-specific prompt (from original HippoRAG2)
        # Note: generate_with_context_kg only accepts specific parameters
        answer = self.llm_generator.generate_with_context_kg(
            question=question,
            context=context,
            max_new_tokens=self.config.max_new_tokens,
            temperature=self.config.temperature
        )
        
        # Extract answer from CoT format
        final_answer = self._extract_answer(answer)
        
        metadata = {
            "model": self.config.model_name,
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_new_tokens,
            "num_edges": len(edges),
            "type": "knowledge_graph"
        }
        
        return final_answer, metadata
    
    def _extract_answer(self, response: str) -> str:
        """
        Extract final answer from CoT response.
        
        The response format is:
        Thought: [reasoning process]
        Answer: [final answer]
        """
        if "Answer:" in response:
            # Extract text after "Answer:"
            answer = response.split("Answer:")[-1].strip()
            # Remove any trailing thought indicators
            if "Thought:" in answer:
                answer = answer.split("Thought:")[0].strip()
            return answer
        elif "answer:" in response.lower():
            # Case-insensitive fallback
            parts = response.lower().split("answer:")
            if len(parts) > 1:
                # Find the position and extract from original
                pos = response.lower().find("answer:") + 7
                return response[pos:].strip()
        
        # If no Answer marker, return the whole response
        return response.strip()
    
    def generate_with_react(
        self,
        question: str,
        search_history: List[Tuple[str, str, str]],
        current_context: str = None
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Generate answer using ReAct framework (for advanced reasoning).
        
        Args:
            question: User's query
            search_history: List of (thought, action, observation) tuples
            current_context: Current retrieved context
            
        Returns:
            Tuple of (answer_or_action, metadata)
        """
        if not self.llm_generator:
            return "LLM not available", {"error": "Ollama not configured"}
        
        # Note: generate_with_react only accepts max_new_tokens parameter
        response = self.llm_generator.generate_with_react(
            question=question,
            context=current_context,
            search_history=search_history,
            max_new_tokens=self.config.max_new_tokens
        )
        
        metadata = {
            "model": self.config.model_name,
            "framework": "ReAct",
            "iterations": len(search_history)
        }
        
        return response, metadata
    
    def print_tunable_parameters(self):
        """Print all tunable parameters with descriptions."""
        print("\n🎛️ TUNABLE OLLAMA PARAMETERS:")
        print("="*60)
        print("\n📊 Generation Parameters:")
        print(f"  • model_name: {self.config.model_name}")
        print(f"    (Available: {DEFAULT_OLLAMA_MODEL}, qwen3:30b-a3b-instruct-2507-q4_K_M)")
        print(f"  • max_new_tokens: {self.config.max_new_tokens} (1-8192)")
        print(f"    Controls maximum response length")
        print(f"  • temperature: {self.config.temperature} (0.0-2.0)")
        print(f"    0.0 = deterministic, 0.7 = balanced, 2.0 = very creative")
        print(f"  • top_p: {self.config.top_p} (0.0-1.0)")
        print(f"    Nucleus sampling - cumulative probability threshold")
        print(f"  • top_k: {self.config.top_k} (1-100)")
        print(f"    Consider only top K tokens at each step")
        
        print("\n🔧 Advanced Parameters:")
        print(f"  • frequency_penalty: {self.config.frequency_penalty} (-2.0 to 2.0)")
        print(f"    Reduce repetition of tokens")
        print(f"  • presence_penalty: {self.config.presence_penalty} (-2.0 to 2.0)")
        print(f"    Encourage talking about new topics")
        print(f"  • reasoning_effort: {self.config.reasoning_effort}")
        print(f"    For thinking models: 'low', 'medium', 'high'")
        print(f"  • seed: {self.config.seed}")
        print(f"    Set for reproducible outputs")
        
        print("\n💾 Response Format:")
        print(f"  • response_format: {self.config.response_format}")
        print(f"    Use {{'type': 'json_object'}} for JSON output")
        
        print("\n⏱️ Performance:")
        print(f"  • timeout: {self.config.timeout}s")
        print(f"  • max_retries: {self.config.max_retries}")
        print("="*60)


def demonstrate_answer_generation():
    """Demonstrate the answer generation with sample data."""
    print("🎯 HIPPORAG2 ANSWER GENERATION DEMO")
    print("="*60)
    
    # Create generator with custom config
    config = OllamaConfig(
        model_name=DEFAULT_OLLAMA_MODEL,  # Thinking variant
        temperature=0.7,
        max_new_tokens=1024,
        top_p=0.9,
        reasoning_effort="medium"  # For thinking model
    )
    
    generator = HippoRAG2AnswerGenerator(config)
    
    # Print tunable parameters
    generator.print_tunable_parameters()
    
    # Sample question and retrieved passages
    question = "What is an OCO order and how to place it in Bridge?"
    
    passages = [
        "OCO (One-Cancels-Other) orders consist of two orders placed simultaneously. When one order is executed, the other is automatically cancelled. This is useful for setting both profit targets and stop losses.",
        "In Bridge, OCO orders can be placed through the Order Management screen. Navigate to Trading > Orders > New Order, then select OCO as the order type. Enter both the limit and stop price levels.",
        "Bridge supports various order types including Market, Limit, Stop, and OCO orders. The platform provides real-time order status updates and execution confirmations."
    ]
    
    scores = [0.8234, 0.7156, 0.5923]  # Sample PageRank scores
    
    print("\n📝 Sample Question:", question)
    print("\n📚 Retrieved Passages:")
    for i, (passage, score) in enumerate(zip(passages, scores), 1):
        print(f"  {i}. [Score: {score:.4f}] {passage[:100]}...")
    
    print("\n🤖 Generating Answer...")
    answer, metadata = generator.generate_answer_from_passages(
        question=question,
        passages=passages,
        scores=scores,
        use_cot=True
    )
    
    print("\n✨ GENERATED ANSWER:")
    print("-"*50)
    print(answer)
    print("-"*50)
    
    print("\n📊 Generation Metadata:")
    for key, value in metadata.items():
        print(f"  • {key}: {value}")
    
    return generator


if __name__ == "__main__":
    generator = demonstrate_answer_generation()