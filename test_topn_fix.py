#!/usr/bin/env python3
"""
Quick test to verify the topN parameter fix works.
"""

import os
import sys
from pathlib import Path

# Set environment variables to avoid segfaults
os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'

def test_retriever_initialization():
    """Test that the retriever can be initialized without parameter errors."""
    
    print("=" * 70)
    print("🧪 Testing HippoRAG2 Retriever Initialization")
    print("=" * 70)
    
    try:
        # Import after setting env vars
        from hipporag2_interactive_debug import InteractiveHippoRAG2Debug
        
        print("\n1. Creating console instance...")
        console = InteractiveHippoRAG2Debug()
        print("   ✅ Console created")
        
        print("\n2. Loading data (using pre-computed embeddings)...")
        if not console.load_data():
            print("   ❌ Failed to load data")
            return False
        print("   ✅ Data loaded")
        
        print("\n3. Setting up LLM...")
        if not console.setup_llm():
            print("   ❌ Failed to setup LLM")
            return False
        print("   ✅ LLM setup complete")
        
        print("\n4. Initializing retriever (this tests the parameter fix)...")
        if not console.initialize_retriever():
            print("   ❌ Failed to initialize retriever")
            return False
        print("   ✅ Retriever initialized successfully!")
        
        print("\n5. Testing retrieve_personalization_dict directly...")
        test_query = "What is an OCO order?"
        
        # This will call the fixed method with topN parameter
        result = console.retriever.retrieve_personalization_dict(test_query, topN=15)
        
        if result:
            print(f"   ✅ retrieve_personalization_dict works!")
            print(f"      Returned {len(result)} items")
        else:
            print("   ❌ retrieve_personalization_dict failed")
            return False
        
        print("\n" + "=" * 70)
        print("✅ All tests passed! The topN parameter fix is working.")
        print("=" * 70)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    
    print("🔧 Testing topN/topk Parameter Fix")
    print("=" * 70)
    print("This test verifies that:")
    print("1. The parameter name mismatch is fixed")
    print("2. Pre-computed embeddings are loaded (not regenerated)")
    print("3. The retriever can be initialized and used")
    print()
    
    success = test_retriever_initialization()
    
    if success:
        print("\n✅ SUCCESS: You can now run the interactive console!")
        print("   python hipporag2_interactive_debug.py")
    else:
        print("\n❌ FAILURE: Please check the error messages above")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())