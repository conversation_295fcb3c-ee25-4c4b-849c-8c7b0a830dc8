#!/usr/bin/env python3
"""
Debug script to investigate MMC content retrieval issues in HippoRAG2
"""

import sys
import os
import pickle
import json
import networkx as nx
import numpy as np
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def debug_mmc_content():
    """Debug MMC content availability and retrieval"""
    
    print("=== MMC Content Debug Analysis ===\n")
    
    # 1. Check GraphML content
    print("1. Checking GraphML file for MMC content...")
    graphml_path = "import/pdf_dataset/kg_graphml/knowledge_graph.graphml"
    
    if os.path.exists(graphml_path):
        print(f"   GraphML file exists: {graphml_path}")
        
        # Load the graph
        G = nx.read_graphml(graphml_path)
        print(f"   Total nodes: {len(G.nodes)}")
        print(f"   Total edges: {len(G.edges)}")
        
        # Find MMC-related nodes
        mmc_nodes = []
        mmc_text_nodes = []
        
        for node_id in G.nodes:
            node_data = G.nodes[node_id]
            
            # Check for MMC in node names/ids
            if "MMC" in str(node_id) or "Market Maker Cockpit" in str(node_id):
                mmc_nodes.append((node_id, node_data))
            
            # Check for MMC in text content
            if hasattr(node_data, 'get') and node_data.get('text') and "MMC" in node_data.get('text', ''):
                mmc_text_nodes.append((node_id, node_data))
            
            # Check for the specific MMC text node
            if node_id == "1fb3759bd9af49535dc71864a9dfaa0382b097be4ae24abdd55ef9dd5838b149":
                print(f"   ✓ Found specific MMC text node: {node_id}")
                print(f"     Node type: {node_data.get('type', 'unknown')}")
                print(f"     Node label: {node_data.get(':LABEL', 'unknown')}")
                if hasattr(node_data, 'get') and node_data.get('text'):
                    print(f"     Text preview: {node_data['text'][:100]}...")
        
        print(f"   MMC entity nodes found: {len(mmc_nodes)}")
        print(f"   MMC text nodes found: {len(mmc_text_nodes)}")
        
        # Show some MMC nodes
        for i, (node_id, node_data) in enumerate(mmc_nodes[:3]):
            print(f"     MMC node {i+1}: {node_id} (type: {node_data.get('type', 'unknown')})")
        
        # Check scenario nodes
        scenario_nodes = []
        for node_id in G.nodes:
            if "scenario" in str(node_id).lower():
                scenario_nodes.append(node_id)
        
        print(f"   Scenario nodes found: {len(scenario_nodes)}")
        for scenario in scenario_nodes[:3]:
            print(f"     Scenario: {scenario}")
    
    else:
        print(f"   ❌ GraphML file not found: {graphml_path}")
    
    # 2. Check text nodes CSV
    print("\n2. Checking text nodes CSV...")
    text_csv_path = "import/pdf_dataset/triples_csv/text_nodes__from_json_with_numeric_id.csv"
    
    if os.path.exists(text_csv_path):
        print(f"   Text CSV exists: {text_csv_path}")
        
        # Count MMC mentions
        mmc_count = 0
        scenario_count = 0
        mmc_node_found = False
        
        with open(text_csv_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f):
                if line_num == 0:  # Skip header
                    continue
                    
                if "MMC" in line or "Market Maker Cockpit" in line:
                    mmc_count += 1
                    
                if "scenario" in line.lower():
                    scenario_count += 1
                    
                if "1fb3759bd9af49535dc71864a9dfaa0382b097be4ae24abdd55ef9dd5838b149" in line:
                    mmc_node_found = True
                    parts = line.split(',', 3)
                    if len(parts) >= 3:
                        print(f"   ✓ MMC text node found with numeric ID: {parts[2]}")
        
        print(f"   MMC mentions in text: {mmc_count}")
        print(f"   Scenario mentions in text: {scenario_count}")
        print(f"   Specific MMC node found: {mmc_node_found}")
    
    else:
        print(f"   ❌ Text CSV not found: {text_csv_path}")
    
    # 3. Check embeddings
    print("\n3. Checking embeddings...")
    embedding_mapping_path = "import/pdf_dataset/kg_embedding/node_id_mapping.csv"
    
    if os.path.exists(embedding_mapping_path):
        print(f"   Embedding mapping exists: {embedding_mapping_path}")
        
        # Check if node 133 (MMC node) has embeddings
        with open(embedding_mapping_path, 'r') as f:
            lines = f.readlines()
            total_embeddings = len(lines) - 1  # Exclude header
            print(f"   Total nodes with embeddings: {total_embeddings}")
            
            # Check for node 133
            for line in lines[1:]:  # Skip header
                if line.startswith("133,"):
                    print(f"   ✓ MMC node (133) has embeddings: {line.strip()}")
                    break
            else:
                print(f"   ❌ MMC node (133) NOT found in embeddings")
    
    else:
        print(f"   ❌ Embedding mapping not found: {embedding_mapping_path}")
    
    # 4. Check actual embedding files
    print("\n4. Checking embedding files...")
    
    node_emb_path = "import/pdf_dataset/kg_embedding/node_embeddings.npy"
    if os.path.exists(node_emb_path):
        try:
            embeddings = np.load(node_emb_path)
            print(f"   Node embeddings shape: {embeddings.shape}")
            print(f"   Embedding dimension: {embeddings.shape[1] if len(embeddings.shape) > 1 else 'scalar'}")
            
            # Check if we can access embedding 133
            if embeddings.shape[0] > 133:
                emb_133 = embeddings[133]
                print(f"   ✓ MMC node embedding (133) accessible, norm: {np.linalg.norm(emb_133):.4f}")
            else:
                print(f"   ❌ MMC node embedding (133) out of range")
                
        except Exception as e:
            print(f"   ❌ Error loading embeddings: {e}")
    else:
        print(f"   ❌ Node embeddings not found: {node_emb_path}")

def check_vector_indices():
    """Check if vector indices include MMC content"""
    print("\n5. Checking vector indices...")
    
    # Check text node FAISS index
    text_index_path = "import/pdf_dataset/vector_index/text_nodes__from_json_with_emb_non_norm.index"
    if os.path.exists(text_index_path):
        print(f"   ✓ Text FAISS index exists: {text_index_path}")
        
        # Get file size
        size_mb = os.path.getsize(text_index_path) / (1024 * 1024)
        print(f"   Index size: {size_mb:.2f} MB")
    else:
        print(f"   ❌ Text FAISS index not found")
    
    # Check edge FAISS index
    edge_index_path = "import/pdf_dataset/vector_index/triple_edges__from_json_with_concept_with_emb_non_norm.index"
    if os.path.exists(edge_index_path):
        print(f"   ✓ Edge FAISS index exists: {edge_index_path}")
        
        # Get file size
        size_mb = os.path.getsize(edge_index_path) / (1024 * 1024)
        print(f"   Index size: {size_mb:.2f} MB")
    else:
        print(f"   ❌ Edge FAISS index not found")

def analyze_mmc_connectivity():
    """Analyze MMC node connectivity in the graph"""
    print("\n6. Analyzing MMC connectivity...")
    
    graphml_path = "import/pdf_dataset/kg_graphml/knowledge_graph.graphml"
    
    if not os.path.exists(graphml_path):
        print("   ❌ GraphML file not found")
        return
    
    G = nx.read_graphml(graphml_path)
    
    # Find MMC-related entities
    mmc_entities = []
    scenario_entities = []
    
    for node_id in G.nodes:
        node_data = G.nodes[node_id]
        
        if "MMC" in str(node_id) or "Market Maker Cockpit" in str(node_id):
            mmc_entities.append(node_id)
            
        if "scenario" in str(node_id).lower():
            scenario_entities.append(node_id)
    
    print(f"   MMC entities: {len(mmc_entities)}")
    print(f"   Scenario entities: {len(scenario_entities)}")
    
    # Check specific MMC text node connectivity
    mmc_text_node = "1fb3759bd9af49535dc71864a9dfaa0382b097be4ae24abdd55ef9dd5838b149"
    
    if mmc_text_node in G.nodes:
        # Get connected entities
        predecessors = list(G.predecessors(mmc_text_node))
        successors = list(G.successors(mmc_text_node))
        
        print(f"   MMC text node connections:")
        print(f"     Incoming edges: {len(predecessors)}")
        print(f"     Outgoing edges: {len(successors)}")
        
        # Show some connected entities
        for i, pred in enumerate(predecessors[:5]):
            print(f"     <- {pred}")
        
        # Check if scenario entities connect to MMC text
        scenario_connections = []
        for scenario in scenario_entities:
            if G.has_edge(scenario, mmc_text_node):
                scenario_connections.append(scenario)
        
        print(f"   Scenario entities connected to MMC text: {len(scenario_connections)}")
        for scenario in scenario_connections:
            print(f"     Scenario -> MMC: {scenario}")
    
    else:
        print(f"   ❌ MMC text node not found in graph")

if __name__ == "__main__":
    debug_mmc_content()
    check_vector_indices()
    analyze_mmc_connectivity()
    
    print("\n=== Analysis Complete ===")
    print("\nKey findings will help identify where MMC content is lost in the retrieval pipeline.")