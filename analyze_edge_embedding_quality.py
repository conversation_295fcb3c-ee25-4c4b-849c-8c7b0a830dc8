#!/usr/bin/env python3
"""
Edge Embedding Quality Analysis

This script analyzes the quality of edge embeddings by:
1. Searching for actual financial content in the edge list
2. Testing if financial edges exist but embeddings don't match them
3. Understanding the embedding model's domain knowledge
4. Identifying why financial queries fail to find financial edges
"""

import os
import sys
import json
import pickle
import numpy as np
from pathlib import Path
from collections import Counter
import re

# Set environment variable to avoid tokenizer warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Add the project root to Python path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from atlas_rag.vectorstore.embedding_model import SentenceEmbedding
from sentence_transformers import SentenceTransformer
from setup_embedding_model import setup_qwen_embedding_model

class EdgeEmbeddingAnalyzer:
    """Analyzer for edge embedding quality and financial domain coverage."""
    
    def __init__(self, data_file: str = "import/pdf_dataset/complete_data.pkl"):
        self.data_file = Path(data_file)
        self.data = None
        self.sentence_encoder = None
        
    def load_data(self):
        """Load the complete data file."""
        print("📁 EDGE EMBEDDING QUALITY ANALYSIS")
        print("=" * 50)
        print("Analyzing why financial queries don't match financial edges...")
        print()
        
        if not self.data_file.exists():
            print(f"❌ Data file not found: {self.data_file}")
            return False
        
        try:
            with open(self.data_file, 'rb') as f:
                self.data = pickle.load(f)
            print(f"✅ Data loaded successfully")
            print(f"   📊 Total edges: {len(self.data['edge_list'])}")
            print(f"   🔗 Edge embeddings shape: {self.data['edge_embeddings'].shape}")
            return True
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return False
    
    def setup_encoder(self):
        """Setup the sentence encoder used in the system - MUST match edge embedding model."""
        try:
            print("🔧 CRITICAL: Using Qwen3-Embedding-4B to match edge embeddings...")
            self.sentence_encoder = setup_qwen_embedding_model()
            print(f"✅ Qwen3-Embedding-4B encoder setup - COMPATIBLE with edge embeddings!")
            return True
        except Exception as e:
            print(f"❌ Error setting up Qwen3-Embedding-4B encoder: {e}")
            print("🚨 This will cause embedding mismatch - edge embeddings are Qwen3-Embedding-4B!")
            return False
    
    def search_for_financial_edges(self):
        """Search for actual financial content in the edge list."""
        print("\n🔍 STEP 1: SEARCHING FOR FINANCIAL CONTENT IN EDGES")
        print("=" * 55)
        
        edge_list = self.data['edge_list']
        KG = self.data['KG']
        
        # Financial terms to search for
        financial_terms = {
            'risk_reversal': ['risk reversal', 'risk-reversal', 'reversal option', 'risk strategy'],
            'mmc': ['mmc', 'market maker cockpit', 'cockpit', 'market maker'],
            'bridge': ['bridge', 'trading platform', 'trading system'],
            'options': ['option', 'call option', 'put option', 'option strategy'],
            'scenarios': ['scenario', 'scenarios', 'scenario creation', 'create scenario'],
            'trading': ['trading', 'trade', 'execution', 'order']
        }
        
        financial_edges = {}
        for category, terms in financial_terms.items():
            financial_edges[category] = []
        
        print(f"🔍 Scanning {len(edge_list)} edges for financial content...")
        
        for i, edge in enumerate(edge_list):
            if i % 20000 == 0:
                print(f"   Progress: {i:,}/{len(edge_list):,} ({i/len(edge_list)*100:.1f}%)")
            
            try:
                # Get edge details
                head_node = KG.nodes[edge[0]]
                tail_node = KG.nodes[edge[1]]
                relation = KG.edges[edge].get('relation', '')
                
                # Combine all text for searching
                head_text = head_node.get('text', head_node.get('id', ''))
                tail_text = tail_node.get('text', tail_node.get('id', ''))
                combined_text = f"{head_text} {relation} {tail_text}".lower()
                
                # Check for financial terms
                for category, terms in financial_terms.items():
                    for term in terms:
                        if term.lower() in combined_text:
                            edge_info = {
                                'index': i,
                                'edge': edge,
                                'head_text': head_text[:200],
                                'relation': relation,
                                'tail_text': tail_text[:200],
                                'matched_term': term,
                                'combined_length': len(combined_text)
                            }
                            financial_edges[category].append(edge_info)
                            break  # Only count once per category
                            
            except Exception as e:
                continue  # Skip problematic edges
        
        print(f"✅ Financial content scan completed")
        print("\n📊 FINANCIAL CONTENT FOUND:")
        print("-" * 40)
        
        total_financial_edges = 0
        for category, edges in financial_edges.items():
            count = len(edges)
            total_financial_edges += count
            percentage = (count / len(edge_list)) * 100
            print(f"   {category.replace('_', ' ').title()}: {count:,} edges ({percentage:.3f}%)")
            
            # Show top 3 examples
            if count > 0:
                print(f"     Examples:")
                for j, edge_info in enumerate(edges[:3]):
                    head_preview = edge_info['head_text'][:50] + "..." if len(edge_info['head_text']) > 50 else edge_info['head_text']
                    tail_preview = edge_info['tail_text'][:50] + "..." if len(edge_info['tail_text']) > 50 else edge_info['tail_text']
                    print(f"       {j+1}. [{head_preview}] --{edge_info['relation']}--> [{tail_preview}]")
                print()
        
        print(f"📈 SUMMARY:")
        print(f"   Total financial edges found: {total_financial_edges:,}")
        print(f"   Percentage of total edges: {(total_financial_edges/len(edge_list))*100:.3f}%")
        
        if total_financial_edges == 0:
            print("❌ CRITICAL: NO financial content found in edge list!")
            print("💡 Issue may be in triple extraction - financial content not being converted to edges")
        else:
            print("✅ Financial content EXISTS in edge list")
            print("💡 Issue is likely in embedding similarity matching")
        
        return financial_edges
    
    def test_embedding_similarity(self, financial_edges):
        """Test if the embedding model can match financial queries to financial edges."""
        print("\n🔍 STEP 2: TESTING EMBEDDING SIMILARITY MATCHING")
        print("=" * 55)
        
        if not financial_edges or sum(len(edges) for edges in financial_edges.values()) == 0:
            print("❌ No financial edges found - skipping embedding tests")
            return
        
        test_queries = [
            "what is a risk reversal option, and how to create one in Bridge?",
            "how to create scenarios in mmc?",
            "Bridge trading platform features"
        ]
        
        print("🧪 Testing embedding similarity for financial queries...")
        
        # Get some financial edges to test against
        test_edges = []
        for category, edges in financial_edges.items():
            if edges:
                test_edges.extend(edges[:5])  # Take top 5 from each category
        
        if not test_edges:
            print("❌ No test edges available")
            return
        
        print(f"📝 Testing {len(test_queries)} queries against {len(test_edges)} financial edges...")
        
        for query in test_queries:
            print(f"\n🎯 Query: '{query}'")
            print("-" * 30)
            
            # Get query embedding
            query_emb = self.sentence_encoder.encode([query], query_type="edge")
            
            # Test against financial edges
            similarities = []
            for edge_info in test_edges:
                # Create edge string (same format as HippoRAG2)
                edge_str = f"{edge_info['head_text']} {edge_info['relation']} {edge_info['tail_text']}"
                
                # Get edge embedding
                edge_emb = self.sentence_encoder.encode([edge_str], query_type="edge")
                
                # Compute similarity
                similarity = np.dot(query_emb[0], edge_emb[0])
                similarities.append({
                    'similarity': similarity,
                    'edge_info': edge_info,
                    'edge_str': edge_str[:100] + "..." if len(edge_str) > 100 else edge_str
                })
            
            # Sort by similarity
            similarities.sort(key=lambda x: x['similarity'], reverse=True)
            
            print(f"   Top 3 financial edge matches:")
            for i, sim_info in enumerate(similarities[:3]):
                score = sim_info['similarity']
                edge_str = sim_info['edge_str']
                matched_term = sim_info['edge_info']['matched_term']
                print(f"     #{i+1}: {score:.3f} | {edge_str} (matched: {matched_term})")
            
            # Check if any good matches
            best_score = similarities[0]['similarity']
            if best_score > 0.7:
                print(f"   ✅ Good similarity found: {best_score:.3f}")
            elif best_score > 0.5:
                print(f"   ⚠️  Moderate similarity: {best_score:.3f}")
            else:
                print(f"   ❌ Poor similarity: {best_score:.3f}")
                print(f"   💡 Embedding model may not understand financial domain")
    
    def analyze_embedding_model_domain(self):
        """Analyze if the embedding model understands financial domain terms."""
        print("\n🔍 STEP 3: EMBEDDING MODEL DOMAIN ANALYSIS")
        print("=" * 50)
        
        # Test financial term understanding
        financial_terms = [
            "risk reversal option",
            "market maker cockpit",
            "bridge trading platform",
            "option strategy",
            "scenario creation"
        ]
        
        general_terms = [
            "document management",
            "user interface",
            "system configuration",
            "data processing",
            "file upload"
        ]
        
        print("🧪 Testing embedding model's understanding of financial vs general terms...")
        
        # Get embeddings for all terms
        all_terms = financial_terms + general_terms
        embeddings = self.sentence_encoder.encode(all_terms, query_type="edge")
        
        print(f"\n📊 FINANCIAL TERM CLUSTERING ANALYSIS:")
        print("-" * 40)
        
        # Compute similarity matrix between financial terms
        financial_embs = embeddings[:len(financial_terms)]
        general_embs = embeddings[len(financial_terms):]
        
        # Average similarity within financial terms
        financial_similarities = []
        for i in range(len(financial_terms)):
            for j in range(i+1, len(financial_terms)):
                sim = np.dot(financial_embs[i], financial_embs[j])
                financial_similarities.append(sim)
        
        # Average similarity within general terms
        general_similarities = []
        for i in range(len(general_terms)):
            for j in range(i+1, len(general_terms)):
                sim = np.dot(general_embs[i], general_embs[j])
                general_similarities.append(sim)
        
        # Cross-domain similarities (financial vs general)
        cross_similarities = []
        for financial_emb in financial_embs:
            for general_emb in general_embs:
                sim = np.dot(financial_emb, general_emb)
                cross_similarities.append(sim)
        
        avg_financial = np.mean(financial_similarities)
        avg_general = np.mean(general_similarities)
        avg_cross = np.mean(cross_similarities)
        
        print(f"   Financial terms inter-similarity: {avg_financial:.3f}")
        print(f"   General terms inter-similarity: {avg_general:.3f}")
        print(f"   Cross-domain similarity: {avg_cross:.3f}")
        
        # Analysis
        if avg_financial > avg_cross + 0.1:
            print("   ✅ Model shows good financial domain clustering")
        elif avg_financial > avg_cross:
            print("   ⚠️  Model shows weak financial domain clustering")
        else:
            print("   ❌ Model does not cluster financial terms well")
            print(f"   💡 Model may have poor financial domain knowledge")
    
    def run_complete_analysis(self):
        """Run the complete edge embedding quality analysis."""
        if not self.load_data():
            return False
        
        if not self.setup_encoder():
            return False
        
        # Step 1: Search for financial content
        financial_edges = self.search_for_financial_edges()
        
        # Step 2: Test embedding similarity
        self.test_embedding_similarity(financial_edges)
        
        # Step 3: Analyze embedding model domain knowledge
        self.analyze_embedding_model_domain()
        
        print("\n" + "=" * 60)
        print("🎯 EDGE EMBEDDING ANALYSIS COMPLETE")
        print("=" * 60)
        
        return True

def main():
    """Main analysis function."""
    analyzer = EdgeEmbeddingAnalyzer()
    
    success = analyzer.run_complete_analysis()
    
    if success:
        print("✅ Edge embedding quality analysis completed")
        print("💡 Check results above to identify the root cause")
    else:
        print("❌ Analysis failed")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)