{"pdf_dataset": {"dataset_name": "pdf_dataset", "directory_structure": {"triples_csv": true, "concept_csv": true, "kg_extraction": true, "vector_index": true}, "file_presence": {"triples_csv": {"triple_nodes_*_from_json_without_emb_with_numeric_id.csv": {"found": true, "files": ["triple_nodes__from_json_without_emb_with_numeric_id.csv"]}, "text_nodes_*_from_json_with_numeric_id.csv": {"found": true, "files": ["text_nodes__from_json_with_numeric_id.csv"]}, "triple_edges_*_from_json_without_emb_with_numeric_id.csv": {"found": true, "files": ["triple_edges__from_json_without_emb_with_numeric_id.csv"]}, "text_edges_*_from_json.csv": {"found": true, "files": ["text_edges__from_json.csv"]}}, "concept_csv": {"concept_nodes_*_from_json_with_concept.csv": {"found": false, "files": []}, "concept_edges_*_from_json_with_concept.csv": {"found": true, "files": ["triple_edges__from_json_with_concept.csv"]}}}, "schema_compliance": {"triple_nodes": {"triple_nodes__from_json_without_emb_with_numeric_id.csv": {"compliant": true, "actual_columns": ["name:<PERSON>", "type", "concepts", "synsets", "numeric_id", ":LABEL"], "missing_columns": [], "extra_columns": []}, "triple_nodes__from_json_with_emb.csv": {"compliant": false, "actual_columns": ["name:<PERSON>", "type", "file_id", "concepts", "synsets", "embedding:STRING", ":LABEL"], "missing_columns": ["numeric_id"], "extra_columns": ["file_id", "embedding:STRING"]}, "triple_nodes__from_json_without_emb.csv": {"compliant": false, "actual_columns": ["name:<PERSON>", "type", "concepts", "synsets", ":LABEL"], "missing_columns": ["numeric_id"], "extra_columns": []}}, "text_nodes": {"text_nodes__from_json_with_emb.csv": {"compliant": false, "actual_columns": ["text_id:ID", "original_text", ":LABEL", "embedding:STRING"], "missing_columns": ["numeric_id"], "extra_columns": ["embedding:STRING"]}, "text_nodes__from_json_with_numeric_id.csv": {"compliant": true, "actual_columns": ["text_id:ID", "original_text", "numeric_id", ":LABEL"], "missing_columns": [], "extra_columns": []}, "text_nodes__from_json.csv": {"compliant": false, "actual_columns": ["text_id:ID", "original_text", ":LABEL"], "missing_columns": ["numeric_id"], "extra_columns": []}}, "triple_edges": {"triple_edges__from_json_with_concept_with_emb.csv": {"compliant": false, "actual_columns": [":START_ID", ":END_ID", "relation", "file_id", "concepts", "synsets", "embedding:STRING", ":TYPE"], "missing_columns": ["head:START_ID", "numeric_id", "relation:TYPE", "tail:END_ID"], "extra_columns": ["concepts", ":TYPE", "relation", "embedding:STRING", ":END_ID", "synsets", "file_id", ":START_ID"]}, "triple_edges__from_json_without_emb_with_numeric_id.csv": {"compliant": false, "actual_columns": [":START_ID", ":END_ID", "relation", "concepts", "synsets", "numeric_id", ":TYPE"], "missing_columns": ["head:START_ID", "relation:TYPE", "tail:END_ID"], "extra_columns": ["concepts", ":TYPE", "relation", ":END_ID", "synsets", ":START_ID"]}, "triple_edges__from_json_without_emb.csv": {"compliant": false, "actual_columns": [":START_ID", ":END_ID", "relation", "concepts", "synsets", ":TYPE"], "missing_columns": ["head:START_ID", "numeric_id", "relation:TYPE", "tail:END_ID"], "extra_columns": ["concepts", ":TYPE", "relation", ":END_ID", "synsets", ":START_ID"]}, "triple_edges__from_json_with_concept.csv": {"compliant": false, "actual_columns": [":START_ID", ":END_ID", "relation", "concepts", "synsets", ":TYPE"], "missing_columns": ["head:START_ID", "numeric_id", "relation:TYPE", "tail:END_ID"], "extra_columns": ["concepts", ":TYPE", "relation", ":END_ID", "synsets", ":START_ID"]}}, "text_edges": {"text_edges__from_json.csv": {"compliant": false, "actual_columns": [":START_ID", ":END_ID", ":TYPE"], "missing_columns": ["head:START_ID", "relation:TYPE", "tail:END_ID"], "extra_columns": [":TYPE", ":START_ID", ":END_ID"]}}, "concept_nodes": {"concept_nodes.csv": {"compliant": false, "actual_columns": ["node_id", "name", "type"], "missing_columns": ["concepts", ":LABEL", "name:<PERSON>", "synsets"], "extra_columns": ["name", "node_id"]}}, "concept_edges": {"status": "missing"}}, "numeric_id_status": {"triple_nodes_*_with_numeric_id.csv": {"found": true, "files": ["triple_edges__from_json_without_emb_with_numeric_id.csv", "triple_nodes__from_json_without_emb_with_numeric_id.csv", "text_nodes__from_json_with_numeric_id.csv"]}, "text_nodes_*_with_numeric_id.csv": {"found": true, "files": ["triple_edges__from_json_without_emb_with_numeric_id.csv", "triple_nodes__from_json_without_emb_with_numeric_id.csv", "text_nodes__from_json_with_numeric_id.csv"]}, "triple_edges_*_with_numeric_id.csv": {"found": true, "files": ["triple_edges__from_json_without_emb_with_numeric_id.csv", "triple_nodes__from_json_without_emb_with_numeric_id.csv", "text_nodes__from_json_with_numeric_id.csv"]}}, "data_quality": {"triple_nodes__from_json_without_emb_with_numeric_id.csv": {"total_rows": 62157, "type_distribution": {"entity": 35782, "event": 26375}}, "triple_nodes__from_json_with_emb.csv": {"total_rows": 62157, "type_distribution": {"entity": 35782, "event": 26375}}, "triple_nodes__from_json_without_emb.csv": {"total_rows": 62157, "type_distribution": {"entity": 35782, "event": 26375}}, "triple_edges__from_json_with_concept_with_emb.csv": {"total_rows": 111787}, "triple_edges__from_json_without_emb_with_numeric_id.csv": {"total_rows": 111787}, "triple_edges__from_json_without_emb.csv": {"total_rows": 111787}, "text_edges__from_json.csv": {"total_rows": 62157}}}, "360t_guide_direct_api_v2": {"dataset_name": "360t_guide_direct_api_v2", "directory_structure": {"triples_csv": true, "concept_csv": true, "kg_extraction": true, "vector_index": true}, "file_presence": {"triples_csv": {"triple_nodes_*_from_json_without_emb_with_numeric_id.csv": {"found": false, "files": []}, "text_nodes_*_from_json_with_numeric_id.csv": {"found": false, "files": []}, "triple_edges_*_from_json_without_emb_with_numeric_id.csv": {"found": false, "files": []}, "text_edges_*_from_json.csv": {"found": true, "files": ["text_edges__from_json.csv"]}}, "concept_csv": {"concept_nodes_*_from_json_with_concept.csv": {"found": true, "files": ["concept_nodes__from_json_with_concept.csv"]}, "concept_edges_*_from_json_with_concept.csv": {"found": true, "files": ["triple_edges__from_json_with_concept.csv", "concept_edges__from_json_with_concept.csv"]}}}, "schema_compliance": {"triple_nodes": {"triple_nodes__from_json_without_emb.csv": {"compliant": false, "actual_columns": ["name:<PERSON>", "type", "concepts", "synsets", ":LABEL"], "missing_columns": ["numeric_id"], "extra_columns": []}}, "text_nodes": {"text_nodes__from_json.csv": {"compliant": false, "actual_columns": ["text_id:ID", "original_text", ":LABEL"], "missing_columns": ["numeric_id"], "extra_columns": []}}, "triple_edges": {"triple_edges__from_json_without_emb.csv": {"compliant": false, "actual_columns": [":START_ID", ":END_ID", "relation", "concepts", "synsets", ":TYPE"], "missing_columns": ["head:START_ID", "numeric_id", "relation:TYPE", "tail:END_ID"], "extra_columns": ["concepts", ":TYPE", "relation", ":END_ID", "synsets", ":START_ID"]}, "triple_edges__from_json_with_concept.csv": {"compliant": false, "actual_columns": [":START_ID", ":END_ID", "relation", "concepts", "synsets", ":TYPE"], "missing_columns": ["head:START_ID", "numeric_id", "relation:TYPE", "tail:END_ID"], "extra_columns": ["concepts", ":TYPE", "relation", ":END_ID", "synsets", ":START_ID"]}}, "text_edges": {"text_edges__from_json.csv": {"compliant": false, "actual_columns": [":START_ID", ":END_ID", ":TYPE"], "missing_columns": ["head:START_ID", "relation:TYPE", "tail:END_ID"], "extra_columns": [":TYPE", ":START_ID", ":END_ID"]}}, "concept_nodes": {"concept_nodes__from_json_with_concept.csv": {"compliant": false, "actual_columns": ["concept_id:ID", "name", ":LABEL"], "missing_columns": ["concepts", "type", "name:<PERSON>", "synsets"], "extra_columns": ["name", "concept_id:ID"]}}, "concept_edges": {"concept_edges__from_json_with_concept.csv": {"compliant": false, "actual_columns": [":START_ID", ":END_ID", "relation", ":TYPE"], "missing_columns": ["head:START_ID", "relation:TYPE", "tail:END_ID"], "extra_columns": ["relation", ":TYPE", ":START_ID", ":END_ID"]}}}, "numeric_id_status": {"triple_nodes_*_with_numeric_id.csv": {"found": false, "files": []}, "text_nodes_*_with_numeric_id.csv": {"found": false, "files": []}, "triple_edges_*_with_numeric_id.csv": {"found": false, "files": []}}, "data_quality": {"triple_nodes__from_json_without_emb.csv": {"total_rows": 479, "type_distribution": {"entity": 327, "event": 152}}, "triple_edges__from_json_without_emb.csv": {"total_rows": 537}, "text_edges__from_json.csv": {"total_rows": 479}}}, "CICGPC_Glazing_ver1.0a": {"dataset_name": "CICGPC_Glazing_ver1.0a", "directory_structure": {"triples_csv": true, "concept_csv": true, "kg_extraction": true, "vector_index": false}, "file_presence": {"triples_csv": {"triple_nodes_*_from_json_without_emb_with_numeric_id.csv": {"found": false, "files": []}, "text_nodes_*_from_json_with_numeric_id.csv": {"found": false, "files": []}, "triple_edges_*_from_json_without_emb_with_numeric_id.csv": {"found": false, "files": []}, "text_edges_*_from_json.csv": {"found": true, "files": ["text_edges_CICGPC_Glazing_ver1.0a_from_json.csv"]}}, "concept_csv": {"concept_nodes_*_from_json_with_concept.csv": {"found": true, "files": ["concept_nodes_CICGPC_Glazing_ver1.0a_from_json_with_concept.csv"]}, "concept_edges_*_from_json_with_concept.csv": {"found": true, "files": ["concept_edges_CICGPC_Glazing_ver1.0a_from_json_with_concept.csv", "triple_edges_CICGPC_Glazing_ver1.0a_from_json_with_concept.csv"]}}}, "schema_compliance": {"triple_nodes": {"triple_nodes_CICGPC_Glazing_ver1.0a_from_json_without_emb.csv": {"compliant": false, "actual_columns": ["name:<PERSON>", "type", "concepts", "synsets", ":LABEL"], "missing_columns": ["numeric_id"], "extra_columns": []}}, "text_nodes": {"text_nodes_CICGPC_Glazing_ver1.0a_from_json.csv": {"compliant": false, "actual_columns": ["text_id:ID", "original_text", ":LABEL"], "missing_columns": ["numeric_id"], "extra_columns": []}}, "triple_edges": {"triple_edges_CICGPC_Glazing_ver1.0a_from_json_without_emb.csv": {"compliant": false, "actual_columns": [":START_ID", ":END_ID", "relation", "concepts", "synsets", ":TYPE"], "missing_columns": ["head:START_ID", "numeric_id", "relation:TYPE", "tail:END_ID"], "extra_columns": ["concepts", ":TYPE", "relation", ":END_ID", "synsets", ":START_ID"]}, "triple_edges_CICGPC_Glazing_ver1.0a_from_json_with_concept.csv": {"compliant": false, "actual_columns": [":START_ID", ":END_ID", "relation", "concepts", "synsets", ":TYPE"], "missing_columns": ["head:START_ID", "numeric_id", "relation:TYPE", "tail:END_ID"], "extra_columns": ["concepts", ":TYPE", "relation", ":END_ID", "synsets", ":START_ID"]}}, "text_edges": {"text_edges_CICGPC_Glazing_ver1.0a_from_json.csv": {"compliant": false, "actual_columns": [":START_ID", ":END_ID", ":TYPE"], "missing_columns": ["head:START_ID", "relation:TYPE", "tail:END_ID"], "extra_columns": [":TYPE", ":START_ID", ":END_ID"]}}, "concept_nodes": {"concept_nodes_CICGPC_Glazing_ver1.0a_from_json_with_concept.csv": {"compliant": false, "actual_columns": ["concept_id:ID", "name", ":LABEL"], "missing_columns": ["concepts", "type", "name:<PERSON>", "synsets"], "extra_columns": ["name", "concept_id:ID"]}}, "concept_edges": {"concept_edges_CICGPC_Glazing_ver1.0a_from_json_with_concept.csv": {"compliant": false, "actual_columns": [":START_ID", ":END_ID", "relation", ":TYPE"], "missing_columns": ["head:START_ID", "relation:TYPE", "tail:END_ID"], "extra_columns": ["relation", ":TYPE", ":START_ID", ":END_ID"]}}}, "numeric_id_status": {"triple_nodes_*_with_numeric_id.csv": {"found": false, "files": []}, "text_nodes_*_with_numeric_id.csv": {"found": false, "files": []}, "triple_edges_*_with_numeric_id.csv": {"found": false, "files": []}}, "data_quality": {"triple_nodes_CICGPC_Glazing_ver1.0a_from_json_without_emb.csv": {"total_rows": 500, "type_distribution": {"entity": 275, "event": 225}}, "text_edges_CICGPC_Glazing_ver1.0a_from_json.csv": {"total_rows": 500}, "triple_edges_CICGPC_Glazing_ver1.0a_from_json_without_emb.csv": {"total_rows": 492}}}}