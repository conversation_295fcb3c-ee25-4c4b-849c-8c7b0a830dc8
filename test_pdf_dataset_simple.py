#!/usr/bin/env python3
"""
Simple test to verify pdf_dataset has the right content.
This script searches the raw CSV files to confirm risk reversal and OCO content exists.
"""

import csv
from pathlib import Path

def search_text_nodes(dataset_dir: str = "import/pdf_dataset"):
    """Search text nodes CSV for specific content."""
    
    print("🔍 Searching pdf_dataset text nodes for financial content...")
    print("=" * 70)
    
    text_nodes_file = Path(dataset_dir) / "triples_csv" / "text_nodes__from_json.csv"
    
    if not text_nodes_file.exists():
        print(f"❌ Text nodes file not found: {text_nodes_file}")
        return
    
    # Count total passages
    with open(text_nodes_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        passages = list(reader)
    
    print(f"📊 Total passages in pdf_dataset: {len(passages)}")
    
    # Search for specific terms
    search_terms = {
        "risk reversal": [],
        "OCO": [],
        "One-Cancels-Other": [],
        "Market Maker Cockpit": [],
        "FIX protocol": [],
        "Bank Basket": [],
        "360T": []
    }
    
    for passage in passages:
        text = passage.get('original_text', '').lower()
        for term in search_terms:
            if term.lower() in text:
                search_terms[term].append(passage.get('text_id:ID', 'unknown'))
    
    # Display results
    print("\n📋 Search Results:")
    for term, matches in search_terms.items():
        print(f"\n  '{term}': {len(matches)} passages")
        if matches:
            # Show sample of content
            for passage in passages:
                if passage.get('text_id:ID') in matches[:1]:  # Show first match
                    text = passage.get('original_text', '')
                    # Find term in context
                    idx = text.lower().find(term.lower())
                    if idx >= 0:
                        start = max(0, idx - 100)
                        end = min(len(text), idx + len(term) + 200)
                        context = text[start:end]
                        print(f"    Sample: ...{context}...")
                        break
    
    # Check for specific document types
    print("\n📄 Document Types Found:")
    doc_types = {
        "RFS Market Taker": 0,
        "HTML Auto Dealing": 0,
        "Limits Monitor": 0,
        "SupersonicTrader": 0,
        "Bridge Administration": 0
    }
    
    for passage in passages:
        text = passage.get('original_text', '')
        for doc_type in doc_types:
            if doc_type in text:
                doc_types[doc_type] += 1
    
    for doc_type, count in doc_types.items():
        if count > 0:
            print(f"  - {doc_type}: {count} passages")
    
    # Summary
    print("\n✅ Summary:")
    print(f"  - Total passages: {len(passages)}")
    print(f"  - Passages with 'risk reversal': {len(search_terms['risk reversal'])}")
    print(f"  - Passages with 'OCO': {len(search_terms['OCO'])}")
    print(f"  - Passages with '360T': {len(search_terms['360T'])}")
    
    if len(passages) > 1000:
        print("\n🎉 This is the CORRECT dataset with full financial documentation!")
    else:
        print("\n⚠️  This dataset seems too small. Expected ~1207 passages.")

def compare_datasets():
    """Compare pdf_dataset with 360t_guide_direct_api_v2."""
    
    print("\n📊 Dataset Comparison")
    print("=" * 70)
    
    datasets = [
        ("360t_guide_direct_api_v2", "import/360t_guide_direct_api_v2"),
        ("pdf_dataset", "import/pdf_dataset")
    ]
    
    for name, path in datasets:
        text_file = Path(path) / "triples_csv" / "text_nodes__from_json.csv"
        if text_file.exists():
            with open(text_file, 'r', encoding='utf-8') as f:
                count = sum(1 for _ in f) - 1  # Subtract header
            print(f"  {name}: {count} passages")
        else:
            print(f"  {name}: Not found")
    
    print("\n💡 pdf_dataset should be used for real testing!")

def main():
    """Main function to verify pdf_dataset content."""
    
    print("🎯 Verifying pdf_dataset Content")
    print("=" * 70)
    print("This script confirms that pdf_dataset has the financial content")
    print("that was missing from 360t_guide_direct_api_v2.")
    print()
    
    # Search pdf_dataset
    search_text_nodes("import/pdf_dataset")
    
    # Compare datasets
    compare_datasets()

if __name__ == "__main__":
    main()