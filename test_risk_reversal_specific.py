#!/usr/bin/env python3
"""
Test retrieval with specific Risk Reversal query after embedding upgrade.
"""

import os
import sys
import pickle
from pathlib import Path

# Set environment and add path
os.environ["TOKENIZERS_PARALLELISM"] = "false"
sys.path.insert(0, str(Path(__file__).parent))

from atlas_rag.retriever.hipporag2 import HippoRAG2Retriever
from atlas_rag.retriever.inference_config import InferenceConfig
from setup_embedding_model import setup_embedding_model

def main():
    print("🎯 Testing Risk Reversal Query with Upgraded Embeddings")
    print("📊 Using all-mpnet-base-v2 (768-dim) embeddings")
    
    # Load data
    data_file = Path("import/pdf_dataset/complete_data.pkl")
    with open(data_file, 'rb') as f:
        complete_data = pickle.load(f)
    
    # Setup embedding model
    embedding_model = setup_embedding_model()
    
    # Enhanced inference config for better financial content discovery
    inference_config = InferenceConfig()
    inference_config.topk_edges = 100  # Get more candidates
    inference_config.simple_mode = True  # Use simple mode to avoid LLM filtering
    
    # Initialize HippoRAG2
    retriever = HippoRAG2Retriever(
        llm_generator=None,  # No LLM filtering
        sentence_encoder=embedding_model,
        data=complete_data,
        inference_config=inference_config
    )
    
    # Test multiple risk reversal queries
    queries = [
        "FX Risk Reversal strategy option",
        "Example 4 Risk Reversal Strategy",
        "pricing of a risk reversal FX option",
        "Figure 24 Pricing risk reversal",
        "Zero Cost Strategy FX option",
        "SEF Risk Reversal option pricing"
    ]
    
    for query in queries:
        print(f"\n{'='*60}")
        print(f"🔍 Query: {query}")
        print(f"{'='*60}")
        
        try:
            passages, passage_ids = retriever.retrieve(query)
            print(f"📊 Retrieved {len(passages)} results")
            
            # Check if we find Risk Reversal content
            found_risk_reversal = False
            for i, (content, passage_id) in enumerate(zip(passages[:5], passage_ids[:5]), 1):
                if any(term in content.lower() for term in ['risk reversal', 'risk-reversal', 'zero cost', 'strategy option']):
                    found_risk_reversal = True
                    print(f"\n✅ MATCH FOUND - Result #{i}:")
                    print(f"   📄 Content: {content[:300]}...")
                    print(f"   🆔 ID: {passage_id}")
                    break
                else:
                    print(f"\n❌ Result #{i} (No match):")
                    print(f"   📄 Content: {content[:200]}...")
            
            if not found_risk_reversal:
                print(f"\n⚠️  No Risk Reversal content found in top 5 results")
            
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print(f"\n{'='*60}")
    print("🎯 SUMMARY:")
    print("✅ Dataset contains Risk Reversal content (confirmed by manual search)")
    print("📊 Using upgraded all-mpnet-base-v2 embeddings (768-dim)")
    print("🔍 Testing multiple query variations")
    print("💡 Results show if embedding upgrade improved financial domain matching")

if __name__ == "__main__":
    main()