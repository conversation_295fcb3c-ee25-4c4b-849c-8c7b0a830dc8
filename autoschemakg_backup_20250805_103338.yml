name: base
channels:
  - conda-forge
  - defaults
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - _anaconda_depends=2024.10=py312_openblas_0
  - aiobotocore=2.12.3=py312hca03da5_0
  - aioitertools=0.7.1=pyhd3eb1b0_0
  - aiosignal=1.2.0=pyhd3eb1b0_0
  - alabaster=0.7.16=py312hca03da5_0
  - altair=5.0.1=py312hca03da5_0
  - anaconda-anon-usage=0.4.4=py312hd6b623d_100
  - anaconda-catalogs=0.2.0=py312hca03da5_1
  - anaconda-client=1.12.3=py312hca03da5_0
  - anaconda-cloud-auth=0.5.1=py312hca03da5_0
  - anaconda-navigator=2.6.3=py312hca03da5_0
  - anaconda-project=0.11.1=py312hca03da5_0
  - aom=3.6.0=h313beb8_0
  - appdirs=1.4.4=pyhd3eb1b0_0
  - applaunchservices=0.3.0=py312hca03da5_0
  - appnope=0.1.3=py312hca03da5_1001
  - appscript=1.2.5=py312h80987f9_0
  - archspec=0.2.3=pyhd3eb1b0_0
  - argon2-cffi=21.3.0=pyhd3eb1b0_0
  - argon2-cffi-bindings=21.2.0=py312h80987f9_0
  - arrow=1.2.3=py312hca03da5_1
  - arrow-cpp=16.1.0=hbc20fb2_0
  - astroid=2.14.2=py312hca03da5_0
  - astropy=6.1.3=py312h80987f9_0
  - astropy-iers-data=0.2024.********.23=py312hca03da5_0
  - asttokens=2.0.5=pyhd3eb1b0_0
  - async-lru=2.0.4=py312hca03da5_0
  - atomicwrites=1.4.0=py_0
  - attrs=23.1.0=py312hca03da5_0
  - automat=20.2.0=py_0
  - autopep8=2.0.4=pyhd3eb1b0_0
  - aws-c-auth=0.6.19=h80987f9_0
  - aws-c-cal=0.5.20=h80987f9_0
  - aws-c-common=0.8.5=h80987f9_0
  - aws-c-compression=0.2.16=h80987f9_0
  - aws-c-event-stream=0.2.15=h313beb8_0
  - aws-c-http=0.6.25=h80987f9_0
  - aws-c-io=0.13.10=h80987f9_0
  - aws-c-mqtt=0.7.13=h80987f9_0
  - aws-c-s3=0.1.51=h80987f9_0
  - aws-c-sdkutils=0.1.6=h80987f9_0
  - aws-checksums=0.1.13=h80987f9_0
  - aws-crt-cpp=0.18.16=h313beb8_0
  - aws-sdk-cpp=1.10.55=h313beb8_0
  - babel=2.11.0=py312hca03da5_0
  - binaryornot=0.4.4=pyhd3eb1b0_1
  - black=24.8.0=py312hca03da5_0
  - blas=1.0=openblas
  - bleach=4.1.0=pyhd3eb1b0_0
  - blosc=1.21.3=h313beb8_0
  - bokeh=3.6.0=py312hca03da5_0
  - boltons=23.0.0=py312hca03da5_0
  - boost-cpp=1.82.0=h48ca7d4_2
  - botocore=1.34.69=py312hca03da5_0
  - brotli=1.0.9=h80987f9_8
  - brotli-bin=1.0.9=h80987f9_8
  - brotli-python=1.0.9=py312h313beb8_8
  - brunsli=0.1=hc377ac9_1
  - bzip2=1.0.8=h80987f9_6
  - c-ares=1.19.1=h80987f9_0
  - c-blosc2=2.12.0=h7df6c2f_0
  - ca-certificates=2025.2.25=hca03da5_0
  - catalogue=2.0.10=py312hca03da5_0
  - cctools=949.0.1=hc179dcd_25
  - cctools_osx-arm64=949.0.1=h332cad3_25
  - certifi=2025.1.31=pyhd8ed1ab_0
  - cffi=1.17.1=py312h3eb5a62_0
  - cfitsio=3.470=h7f6438f_7
  - charls=2.2.0=hc377ac9_0
  - cloudpickle=3.0.0=py312hca03da5_0
  - colorama=0.4.6=py312hca03da5_0
  - colorcet=3.1.0=py312hca03da5_0
  - comm=0.2.1=py312hca03da5_0
  - conda=25.3.1=py312h81bd7bf_0
  - conda-build=24.9.0=py312hca03da5_0
  - conda-content-trust=0.2.0=py312hca03da5_1
  - conda-index=0.5.0=py312hca03da5_0
  - conda-libmamba-solver=24.9.0=pyhd3eb1b0_0
  - conda-pack=0.7.1=py312hca03da5_0
  - conda-package-handling=2.3.0=py312hca03da5_0
  - conda-package-streaming=0.10.0=py312hca03da5_0
  - conda-repo-cli=1.0.114=py312hca03da5_0
  - conda-token=0.5.0=pyhd3eb1b0_0
  - constantly=23.10.4=py312hca03da5_0
  - cookiecutter=2.6.0=py312hca03da5_0
  - coverage=7.6.9=py312h80987f9_0
  - cssselect=1.2.0=py312hca03da5_0
  - curl=8.9.1=h02f6b3c_0
  - cyrus-sasl=2.1.28=h9131b1a_1
  - cytoolz=0.12.2=py312h80987f9_0
  - dask=2024.8.2=py312hca03da5_0
  - dask-core=2024.8.2=py312hca03da5_0
  - dask-expr=1.1.13=py312hca03da5_0
  - datashader=0.16.3=py312hca03da5_0
  - dav1d=1.2.1=h80987f9_0
  - debugpy=1.6.7=py312h313beb8_0
  - defusedxml=0.7.1=pyhd3eb1b0_0
  - diff-match-patch=20200713=pyhd3eb1b0_0
  - dill=0.3.8=py312hca03da5_0
  - distributed=2024.8.2=py312hca03da5_0
  - distro=1.9.0=py312hca03da5_0
  - dmglib=0.9.5=py312hca03da5_0
  - docstring-to-markdown=0.11=py312hca03da5_0
  - docutils=0.18.1=py312hca03da5_3
  - executing=0.8.3=pyhd3eb1b0_0
  - expat=2.6.3=h313beb8_0
  - flake8=7.0.0=py312hca03da5_0
  - fmt=9.1.0=h48ca7d4_1
  - freetype=2.12.1=h1192e45_0
  - frozendict=2.4.2=py312hca03da5_0
  - frozenlist=1.4.0=py312h80987f9_0
  - gensim=4.3.3=py312hd77ebd4_0
  - gettext=0.21.0=hbdbcc25_2
  - gflags=2.2.2=h313beb8_1
  - giflib=5.2.1=h80987f9_3
  - gitdb=4.0.7=pyhd3eb1b0_0
  - gitpython=3.1.43=py312hca03da5_0
  - glib=2.78.4=h313beb8_0
  - glib-tools=2.78.4=h313beb8_0
  - glog=0.5.0=h313beb8_1
  - greenlet=3.0.1=py312h313beb8_0
  - gst-plugins-base=1.14.1=h313beb8_1
  - gstreamer=1.14.1=h80987f9_1
  - h11=0.14.0=py312hca03da5_0
  - hdf5=1.12.1=h05c076b_3
  - heapdict=1.0.1=pyhd3eb1b0_0
  - holoviews=1.19.1=py312hca03da5_0
  - hvplot=0.11.0=py312hca03da5_0
  - hyperlink=21.0.0=pyhd3eb1b0_0
  - icu=73.1=h313beb8_0
  - imagecodecs=2023.1.23=py312h75b721f_1
  - imageio=2.33.1=py312hca03da5_0
  - imagesize=1.4.1=py312hca03da5_0
  - imbalanced-learn=0.12.3=py312hca03da5_1
  - importlib-metadata=7.0.1=py312hca03da5_0
  - incremental=22.10.0=pyhd3eb1b0_0
  - inflection=0.5.1=py312hca03da5_1
  - iniconfig=1.1.1=pyhd3eb1b0_0
  - intake=2.0.7=py312hca03da5_0
  - intervaltree=3.1.0=pyhd3eb1b0_0
  - ipykernel=6.28.0=py312hca03da5_0
  - ipython=8.27.0=py312hca03da5_0
  - ipython_genutils=0.2.0=pyhd3eb1b0_1
  - ipywidgets=7.8.1=py312hca03da5_0
  - isort=5.13.2=py312hca03da5_0
  - itemadapter=0.3.0=pyhd3eb1b0_0
  - itemloaders=1.1.0=py312hca03da5_0
  - itsdangerous=2.2.0=py312hca03da5_0
  - jaraco.classes=3.2.1=pyhd3eb1b0_0
  - jedi=0.19.1=py312hca03da5_0
  - jellyfish=1.0.1=py312h15d1925_0
  - jmespath=1.0.1=py312hca03da5_0
  - joblib=1.4.2=py312hca03da5_0
  - jpeg=9e=h80987f9_3
  - jq=1.6=h1a28f6b_1
  - json5=0.9.6=pyhd3eb1b0_0
  - jsonpatch=1.33=py312hca03da5_1
  - jsonpointer=2.1=pyhd3eb1b0_0
  - jsonschema=4.23.0=py312hca03da5_0
  - jsonschema-specifications=2023.7.1=py312hca03da5_0
  - jupyter=1.0.0=py312hca03da5_9
  - jupyter-lsp=2.2.0=py312hca03da5_0
  - jupyter_client=8.6.0=py312hca03da5_0
  - jupyter_console=6.6.3=py312hca03da5_1
  - jupyter_core=5.7.2=py312hca03da5_0
  - jupyter_events=0.10.0=py312hca03da5_0
  - jupyter_server=2.14.1=py312hca03da5_0
  - jupyter_server_terminals=0.4.4=py312hca03da5_1
  - jupyterlab=4.2.5=py312hca03da5_0
  - jupyterlab-variableinspector=3.1.0=py312hca03da5_0
  - jupyterlab_pygments=0.1.2=py_0
  - jupyterlab_server=2.27.3=py312hca03da5_0
  - jupyterlab_widgets=1.0.0=pyhd3eb1b0_1
  - jxrlib=1.1=h1a28f6b_2
  - keyring=24.3.1=py312hca03da5_0
  - krb5=1.20.1=hf3e1bf2_1
  - lazy-object-proxy=1.10.0=py312h80987f9_0
  - lazy_loader=0.4=py312hca03da5_0
  - lcms2=2.12=hba8e193_0
  - ld64=530=hb29bf3f_25
  - ld64_osx-arm64=530=h001ce53_25
  - ldid=2.1.5=h20b2a84_3
  - lerc=3.0=hc377ac9_0
  - libabseil=20240116.2=cxx17_h313beb8_0
  - libaec=1.0.4=hc377ac9_1
  - libarchive=3.7.4=h8f13d7a_0
  - libavif=0.11.1=h80987f9_0
  - libboost=1.82.0=h0bc93f9_2
  - libbrotlicommon=1.0.9=h80987f9_8
  - libbrotlidec=1.0.9=h80987f9_8
  - libbrotlienc=1.0.9=h80987f9_8
  - libclang=14.0.6=default_h1b80db6_1
  - libclang13=14.0.6=default_h24352ff_1
  - libcurl=8.9.1=h3e2b118_0
  - libcxx=14.0.6=h848a8c0_0
  - libdeflate=1.17=h80987f9_1
  - libedit=3.1.20230828=h80987f9_0
  - libev=4.33=h1a28f6b_1
  - libevent=2.1.12=h02f6b3c_1
  - libexpat=2.6.3=hf9b8971_0
  - libffi=3.4.4=hca03da5_1
  - libgfortran=5.0.0=11_3_0_hca03da5_28
  - libgfortran5=11.3.0=h009349e_28
  - libglib=2.78.4=h0a96307_0
  - libgrpc=1.62.2=h62f6fdd_0
  - libiconv=1.16=h80987f9_3
  - liblief=0.12.3=h313beb8_0
  - libllvm14=14.0.6=h19fdd8a_4
  - libmamba=1.5.8=haeffa04_3
  - libmambapy=1.5.8=py312h1c5506f_3
  - libnghttp2=1.57.0=h62f6fdd_0
  - libopenblas=0.3.21=h269037a_0
  - libpng=1.6.39=h80987f9_0
  - libpq=12.17=h02f6b3c_0
  - libprotobuf=4.25.3=h514c7bf_0
  - libsodium=1.0.18=h1a28f6b_0
  - libsolv=0.7.24=h514c7bf_1
  - libspatialindex=1.9.3=hc377ac9_0
  - libsqlite=3.46.0=hfb93653_0
  - libssh2=1.11.0=h3e2b118_0
  - libthrift=0.15.0=h73c2103_2
  - libtiff=4.5.1=h313beb8_0
  - libwebp-base=1.3.2=h80987f9_0
  - libxml2=2.13.1=h0b34f26_2
  - libxslt=1.1.41=hf4d3faa_0
  - libzlib=1.2.13=hfb2fe0b_6
  - libzopfli=1.0.3=hc377ac9_0
  - linkify-it-py=2.0.0=py312hca03da5_0
  - llvm-openmp=14.0.6=hc6e5704_0
  - llvmlite=0.43.0=py312h313beb8_0
  - locket=1.0.0=py312hca03da5_0
  - lz4=4.3.2=py312h80987f9_0
  - lz4-c=1.9.4=h313beb8_1
  - lzo=2.10=h1a28f6b_2
  - matplotlib-inline=0.1.6=py312hca03da5_0
  - mccabe=0.7.0=pyhd3eb1b0_0
  - mdit-py-plugins=0.3.0=py312hca03da5_0
  - menuinst=2.1.2=py312hca03da5_0
  - mistune=2.0.4=py312hca03da5_0
  - more-itertools=10.3.0=py312hca03da5_0
  - mpmath=1.3.0=py312hca03da5_0
  - msgpack-python=1.0.3=py312h48ca7d4_0
  - multidict=6.0.4=py312h80987f9_0
  - multipledispatch=0.6.0=py312hca03da5_0
  - murmurhash=1.0.12=py312h313beb8_0
  - mypy=1.11.2=py312h80987f9_0
  - mypy_extensions=1.0.0=py312hca03da5_0
  - mysql=5.7.24=ha71a6ea_2
  - navigator-updater=0.5.1=py312hca03da5_0
  - nbclient=0.8.0=py312hca03da5_0
  - nbconvert=7.16.4=py312hca03da5_0
  - nbformat=5.10.4=py312hca03da5_0
  - ncurses=6.4=h313beb8_0
  - nest-asyncio=1.6.0=py312hca03da5_0
  - nltk=3.9.1=py312hca03da5_0
  - notebook=7.2.2=py312hca03da5_1
  - notebook-shim=0.2.3=py312hca03da5_0
  - numba=0.60.0=py312hd77ebd4_0
  - numpy=1.26.4=py312h7f4fdc5_0
  - numpy-base=1.26.4=py312he047099_0
  - numpydoc=1.7.0=py312hca03da5_0
  - oniguruma=*******=h1a28f6b_0
  - openjpeg=2.5.2=h54b8e55_0
  - openpyxl=3.1.5=py312h80987f9_0
  - openssl=3.4.1=h81ee809_0
  - orc=2.0.1=h937ddfc_0
  - overrides=7.4.0=py312hca03da5_0
  - pandocfilters=1.5.0=pyhd3eb1b0_0
  - panel=1.5.2=py312hca03da5_0
  - param=2.1.1=py312hca03da5_0
  - parsel=1.8.1=py312hca03da5_0
  - parso=0.8.3=pyhd3eb1b0_0
  - partd=1.4.1=py312hca03da5_0
  - patch=2.7.6=h1a28f6b_1001
  - pathspec=0.10.3=py312hca03da5_0
  - patsy=0.5.6=py312hca03da5_0
  - pcre2=10.42=hb066dcc_1
  - pexpect=4.8.0=pyhd3eb1b0_3
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pip=24.2=py312hca03da5_0
  - pkce=1.0.3=py312hca03da5_0
  - pkginfo=1.10.0=py312hca03da5_0
  - plotly=5.24.1=py312h989b03a_0
  - ply=3.11=py312hca03da5_1
  - prometheus_client=0.14.1=py312hca03da5_0
  - prompt-toolkit=3.0.43=py312hca03da5_0
  - prompt_toolkit=3.0.43=hd3eb1b0_0
  - protego=0.1.16=py_0
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - pure_eval=0.2.2=pyhd3eb1b0_0
  - py-cpuinfo=9.0.0=py312hca03da5_0
  - py-lief=0.12.3=py312h313beb8_0
  - pybind11-abi=5=hd3eb1b0_0
  - pycodestyle=2.11.1=py312hca03da5_0
  - pycosat=0.6.6=py312h80987f9_1
  - pyct=0.5.0=py312hca03da5_0
  - pycurl=7.45.3=py312h02f6b3c_0
  - pydeck=0.8.0=py312hca03da5_2
  - pydispatcher=2.0.5=py312hca03da5_3
  - pydocstyle=6.3.0=py312hca03da5_0
  - pyerfa=*******=py312ha86b861_0
  - pyflakes=3.2.0=py312hca03da5_0
  - pylint=2.16.2=py312hca03da5_0
  - pylint-venv=3.0.3=py312hca03da5_0
  - pyls-spyder=0.4.0=pyhd3eb1b0_0
  - pyodbc=5.1.0=py312h313beb8_0
  - pyopenssl=24.2.1=py312hca03da5_0
  - pyqt=5.15.10=py312h313beb8_0
  - pyqt5-sip=12.13.0=py312h80987f9_0
  - pyqtwebengine=5.15.10=py312h313beb8_0
  - pysocks=1.7.1=py312hca03da5_0
  - pytables=3.10.1=py312h905a39b_0
  - pytest-cov=6.0.0=py312hca03da5_0
  - python=3.12.2=hdf0ec26_0_cpython
  - python-dateutil=2.9.0post0=py312hca03da5_2
  - python-fastjsonschema=2.16.2=py312hca03da5_0
  - python-json-logger=2.0.7=py312hca03da5_0
  - python-libarchive-c=5.1=pyhd3eb1b0_0
  - python-lmdb=1.4.1=py312h313beb8_0
  - python-lsp-black=2.0.0=py312hca03da5_0
  - python-lsp-jsonrpc=1.1.2=pyhd3eb1b0_0
  - python-lsp-server=1.10.0=py312hca03da5_0
  - python-slugify=5.0.2=pyhd3eb1b0_0
  - python.app=3=py312h80987f9_1
  - python_abi=3.12=6_cp312
  - pytoolconfig=1.2.6=py312hca03da5_0
  - pyviz_comms=3.0.2=py312hca03da5_0
  - pywavelets=1.7.0=py312h80987f9_0
  - pyzmq=25.1.2=py312h313beb8_0
  - qdarkstyle=3.2.3=pyhd3eb1b0_0
  - qstylizer=0.2.2=py312hca03da5_0
  - qt-main=5.15.2=h0917680_10
  - qt-webengine=5.15.9=h2903aaf_7
  - qtawesome=1.3.1=py312hca03da5_0
  - qtconsole=5.5.1=py312hca03da5_0
  - qtpy=2.4.1=py312hca03da5_0
  - queuelib=1.6.2=py312hca03da5_0
  - re2=2022.04.01=hc377ac9_0
  - readline=8.2=h1a28f6b_0
  - referencing=0.30.2=py312hca03da5_0
  - reproc=14.2.4=h313beb8_2
  - reproc-cpp=14.2.4=h313beb8_2
  - requests=2.32.3=py312hca03da5_0
  - requests-file=1.5.1=pyhd3eb1b0_0
  - requests-toolbelt=1.0.0=py312hca03da5_0
  - rfc3339-validator=0.1.4=py312hca03da5_0
  - rfc3986-validator=0.1.1=py312hca03da5_0
  - rope=1.12.0=py312hca03da5_0
  - rpds-py=0.10.6=py312hf0e4da2_0
  - rtree=1.0.1=py312hca03da5_0
  - ruamel.yaml=0.18.6=py312h80987f9_0
  - ruamel.yaml.clib=0.2.8=py312h80987f9_0
  - ruamel_yaml=0.17.21=py312h80987f9_0
  - s3fs=2024.6.1=py312hca03da5_0
  - scikit-image=0.24.0=py312hd77ebd4_0
  - scrapy=2.11.1=py312hca03da5_0
  - seaborn=0.13.2=py312hca03da5_0
  - semver=3.0.2=py312hca03da5_0
  - send2trash=1.8.2=py312hca03da5_0
  - service_identity=18.1.0=pyhd3eb1b0_1
  - sip=6.7.12=py312h313beb8_0
  - smmap=4.0.0=pyhd3eb1b0_0
  - snappy=1.2.1=h313beb8_0
  - snowballstemmer=2.2.0=pyhd3eb1b0_0
  - sortedcontainers=2.4.0=pyhd3eb1b0_0
  - spacy-legacy=3.0.12=py312hca03da5_0
  - sphinx=7.3.7=py312hca03da5_0
  - sphinxcontrib-applehelp=1.0.2=pyhd3eb1b0_0
  - sphinxcontrib-devhelp=1.0.2=pyhd3eb1b0_0
  - sphinxcontrib-htmlhelp=2.0.0=pyhd3eb1b0_0
  - sphinxcontrib-jsmath=1.0.1=pyhd3eb1b0_0
  - sphinxcontrib-qthelp=1.0.3=pyhd3eb1b0_0
  - sphinxcontrib-serializinghtml=1.1.10=py312hca03da5_0
  - spyder=5.5.1=py312hca03da5_4
  - spyder-kernels=2.5.0=py312hca03da5_0
  - sqlalchemy=2.0.34=py312hbe2cdee_0
  - sqlite=3.45.3=h80987f9_0
  - srsly=2.5.1=py312h313beb8_0
  - stack_data=0.2.0=pyhd3eb1b0_0
  - statsmodels=0.14.2=py312ha86b861_0
  - streamlit=1.37.1=py312hca03da5_0
  - tabulate=0.9.0=py312hca03da5_0
  - tapi=1100.0.11=h8754e6a_1
  - tbb=2021.8.0=h48ca7d4_0
  - tblib=1.7.0=pyhd3eb1b0_0
  - terminado=0.17.1=py312hca03da5_0
  - text-unidecode=1.3=pyhd3eb1b0_0
  - textdistance=4.2.1=pyhd3eb1b0_0
  - threadpoolctl=3.5.0=py312h989b03a_0
  - three-merge=0.1.1=pyhd3eb1b0_0
  - tifffile=2023.4.12=py312hca03da5_0
  - tk=8.6.14=h6ba3021_0
  - tldextract=5.1.2=py312hca03da5_0
  - toml=0.10.2=pyhd3eb1b0_0
  - tomli=2.0.1=py312hca03da5_1
  - tomlkit=0.11.1=py312hca03da5_0
  - toolz=0.12.0=py312hca03da5_0
  - traitlets=5.14.3=py312hca03da5_0
  - truststore=0.8.0=py312hca03da5_0
  - twisted=23.10.0=py312hca03da5_0
  - typing-inspection=0.4.0=pyhd8ed1ab_0
  - uc-micro-py=1.0.1=py312hca03da5_0
  - ujson=5.10.0=py312h313beb8_0
  - unicodedata2=15.1.0=py312h80987f9_0
  - unidecode=1.3.8=py312hca03da5_0
  - unixodbc=2.3.11=h1a28f6b_0
  - utf8proc=2.6.1=h80987f9_1
  - w3lib=2.1.2=py312hca03da5_0
  - watchdog=4.0.1=py312h80987f9_0
  - wcwidth=0.2.5=pyhd3eb1b0_0
  - webencodings=0.5.1=py312hca03da5_2
  - whatthepatch=1.0.2=py312hca03da5_0
  - widgetsnbextension=3.6.6=py312hca03da5_0
  - wurlitzer=3.0.2=py312hca03da5_0
  - xarray=2023.6.0=py312hca03da5_0
  - xlwings=0.32.1=py312hca03da5_0
  - xyzservices=2022.9.0=py312hca03da5_1
  - xz=5.4.6=h80987f9_1
  - yaml=0.2.5=h1a28f6b_0
  - yaml-cpp=0.8.0=h313beb8_1
  - yapf=0.40.2=py312hca03da5_0
  - zeromq=4.3.5=h313beb8_0
  - zfp=1.0.0=h313beb8_0
  - zict=3.0.0=py312hca03da5_0
  - zipp=3.17.0=py312hca03da5_0
  - zlib=1.2.13=hfb2fe0b_6
  - zlib-ng=2.0.7=h80987f9_0
  - zope=1.0=py312hca03da5_1
  - zope.interface=5.4.0=py312h80987f9_0
  - zstandard=0.23.0=py312h1a4646a_0
  - zstd=1.5.6=hfb09047_0
  - pip:
      - accelerate==1.8.1
      - acvl-utils==0.2.5
      - addict==2.4.0
      - aiofiles==24.1.0
      - aiohappyeyeballs==2.6.1
      - aiohttp==3.12.13
      - aiosqlite==0.21.0
      - annotated-types==0.7.0
      - anthropic==0.46.0
      - antlr4-python3-runtime==4.13.2
      - anyio==4.9.0
      - applicationinsights==0.11.10
      - argcomplete==3.5.3
      - argparse==1.4.0
      - ascii-colors==0.5.2
      - asgiref==3.8.1
      - atlas-rag==0.0.4
      - aurelio-sdk==0.0.19
      - av==14.4.0
      - azure-ai-agents==1.0.2
      - azure-ai-inference==1.0.0b9
      - azure-ai-projects==1.0.0b12
      - azure-appconfiguration==1.7.1
      - azure-batch==15.0.0b2
      - azure-cli==2.75.0
      - azure-cli-core==2.75.0
      - azure-cli-telemetry==1.1.0
      - azure-common==1.1.28
      - azure-core==1.35.0
      - azure-cosmos==3.2.0
      - azure-data-tables==12.4.0
      - azure-datalake-store==1.0.1
      - azure-identity==1.23.1
      - azure-keyvault-administration==4.4.0b2
      - azure-keyvault-certificates==4.7.0
      - azure-keyvault-keys==4.11.0b1
      - azure-keyvault-secrets==4.7.0
      - azure-keyvault-securitydomain==1.0.0b1
      - azure-mgmt-advisor==9.0.0
      - azure-mgmt-apimanagement==4.0.0
      - azure-mgmt-appconfiguration==3.1.0
      - azure-mgmt-appcontainers==2.0.0
      - azure-mgmt-applicationinsights==1.0.0
      - azure-mgmt-authorization==4.0.0
      - azure-mgmt-batch==17.3.0
      - azure-mgmt-batchai==7.0.0b1
      - azure-mgmt-billing==6.0.0
      - azure-mgmt-botservice==2.0.0
      - azure-mgmt-cdn==12.0.0
      - azure-mgmt-cognitiveservices==13.5.0
      - azure-mgmt-compute==34.1.0
      - azure-mgmt-containerinstance==10.2.0b1
      - azure-mgmt-containerregistry==14.1.0b1
      - azure-mgmt-containerservice==37.0.0
      - azure-mgmt-core==1.6.0
      - azure-mgmt-cosmosdb==9.8.0
      - azure-mgmt-databoxedge==1.0.0
      - azure-mgmt-datalake-store==1.1.0b1
      - azure-mgmt-datamigration==10.0.0
      - azure-mgmt-dns==8.0.0
      - azure-mgmt-eventgrid==10.2.0b2
      - azure-mgmt-eventhub==10.1.0
      - azure-mgmt-extendedlocation==1.0.0b2
      - azure-mgmt-hdinsight==9.0.0b3
      - azure-mgmt-imagebuilder==1.3.0
      - azure-mgmt-iotcentral==10.0.0b2
      - azure-mgmt-iothub==3.0.0
      - azure-mgmt-iothubprovisioningservices==1.1.0
      - azure-mgmt-keyvault==11.0.0
      - azure-mgmt-loganalytics==13.0.0b4
      - azure-mgmt-managementgroups==1.0.0
      - azure-mgmt-maps==2.0.0
      - azure-mgmt-marketplaceordering==1.1.0
      - azure-mgmt-media==9.0.0
      - azure-mgmt-monitor==7.0.0
      - azure-mgmt-msi==7.0.0
      - azure-mgmt-mysqlflexibleservers==1.0.0b3
      - azure-mgmt-netapp==10.1.0
      - azure-mgmt-policyinsights==1.1.0b4
      - azure-mgmt-postgresqlflexibleservers==1.1.0b2
      - azure-mgmt-privatedns==1.0.0
      - azure-mgmt-rdbms==10.2.0b17
      - azure-mgmt-recoveryservices==3.1.0
      - azure-mgmt-recoveryservicesbackup==9.2.0
      - azure-mgmt-redhatopenshift==1.5.0
      - azure-mgmt-redis==14.5.0
      - azure-mgmt-resource==23.3.0
      - azure-mgmt-search==9.2.0
      - azure-mgmt-security==6.0.0
      - azure-mgmt-servicebus==8.2.1
      - azure-mgmt-servicefabric==2.1.0
      - azure-mgmt-servicefabricmanagedclusters==2.1.0b1
      - azure-mgmt-servicelinker==1.2.0b3
      - azure-mgmt-signalr==2.0.0b2
      - azure-mgmt-sql==4.0.0b21
      - azure-mgmt-sqlvirtualmachine==1.0.0b5
      - azure-mgmt-storage==23.0.0
      - azure-mgmt-synapse==2.1.0b5
      - azure-mgmt-trafficmanager==1.0.0
      - azure-mgmt-web==7.3.1
      - azure-monitor-query==1.2.0
      - azure-multiapi-storage==1.4.1
      - azure-storage-blob==12.26.0
      - azure-storage-common==1.4.2
      - azure-synapse-accesscontrol==0.5.0
      - azure-synapse-artifacts==0.20.0
      - azure-synapse-managedprivateendpoints==0.4.0
      - azure-synapse-spark==0.7.0
      - backoff==2.2.1
      - banks==2.1.2
      - batchgenerators==0.25.1
      - batchgeneratorsv2==0.2.3
      - bcrypt==4.3.0
      - beautifulsoup4==4.13.4
      - blinker==1.9.0
      - blis==1.3.0
      - blosc2==3.3.1
      - bottleneck==1.4.2
      - build==1.2.2.post1
      - cachetools==5.5.2
      - cfgv==3.4.0
      - chardet==5.2.0
      - charset-normalizer==3.4.1
      - chroma-hnswlib==0.7.6
      - chromadb==1.0.12
      - click==8.2.1
      - cloudpathlib==0.21.0
      - cobble==0.1.4
      - coloredlogs==15.0.1
      - colorlog==6.9.0
      - confection==0.1.5
      - configparser==7.2.0
      - connected-components-3d==3.23.0
      - contourpy==1.3.2
      - cryptography==43.0.3
      - cssselect2==0.8.0
      - csvw==3.5.1
      - ctranslate2==4.6.0
      - curated-tokenizers==0.0.9
      - curated-transformers==0.1.1
      - cycler==0.12.1
      - cymem==2.0.11
      - dashscope==1.24.1
      - dataclasses-json==0.6.7
      - datamodel-code-generator==0.26.1
      - datasets==4.0.0
      - decorator==4.4.2
      - deprecated==1.2.18
      - dicom2nifti==2.6.0
      - diffusers==0.33.1
      - dirtyjson==1.0.8
      - diskcache==5.6.3
      - distlib==0.4.0
      - dlinfo==2.0.0
      - dnspython==2.7.0
      - docopt==0.6.2
      - durationpy==0.9
      - dynamic-network-architectures==0.3.1
      - ebooklib==0.18
      - effdet==0.4.1
      - einops==0.8.1
      - email-validator==2.2.0
      - emoji==2.14.1
      - en-core-web-lg==3.8.0
      - en-core-web-sm==3.7.1
      - espeakng-loader==0.2.4
      - et-xmlfile==2.0.0
      - eval-type-backport==0.2.2
      - fabric==3.2.2
      - faiss-cpu==1.11.0.post1
      - faker==37.1.0
      - fastapi==0.115.9
      - faster-whisper==1.1.1
      - fft-conv-pytorch==1.2.0
      - filelock==3.18.0
      - filetype==1.2.0
      - flask==3.1.0
      - flatbuffers==25.2.10
      - fonttools==4.57.0
      - fsspec==2024.12.0
      - ftfy==6.3.1
      - future==1.0.0
      - fvcore==0.1.5.post20221221
      - genson==1.3.0
      - google-ai-generativelanguage==0.6.15
      - google-api-core==2.24.2
      - google-api-python-client==2.174.0
      - google-auth==2.39.0
      - google-auth-httplib2==0.2.0
      - google-cloud-vision==3.10.1
      - google-genai==1.27.0
      - google-generativeai==0.8.5
      - googleapis-common-protos==1.70.0
      - graphdatascience==1.16
      - graphiti-core==0.18.0
      - graphviz==0.20.3
      - griffe==1.7.3
      - grpcio==1.72.0rc1
      - grpcio-status==1.71.2
      - gtts==2.5.4
      - h5py==3.13.0
      - hnswlib==0.8.0
      - html5lib==1.1
      - httpcore==1.0.8
      - httplib2==0.22.0
      - httptools==0.6.4
      - httpx==0.28.1
      - httpx-sse==0.4.0
      - huggingface-hub==0.30.2
      - humanfriendly==10.0
      - identify==2.6.12
      - idna==3.10
      - ijson==3.3.0
      - imagebind==0.1.0
      - imageio-ffmpeg==0.6.0
      - importlib-resources==6.5.2
      - inflect==5.6.2
      - invoke==2.2.0
      - iopath==0.1.10
      - isodate==0.7.2
      - javaproperties==0.5.2
      - jinja2==3.1.6
      - jiter==0.9.0
      - jsf==0.11.2
      - json-repair==0.39.1
      - jsondiff==2.0.0
      - kiwisolver==1.4.8
      - knack==0.11.0
      - kokoro==0.9.4
      - kokoro-onnx==0.4.9
      - kubernetes==32.0.1
      - label-studio-sdk==1.0.12.dev0
      - langchain==0.3.23
      - langchain-chroma==0.2.4
      - langchain-community==0.3.21
      - langchain-core==0.3.65
      - langchain-experimental==0.3.4
      - langchain-google-genai==2.0.10
      - langchain-huggingface==0.3.0
      - langchain-neo4j==0.4.0
      - langchain-ollama==0.3.3
      - langchain-openai==0.3.12
      - langchain-text-splitters==0.3.8
      - langcodes==3.5.0
      - langdetect==1.0.9
      - langsmith==0.3.45
      - language-data==1.3.0
      - language-tags==1.2.0
      - lightrag-hku==1.3.2
      - linecache2==1.0.0
      - litellm==1.72.6.post1
      - llama-cloud==0.1.23
      - llama-cloud-services==0.6.25
      - llama-index-core==0.12.39
      - llama-parse==0.6.25
      - loguru==0.7.3
      - lxml==5.3.2
      - mammoth==1.9.1
      - marisa-trie==1.2.1
      - markdown==3.8
      - markdown-it-py==3.0.0
      - markdown2==2.5.4
      - markdownify==1.1.0
      - marker-pdf==1.8.2
      - markupsafe==3.0.2
      - marshmallow==3.26.1
      - matplotlib==3.10.1
      - mdurl==0.1.2
      - microsoft-security-utilities-secret-masker==1.0.0b4
      - misaki==0.9.4
      - mistralai==1.6.0
      - mmh3==5.1.0
      - monotonic==1.6
      - motor==3.7.0
      - moviepy==1.0.3
      - msal==1.33.0b1
      - msal-extensions==1.2.0
      - msrest==0.7.1
      - multimethod==2.0
      - multiprocess==0.70.16
      - nano-vectordb==*******
      - ndindex==1.9.2
      - neo4j==5.28.1
      - neo4j-graphrag==1.6.1
      - networkx==3.4.2
      - nibabel==5.3.2
      - nnunetv2==2.6.0
      - nodeenv==1.9.1
      - num2words==0.5.14
      - numexpr==2.10.2
      - oauthlib==3.2.2
      - olefile==0.47
      - ollama==0.5.1
      - omegaconf==2.3.0
      - onnx==1.17.0
      - onnxruntime==1.21.1
      - openai==1.97.1
      - opencv-python==*********
      - opencv-python-headless==*********
      - opentelemetry-api==1.32.0
      - opentelemetry-exporter-otlp-proto-common==1.32.0
      - opentelemetry-exporter-otlp-proto-grpc==1.32.0
      - opentelemetry-instrumentation==0.53b0
      - opentelemetry-instrumentation-asgi==0.53b0
      - opentelemetry-instrumentation-fastapi==0.53b0
      - opentelemetry-proto==1.32.0
      - opentelemetry-sdk==1.32.0
      - opentelemetry-semantic-conventions==0.53b0
      - opentelemetry-util-http==0.53b0
      - orjson==3.10.16
      - packaging==24.2
      - pandas==2.2.3
      - parameterized==0.9.0
      - paramiko==3.5.1
      - pdf2image==1.17.0
      - pdfminer-six==20250416
      - pdftext==0.6.3
      - phonemizer-fork==3.3.2
      - pi-heif==0.22.0
      - pikepdf==9.7.0
      - pillow==11.0.0
      - pipmaster==0.5.4
      - platformdirs==4.3.8
      - pluggy==1.6.0
      - portalocker==2.10.1
      - posthog==3.24.1
      - pre-commit==4.2.0
      - preshed==3.0.9
      - proglog==0.1.12
      - propcache==0.3.2
      - proto-plus==1.26.1
      - protobuf==5.29.5
      - psutil==7.0.0
      - py-deviceid==0.1.1
      - pyarrow==19.0.1
      - pyasn1==0.6.1
      - pyasn1-modules==0.4.2
      - pycocotools==2.0.8
      - pycomposefile==0.0.33
      - pycparser==2.22
      - pydantic==2.11.7
      - pydantic-core==2.33.2
      - pydantic-settings==2.8.1
      - pydicom==3.0.1
      - pydyf==0.11.0
      - pygithub==1.59.1
      - pygments==2.19.1
      - pyjwt==2.10.1
      - pymongo==4.12.0
      - pymupdf==1.25.5
      - pynacl==1.5.0
      - pyobjc==11.1
      - pyobjc-core==11.1
      - pyobjc-framework-accessibility==11.1
      - pyobjc-framework-accounts==11.1
      - pyobjc-framework-addressbook==11.1
      - pyobjc-framework-adservices==11.1
      - pyobjc-framework-adsupport==11.1
      - pyobjc-framework-applescriptkit==11.1
      - pyobjc-framework-applescriptobjc==11.1
      - pyobjc-framework-applicationservices==11.1
      - pyobjc-framework-apptrackingtransparency==11.1
      - pyobjc-framework-audiovideobridging==11.1
      - pyobjc-framework-authenticationservices==11.1
      - pyobjc-framework-automaticassessmentconfiguration==11.1
      - pyobjc-framework-automator==11.1
      - pyobjc-framework-avfoundation==11.1
      - pyobjc-framework-avkit==11.1
      - pyobjc-framework-avrouting==11.1
      - pyobjc-framework-backgroundassets==11.1
      - pyobjc-framework-browserenginekit==11.1
      - pyobjc-framework-businesschat==11.1
      - pyobjc-framework-calendarstore==11.1
      - pyobjc-framework-callkit==11.1
      - pyobjc-framework-carbon==11.1
      - pyobjc-framework-cfnetwork==11.1
      - pyobjc-framework-cinematic==11.1
      - pyobjc-framework-classkit==11.1
      - pyobjc-framework-cloudkit==11.1
      - pyobjc-framework-cocoa==11.1
      - pyobjc-framework-collaboration==11.1
      - pyobjc-framework-colorsync==11.1
      - pyobjc-framework-contacts==11.1
      - pyobjc-framework-contactsui==11.1
      - pyobjc-framework-coreaudio==11.1
      - pyobjc-framework-coreaudiokit==11.1
      - pyobjc-framework-corebluetooth==11.1
      - pyobjc-framework-coredata==11.1
      - pyobjc-framework-corehaptics==11.1
      - pyobjc-framework-corelocation==11.1
      - pyobjc-framework-coremedia==11.1
      - pyobjc-framework-coremediaio==11.1
      - pyobjc-framework-coremidi==11.1
      - pyobjc-framework-coreml==11.1
      - pyobjc-framework-coremotion==11.1
      - pyobjc-framework-coreservices==11.1
      - pyobjc-framework-corespotlight==11.1
      - pyobjc-framework-coretext==11.1
      - pyobjc-framework-corewlan==11.1
      - pyobjc-framework-cryptotokenkit==11.1
      - pyobjc-framework-datadetection==11.1
      - pyobjc-framework-devicecheck==11.1
      - pyobjc-framework-devicediscoveryextension==11.1
      - pyobjc-framework-dictionaryservices==11.1
      - pyobjc-framework-discrecording==11.1
      - pyobjc-framework-discrecordingui==11.1
      - pyobjc-framework-diskarbitration==11.1
      - pyobjc-framework-dvdplayback==11.1
      - pyobjc-framework-eventkit==11.1
      - pyobjc-framework-exceptionhandling==11.1
      - pyobjc-framework-executionpolicy==11.1
      - pyobjc-framework-extensionkit==11.1
      - pyobjc-framework-externalaccessory==11.1
      - pyobjc-framework-fileprovider==11.1
      - pyobjc-framework-fileproviderui==11.1
      - pyobjc-framework-findersync==11.1
      - pyobjc-framework-fsevents==11.1
      - pyobjc-framework-fskit==11.1
      - pyobjc-framework-gamecenter==11.1
      - pyobjc-framework-gamecontroller==11.1
      - pyobjc-framework-gamekit==11.1
      - pyobjc-framework-gameplaykit==11.1
      - pyobjc-framework-healthkit==11.1
      - pyobjc-framework-imagecapturecore==11.1
      - pyobjc-framework-inputmethodkit==11.1
      - pyobjc-framework-installerplugins==11.1
      - pyobjc-framework-instantmessage==11.1
      - pyobjc-framework-intents==11.1
      - pyobjc-framework-intentsui==11.1
      - pyobjc-framework-iobluetooth==11.1
      - pyobjc-framework-iobluetoothui==11.1
      - pyobjc-framework-iosurface==11.1
      - pyobjc-framework-ituneslibrary==11.1
      - pyobjc-framework-kernelmanagement==11.1
      - pyobjc-framework-latentsemanticmapping==11.1
      - pyobjc-framework-launchservices==11.1
      - pyobjc-framework-libdispatch==11.1
      - pyobjc-framework-libxpc==11.1
      - pyobjc-framework-linkpresentation==11.1
      - pyobjc-framework-localauthentication==11.1
      - pyobjc-framework-localauthenticationembeddedui==11.1
      - pyobjc-framework-mailkit==11.1
      - pyobjc-framework-mapkit==11.1
      - pyobjc-framework-mediaaccessibility==11.1
      - pyobjc-framework-mediaextension==11.1
      - pyobjc-framework-medialibrary==11.1
      - pyobjc-framework-mediaplayer==11.1
      - pyobjc-framework-mediatoolbox==11.1
      - pyobjc-framework-metal==11.1
      - pyobjc-framework-metalfx==11.1
      - pyobjc-framework-metalkit==11.1
      - pyobjc-framework-metalperformanceshaders==11.1
      - pyobjc-framework-metalperformanceshadersgraph==11.1
      - pyobjc-framework-metrickit==11.1
      - pyobjc-framework-mlcompute==11.1
      - pyobjc-framework-modelio==11.1
      - pyobjc-framework-multipeerconnectivity==11.1
      - pyobjc-framework-naturallanguage==11.1
      - pyobjc-framework-netfs==11.1
      - pyobjc-framework-network==11.1
      - pyobjc-framework-networkextension==11.1
      - pyobjc-framework-notificationcenter==11.1
      - pyobjc-framework-opendirectory==11.1
      - pyobjc-framework-osakit==11.1
      - pyobjc-framework-oslog==11.1
      - pyobjc-framework-passkit==11.1
      - pyobjc-framework-pencilkit==11.1
      - pyobjc-framework-phase==11.1
      - pyobjc-framework-photos==11.1
      - pyobjc-framework-photosui==11.1
      - pyobjc-framework-preferencepanes==11.1
      - pyobjc-framework-pushkit==11.1
      - pyobjc-framework-quartz==11.1
      - pyobjc-framework-quicklookthumbnailing==11.1
      - pyobjc-framework-replaykit==11.1
      - pyobjc-framework-safariservices==11.1
      - pyobjc-framework-safetykit==11.1
      - pyobjc-framework-scenekit==11.1
      - pyobjc-framework-screencapturekit==11.1
      - pyobjc-framework-screensaver==11.1
      - pyobjc-framework-screentime==11.1
      - pyobjc-framework-scriptingbridge==11.1
      - pyobjc-framework-searchkit==11.1
      - pyobjc-framework-security==11.1
      - pyobjc-framework-securityfoundation==11.1
      - pyobjc-framework-securityinterface==11.1
      - pyobjc-framework-securityui==11.1
      - pyobjc-framework-sensitivecontentanalysis==11.1
      - pyobjc-framework-servicemanagement==11.1
      - pyobjc-framework-sharedwithyou==11.1
      - pyobjc-framework-sharedwithyoucore==11.1
      - pyobjc-framework-shazamkit==11.1
      - pyobjc-framework-social==11.1
      - pyobjc-framework-soundanalysis==11.1
      - pyobjc-framework-speech==11.1
      - pyobjc-framework-spritekit==11.1
      - pyobjc-framework-storekit==11.1
      - pyobjc-framework-symbols==11.1
      - pyobjc-framework-syncservices==11.1
      - pyobjc-framework-systemconfiguration==11.1
      - pyobjc-framework-systemextensions==11.1
      - pyobjc-framework-threadnetwork==11.1
      - pyobjc-framework-uniformtypeidentifiers==11.1
      - pyobjc-framework-usernotifications==11.1
      - pyobjc-framework-usernotificationsui==11.1
      - pyobjc-framework-videosubscriberaccount==11.1
      - pyobjc-framework-videotoolbox==11.1
      - pyobjc-framework-virtualization==11.1
      - pyobjc-framework-vision==11.1
      - pyobjc-framework-webkit==11.1
      - pypandoc==1.15
      - pyparsing==3.2.3
      - pypdf==5.4.0
      - pypdf2==3.0.1
      - pypdfium2==4.30.0
      - pyphen==0.17.2
      - pypika==0.48.9
      - pyproject-hooks==1.2.0
      - pytesseract==0.3.13
      - pytest==8.4.1
      - pytest-asyncio==1.1.0
      - python-docx==1.1.2
      - python-dotenv==1.1.0
      - python-gdcm==********
      - python-iso639==2025.2.18
      - python-magic==0.4.27
      - python-multipart==0.0.20
      - python-oxmsg==0.0.2
      - python-pptx==1.0.2
      - pytorchvideo==0.1.5
      - pyttsx3==2.98
      - pytz==2025.2
      - pyyaml==6.0.2
      - rapidfuzz==3.13.0
      - rdflib==7.1.4
      - regex==2024.11.6
      - requests-mock==1.12.1
      - requests-oauthlib==2.0.0
      - rfc3986==1.5.0
      - rich==14.0.0
      - rsa==4.9.1
      - rstr==3.2.2
      - safetensors==0.5.3
      - scikit-learn==1.6.1
      - scipy==1.15.2
      - scp==0.13.6
      - segments==2.3.0
      - semantic-chunkers==0.1.1
      - semantic-router==0.1.8
      - sentence-transformers==4.1.0
      - setuptools==75.8.0
      - shellingham==1.5.4
      - simpleitk==2.4.1
      - six==1.17.0
      - smart-open==7.1.0
      - sniffio==1.3.1
      - soundfile==0.13.1
      - soupsieve==2.6
      - spacy==3.8.5
      - spacy-alignments==0.9.1
      - spacy-curated-transformers==0.3.1
      - spacy-loggers==1.0.5
      - spacy-lookups-data==1.0.5
      - spacy-transformers==1.3.8
      - sshtunnel==0.1.5
      - starlette==0.45.3
      - structlog==25.4.0
      - surya-ocr==0.14.7
      - sympy==1.13.1
      - tenacity==8.5.0
      - termcolor==3.1.0
      - thinc==8.3.6
      - tiktoken==0.9.0
      - timm==1.0.15
      - tinycss2==1.4.0
      - tinyhtml5==2.0.0
      - tokenizers==0.20.3
      - torch==2.5.0
      - torchaudio==2.6.0
      - torchvision==0.21.0
      - tornado==6.5.1
      - totalsegmentator==2.8.0
      - tqdm==4.66.5
      - traceback2==1.4.0
      - transformers==4.45.0
      - typer==0.15.2
      - types-pyyaml==6.0.12.20250402
      - typing-extensions==4.13.2
      - typing-inspect==0.9.0
      - tzdata==2025.2
      - unittest2==1.1.0
      - unstructured==0.17.2
      - unstructured-client==0.33.0
      - unstructured-inference==0.8.10
      - unstructured-pytesseract==0.3.15
      - uritemplate==4.2.0
      - urllib3==1.26.20
      - uv==0.8.0
      - uvicorn==0.34.1
      - uvloop==0.21.0
      - virtualenv==20.32.0
      - wasabi==1.1.3
      - watchfiles==1.0.5
      - weasel==0.4.1
      - weasyprint==63.1
      - websocket-client==1.3.3
      - websockets==15.0.1
      - werkzeug==3.1.3
      - wheel==0.45.1
      - wrapt==1.17.2
      - xlrd==2.0.1
      - xlsxwriter==3.2.3
      - xmljson==0.2.1
      - xmltodict==0.14.2
      - xvfbwrapper==0.2.10
      - xxhash==3.5.0
      - yacs==0.1.8
      - yarl==1.20.1
      - zopfli==0.2.3.post1
prefix: /opt/anaconda3
