#!/usr/bin/env python3
"""
Test MMC Query After Integration

Tests the specific MMC query that was problematic before integration:
"how to create scenarios in MMC?"

This query should now return relevant MMC documentation instead of
generic FIX protocol content, thanks to improved entity-text connectivity.
"""

import time
from hipporag2_pipeline import HippoRAG2Pipeline

def test_mmc_query_after_integration():
    """Test the problematic MMC query with integrated embeddings."""
    print("🧪 Testing MMC Query After Integration")
    print("="*60)
    
    # Initialize pipeline with integrated embeddings
    print("🚀 Initializing HippoRAG2 Pipeline with integrated embeddings...")
    pipeline = HippoRAG2Pipeline()
    
    # Load data (should now include integrated embeddings)
    print("\n📂 Loading data with integrated embeddings...")
    data = pipeline.load_existing_data()
    
    # Setup models
    print("\n🤖 Setting up models...")
    pipeline.setup_models()
    
    # Store data for retriever use
    pipeline.data = data
    
    # Initialize HippoRAG2
    print("\n🧠 Initializing HippoRAG2 retriever...")
    pipeline.initialize_hipporag2()
    
    print("\n" + "="*60)
    print("🎯 TESTING PROBLEMATIC MMC QUERY")
    print("="*60)
    
    # Test the specific problematic query
    mmc_query = "how to create scenarios in MMC?"
    print(f"🔍 Query: {mmc_query}")
    print("\n⏱️  Executing retrieval...")
    
    start_time = time.time()
    
    try:
        # Use the HippoRAG2 retriever for the query
        retrieval_results = pipeline.hipporag2_retriever.retrieve(mmc_query)
        
        retrieval_time = time.time() - start_time
        print(f"⏱️  Retrieval completed in {retrieval_time:.2f} seconds")
        
        print(f"\n📊 RETRIEVAL RESULTS:")
        print(f"   Retrieved passages: {len(retrieval_results)}")
        
        # Analyze results for MMC relevance
        mmc_relevant_count = 0
        fix_protocol_count = 0
        
        print(f"\n📋 TOP RETRIEVAL RESULTS:")
        for i, result in enumerate(retrieval_results[:5]):  # Show top 5
            print(f"\n   Result {i+1}:")
            
            # Handle different result formats
            if isinstance(result, dict):
                score = result.get('score', 'N/A')
                content = result.get('content', result.get('text', 'No content'))
            elif isinstance(result, list) and len(result) >= 2:
                # Format might be [score, content] or [node_id, content]
                score = result[0] if isinstance(result[0], (int, float)) else 'N/A'
                content = result[1] if len(result) > 1 else str(result)
            else:
                score = 'N/A'
                content = str(result)
            
            print(f"   Score: {score}")
            content_preview = content[:200] + "..." if len(content) > 200 else content
            print(f"   Content: {content_preview}")
            
            # Check for MMC vs FIX protocol relevance
            content_lower = str(content).lower()
            if any(term in content_lower for term in ['mmc', 'market maker cockpit', 'cockpit']):
                mmc_relevant_count += 1
                print(f"   ✅ MMC-relevant content detected")
            elif any(term in content_lower for term in ['fix protocol', 'fix message', 'fix api']):
                fix_protocol_count += 1  
                print(f"   ⚠️  FIX protocol content detected")
            else:
                print(f"   ❓ Other content type")
        
        # Summary analysis
        print(f"\n📈 RELEVANCE ANALYSIS:")
        print(f"   MMC-relevant results: {mmc_relevant_count}/{len(retrieval_results[:5])}")
        print(f"   FIX protocol results: {fix_protocol_count}/{len(retrieval_results[:5])}")
        
        if mmc_relevant_count > fix_protocol_count:
            print(f"   🎉 SUCCESS: MMC-relevant content now dominates results!")
        elif mmc_relevant_count > 0:
            print(f"   ✅ IMPROVEMENT: MMC-relevant content now appears in results")
        else:
            print(f"   ❌ ISSUE: Still no MMC-relevant content in top results")
        
        return mmc_relevant_count > 0
        
    except Exception as e:
        print(f"❌ Query execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_entity_connectivity():
    """Test if MMC entities are now properly connected to text passages."""
    print(f"\n🔗 TESTING ENTITY-TEXT CONNECTIVITY")
    print("="*40)
    
    pipeline = HippoRAG2Pipeline()
    data = pipeline.load_existing_data()
    
    # Find MMC-related entities in the graph
    mmc_entities = []
    for node_id in data["KG"].nodes():
        node_data = data["KG"].nodes[node_id]
        node_name = str(node_data.get('name', '')).lower()
        if any(term in node_name for term in ['mmc', 'market maker cockpit', 'cockpit']):
            mmc_entities.append((node_id, node_data.get('name', node_id)))
    
    print(f"🔍 Found {len(mmc_entities)} MMC-related entities")
    
    # Check connectivity to text passages
    connected_entities = 0
    for entity_id, entity_name in mmc_entities[:5]:  # Check first 5
        # Find edges from this entity to text/passage nodes
        entity_connections = []
        for edge in data["edge_list"]:  # Now using the expanded edge list!
            source, target = edge
            if source == entity_id:
                target_data = data["KG"].nodes.get(target, {})
                target_type = target_data.get('type', 'unknown')
                if target_type in ['passage', 'text']:
                    entity_connections.append((target, target_data.get('name', target)))
        
        print(f"\n   Entity: {entity_name}")
        print(f"   Connections to text: {len(entity_connections)}")
        
        if entity_connections:
            connected_entities += 1
            print(f"   ✅ Connected to passages")
            # Show first few connections
            for conn_id, conn_name in entity_connections[:2]:
                conn_preview = str(conn_name)[:100] + "..." if len(str(conn_name)) > 100 else str(conn_name)
                print(f"      → {conn_preview}")
        else:
            print(f"   ❌ No passage connections found")
    
    print(f"\n📊 CONNECTIVITY SUMMARY:")
    print(f"   MMC entities with text connections: {connected_entities}/{min(len(mmc_entities), 5)}")
    
    return connected_entities > 0

def main():
    """Main test function."""
    print("🧪 MMC QUERY INTEGRATION TEST")
    print("=" * 80)
    
    try:
        # Test entity connectivity first
        connectivity_success = test_entity_connectivity()
        
        # Test actual query performance
        query_success = test_mmc_query_after_integration()
        
        print(f"\n" + "="*80)
        print(f"🏁 TEST SUMMARY")
        print(f"="*80)
        print(f"   Entity-text connectivity: {'✅ PASS' if connectivity_success else '❌ FAIL'}")
        print(f"   MMC query relevance: {'✅ PASS' if query_success else '❌ FAIL'}")
        
        if connectivity_success and query_success:
            print(f"\n🎉 INTEGRATION SUCCESS!")
            print(f"   MMC queries now return relevant MMC documentation")
            print(f"   Entity-text connectivity has been restored")
        elif connectivity_success:
            print(f"\n✅ PARTIAL SUCCESS")
            print(f"   Entity connectivity improved, but query results need refinement")
        else:
            print(f"\n❌ INTEGRATION INCOMPLETE")
            print(f"   Further investigation needed")
        
        return connectivity_success and query_success
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)